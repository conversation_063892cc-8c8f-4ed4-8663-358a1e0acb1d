// export const CheckCircleFill = `<svg
//  width="1em"
//  height="1em"
//  viewBox="0 0 48 48"
//  xmlns="http://www.w3.org/2000/svg"
//  xmlns:xlink="http://www.w3.org/1999/xlink"
//  font-size="22"
// >
//  <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
//      <g>
//          <rect fill="#FFFFFF" opacity="0" x="0" y="0" width="48" height="48"></rect>
//          <path
//              d="M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M35.8202936,17 L32.7086692,17 C32.6025922,17 32.500859,17.0421352 32.4258461,17.1171378 L32.4258461,17.1171378 L21.3922352,28.1492247 L16.3591562,23.1163755 C16.2841422,23.0413649 16.1824034,22.9992247 16.0763199,22.9992247 L16.0763199,22.9992247 L12.9653996,22.9992247 C12.859342,22.9992247 12.7576259,23.0413445 12.6826161,23.1163228 C12.5263737,23.2724998 12.5263207,23.5257658 12.6824977,23.6820082 C12.8583452,23.8579294 13.0341927,24.0338505 13.2100402,24.2097716 C13.2577488,24.2575002 13.3065097,24.3063074 13.3562592,24.3561283 L13.6661084,24.6666997 C14.3074913,25.3100963 15.0728595,26.0807873 15.8520136,26.8666654 L16.4372421,27.4571699 C18.2552812,29.2922548 19.9983838,31.0574343 20.2666114,31.3285298 L20.301004,31.3632341 C20.8867904,31.9490205 21.8365379,31.9490205 22.4223243,31.3632341 L22.4223243,31.3632341 L36.1031319,17.6828471 C36.1781492,17.6078322 36.2202936,17.5060887 36.2202936,17.4 C36.2202936,17.1790861 36.0412075,17 35.8202936,17 L35.8202936,17 Z"
//              fill="currentColor"
//              fill-rule="nonzero"
//          ></path>
//      </g>
//  </g>
// </svg>`;
export const CheckCircleFill = `<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<title>选中</title>
<g id="浩瀚有借H5流程" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="匹配推荐--通用版多个" transform="translate(-32, -293)">
        <g id="1" transform="translate(16, 263)">
            <g id="选中" transform="translate(16, 30)">
                <circle id="椭圆形" fill="#3C56E4" cx="10" cy="10" r="10"></circle>
                <polyline id="直线" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" transform="translate(9.6953, 9.775) rotate(-45) translate(-9.6953, -9.775)" points="6.69526215 7.77498638 6.69526215 11.7749864 12.6952621 11.7749864"></polyline>
            </g>
        </g>
    </g>
</g>
</svg>`;
export const PhoneFill = `<svg width="1em" height="1em" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" ><g id="PhoneFill-PhoneFill" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g ><rect  fill="#FFFFFF" opacity="0" x="0" y="0" width="48" height="48"></rect><path d="M15.1766716,31.9445426 C8.09652665,24.7479935 4.37345146,18.0644855 4.00844409,11.8899316 L4.0084441,11.8899317 C3.92661639,10.5018215 4.44249252,9.1450022 5.42588939,8.16187843 L8.87520905,4.71264482 L8.875209,4.71264487 C9.82549238,3.76245173 11.3661372,3.76245173 12.3164158,4.71264476 L17.4782284,9.87432857 L17.4782283,9.87432851 C18.4284452,10.8245882 18.4284452,12.3651945 17.4782284,13.3154495 L14.1657865,16.628821 L14.1657866,16.628821 C13.8067263,16.9875805 13.7081153,17.5315453 13.9183928,17.9935105 C15.7748593,22.0642535 17.9030566,25.2995591 20.301982,27.6984247 C22.7008932,30.097276 25.9362605,32.2254155 30.0071383,34.0818547 L30.0071383,34.0818546 C30.4691155,34.2921269 31.01309,34.1935184 31.3718619,33.8344671 L34.6853161,30.5221078 L34.685316,30.5221079 C35.6355994,29.5719147 37.1762442,29.5719147 38.1265228,30.5221078 L43.2873373,35.6837916 L43.2873374,35.6837916 C44.2375542,36.6340513 44.2375542,38.1746576 43.2873373,39.1249125 L39.83903,42.5741462 L39.8390299,42.5741463 C38.8558816,43.5575172 37.4990332,44.0733814 36.1108835,43.9915562 C29.8114647,43.6194609 22.9787453,39.7504807 15.6147216,32.3856094 L15.1766716,31.9445426 Z" fill="currentColor" fill-rule="nonzero"></path></g></g></svg>`;
export const CheckCircleFill2 = `<svg width="128px" height="128px" viewBox="0 0 128 128" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<title>勾</title>
<defs>
    <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
        <stop stop-color="#1FDC8D" offset="0%"></stop>
        <stop stop-color="#18D484" offset="100%"></stop>
    </linearGradient>
    <filter x="-44.1%" y="-44.1%" width="188.2%" height="188.2%" filterUnits="objectBoundingBox" id="filter-2">
        <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
    </filter>
</defs>
<g id="浩瀚H5完善资料页20230323" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="市场投放H5图标" transform="translate(-9.000000, -166.000000)">
        <g id="勾" transform="translate(39.000000, 196.000000)">
            <circle id="椭圆形" fill="url(#linearGradient-1)" filter="url(#filter-2)" cx="34" cy="34" r="34"></circle>
            <circle id="椭圆形" fill="url(#linearGradient-1)" cx="34" cy="34" r="34"></circle>
            <polygon id="路径" fill="#FFFFFF" fill-rule="nonzero" points="51.9860156 24.7280078 29.7179688 45.9860156 16.0139844 32.9379687 18.8680078 30.2 29.7139844 40.5280078 49.1320313 22"></polygon>
        </g>
    </g>
</g>
</svg>`;
