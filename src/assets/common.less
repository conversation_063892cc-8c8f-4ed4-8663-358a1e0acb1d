body {
  font-size: 12px;
  color: #000;
  font-family: PingFangSC-Regular, PingFang SC, '-apple-system', "Helvetica Neue", "Roboto", "Segoe UI", sans-serif;
  // overflow: hidden;
}

input {
  -webkit-appearance: none;
  resize: none
}

input,
textarea {
  font: inherit;
}

body,
div,
ul,
li,
ol,
h1,
h2,
h3,
h4,
h5,
h6,
input,
textarea,
select,
p,
dl,
dt,
dd,
a,
img,
button,
form,
table,
th,
tr,
td,
tbody,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  padding: 0;
  margin: 0
}

h1,
h2,
h3,
h4,
h5 {
  font-style: normal;
  font-weight: normal
}

img {
  border: 0;
  display: block;
  border: 0
}

.clearfix:after {
  content: "";
  display: block;
  visibility: hidden;
  height: 0;
  clear: both
}

.clearfix {
  zoom: 1
}

a {
  text-decoration: none;
  color: #969696
}

a:hover {
  color: #fed503;
  text-decoration: none
}

ul,
ol {
  list-style: none
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}

input:-webkit-autofill {
  box-shadow: 0 0 0px 1000px white inset !important;
}

* {
  box-sizing: border-box;
}

@font-face {
  font-family: 'Din';
  src: url(//p.hzkxgba.com/font/DIN-Medium.otf);
  font-weight: normal;
  font-style: normal;
}


:root {
  ---page-view-width: 100vw;
  ---page-view-height: 100vh;
  ---safe-area-top: 0px;
  ---safe-area-top: constant(safe-area-inset-top); //兼容 IOS<11.2
  ---safe-area-top: env(safe-area-inset-top); //兼容 IOS>11.2
  ---safe-area-bottom: 0px;
  ---safe-area-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  ---safe-area-bottom: env(safe-area-inset-bottom); //兼容 IOS>11.2
  ---page-header-height: calc(44px + var(---safe-area-top));
  ---page-menu-height: calc(44px + var(---safe-area-bottom));


  --color-primary-text: #fff;
  --color-primary-bg: #354bdb;
  --color-primary-border-color: #EE5533;
  --color-primary-border-bg: #adccf63b;
  --color-primary-border-solid: #EE5533;
}


#__nuxt {
  box-sizing: border-box;
  font-family: PingFang SC, Arial, Microsoft YaHei, sans-serif;
  -moz-osx-font-smoothing: grayscale;
  display: flex;
  flex-direction: column;
  background-color: #ffff;
}

.safe-area-padding-top {
  padding-top: var(---safe-area-top)
}

.safe-area-padding-bottom {
  padding-bottom: var(---safe-area-bottom); //兼容 IOS>11.2
}

.icon {
  font-size: 12px;
  height: 1.2em;
  width: 1.2em;
  fill: currentColor;

  svg {
    font-size: 12px;
    height: 1.2em;
    width: 1.2em;
    fill: currentColor;
  }
}

.icon-svg {
  svg {
    font-size: inherit;
    height: 1.2em;
    width: 1.2em;
    fill: currentColor;
  }
}


// 页面page
.layout_page,
.layout_page_child {
  z-index: 1;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  // box-shadow: 0 0 8px 0px #00000063;
}

.layout_page_child {
  flex-direction: row;
  flex-wrap: nowrap;
  // width: 100vw;
  // min-width: 100vw;
  // max-width: 100vw;

  & > .layout_page {
    width: 100vw;
    width: 100vw;
    min-width: 100vw;
    max-width: 100vw;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity .5s ease;
}


.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}


.nut-drag {
  position: fixed;
  display: inline-block;
  z-index: 9997 !important;
  width: fit-content;
  height: fit-content;

  .nut-fixednav {
    position: relative !important;
  }
}

.nut-taro-drag {
  // position: fixed;
  display: inline-block;
  z-index: 9997 !important;
  width: fit-content;
  height: fit-content;
}

// 渐入

.fade-up-enter-active,
.fade-up-leave-active {
  transition: all .35s ease;
}


.fade-up-enter-from,
.fade-up-leave-to {
  opacity: 0;
  transform: translateY(100%);
}


@pageAnimateTime: 300ms;


:root {
  .popup-center.round {
    border-radius: 10px;
  }


  .global-btn {
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    border-radius: 22px;
  }

  .form-row {
    margin: 16px 0;

    .app-input {
      height: 44px;
      background: #F6F6F6;
      border-radius: 4px;
      overflow: hidden;
    }

    .form-icon {
      width: 52px;
      text-align: center;
      height: 20px;
      border-right: 1px solid #D8D8D8;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .form-prefix {
      width: 52px;
      text-align: center;
      height: 20px;
      border-right: 1px solid #D8D8D8;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #848484;
    }
  }

  //vant

  .van-toast {
    width: auto !important;
    max-width: 80%;
  }

  --van-overlay-background-color: rgba(0, 0, 0, 0.2) !important;
}

.clearfix::before, .clearfix::after {
  /* 解决高度塌陷+外边距重叠问题 */
  content: "";
  /* 清楚浮动影响 */
  clear: both;
  /* block可以解决高度塌陷 ，不可以解决外边距重叠 所以要用table */
  display: table;
  /* 使元素不可见*/
  visibility: hidden;
}
