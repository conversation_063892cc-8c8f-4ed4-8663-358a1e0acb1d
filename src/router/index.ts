import { createRouter, createWebHashHistory, createWebHistory } from "vue-router";
import HomeView from "@/pages/Home.vue";
import { sessionStorage, setTitle } from "@/utils/index";
import { useGlobalStore } from "@/stores/global";
import { webHistoryType } from "../../config";
// 需要忽略的上一级路由
const ignoreRouterPath: string[] = [
    "/procedure/recommend",
    "/auth",
    "/procedure/product",
    "/procedure/apics/prodectList",
    "/procedure/result"
];
const router = createRouter({
    history: webHistoryType ? createWebHistory("/h5") : createWebHashHistory(),
    routes: [
        {
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            name: "index",
            component: HomeView,
            meta: {
                anonymous: true,
                title: "纳赫兹"
            }
        },
        {
            path: "/auth",
            name: "auth",
            component: () => import("@/pages/auth/auth.vue"),
            meta: {
                title: "申请资料",
                replace: true
            }
        },
        {
            path: "/procedure/product",
            name: "product",
            component: () => import("@/pages/procedure/Product.vue"),
            meta: {
                title: "产品"
            }
        },
        {
            path: "/procedure/recommend",
            name: "recommend",
            component: () => import("@/pages/procedure/Recommend.vue"),
            meta: {
                title: "匹配产品列表"
            }
        },
        {
            // h5流程匹配的全流程产品
            path: "/procedure/wholeProcessGoods",
            name: "wholeProcessGoods",
            component: () => import("@/pages/procedure/wholeProcessGoods/wholeProcessGoods.vue"),
            meta: {
                title: "匹配产品"
            }
        },
        {
            path: "/procedure/result",
            name: "result",
            component: () => import("@/pages/procedure/Result.vue"),
            meta: {
                title: "匹配结果"
            }
        },
        {
            path: "/procedure/isFlowResult",
            name: "isFlowResult",
            component: () => import("@/pages/procedure/isFlowResult.vue"),
            meta: {
                title: "匹配结果"
            }
        },
        {
            path: "/record",
            name: "record",
            component: () => import("@/pages/Record.vue"),
            meta: {
                title: "我的贷款"
            }
        },
        {
            path: "/aboutUs",
            name: "aboutUs",
            component: () => import("@/pages/AboutUs.vue"),
            meta: {
                title: "关于我们"
            }
        },
        {
            path: "/auxiliary/agreement",
            name: "agreement",
            component: () => import("@/pages/auxiliary/Agreement.vue"),
            meta: {
                title: "用户协议"
            }
        },
        {
            path: "/auxiliary/download",
            name: "download",
            component: () => import("@/pages/auxiliary/Download.vue"),
            meta: {
                title: "下载"
            }
        },
        {
            path: "/procedure/:theme/prodectList",
            name: "prodectList",
            component: () => import("@/pages/authorization/index.vue"),
            meta: {
                title: "授权"
            }
        },
        {
            path: "/authorization/partnerAuth",
            name: "partnerAuth",
            component: () => import("@/pages/authorization/partnerAuth.vue"),
            meta: {
                title: "合作方"
            }
        },
        {
            path: "/auth/gdtxh",
            name: "authb",
            component: () => import("@/pages/customAuth/index.vue"),
            meta: {
                title: "申请资料"
            }
        },
        {
            path: "/information",
            name: "information",
            component: () => import("@/pages/procedure/Information.vue"),
            meta: {
                title: "我的资料"
            }
        },
        {
            path: "/procedure/resultH5",
            name: "resultH5",
            component: () => import("@/pages/procedure/ResultH5.vue"),
            meta: {
                title: "H5匹配结果"
            }
        },
        {
            path: "/procedure/resultWx",
            name: "resultWx",
            component: () => import("@/pages/procedure/ResultWx.vue"),
            meta: {
                title: "微信匹配结果"
            }
        }
        // {
        //     path: "/procedure/recommend2",
        //     name: "recommend2",
        //     component: () => import("@/pages/procedure/Recommend2.vue"),
        //     meta: {
        //         title: "测试专用",
        //     },
        // },
    ]
});
router.beforeEach(async (to, from, next) => {
    const token = sessionStorage.getItem("token");
    const store: any = useGlobalStore();
    const channel = store.channel;
    const route = router.currentRoute.value;
    const query: any = {};

    if (route.query.yxtoken) {
        query.yxtoken = route.query.yxtoken;
    }
    if (channel) {
        query._c = channel;
    }
    // 如果有uid则是授权页不校验登录
    if (!token && !store._u) {
        const anonymous = !!to.meta.anonymous;
        // console.log("未登录", anonymous);
        if (!anonymous) {
            next({ path: "/index", query });
            return;
        }
    }
    // 目标路由不等于当前路由，并且当前路由不等于/，并且目标路径没有渠道号，并且没有uid
    if (to.path !== from.path && from.path !== "/" && !to.query._c && !store._u) {
        to.query = {
            ...to.query,
            ...query
        };
        next(to);
        return;
    }

    // 返回拦截
    // if (from.path != "/") {
    //     if (store.currentRoute == from.path && store.interceptState) {
    //         if (from.path !== to.path && to.path == store.preRouter) {
    //             store.interceptTips = true;
    //             next(false);
    //             return;
    //         }
    //         if (from.path !== to.path && !ignoreRouterPath.includes(from.path)) {
    //             store.preRouter = from.path;
    //         }
    //     }
    // }
    store.currentRoute = to.path;

    setTitle(to?.meta?.title as string);
    next(true);
});

export default router;
