import axios from "axios";

import { appHost } from "../../config";
import { useGlobalStore } from "@/stores/global";

const baseURL = appHost;

const instance = axios.create({
    baseURL,
    // withCredentials: true,
    timeout: 10000
});

//  请求拦截
instance.interceptors.request.use(
    (config: any) => {
        const store: any = useGlobalStore();
        const token = store.token;
        if (!config.ignoreAuth && token) config.headers["authorization"] = "Bearer " + token;

        return config;
    },
    function (error) {
        // 对请求错误做些什么
        return Promise.reject(error);
    }
);
//配置响应拦截
instance.interceptors.response.use(
    async (response): Promise<any> => {
        return Promise.resolve({ data: response.data });
    },
    async ({ response }: any) => {
        if (response?.status === 401) {
            // return Promise.reject('登录状态失效');
            // router.push("/index");
            const store: any = useGlobalStore();
            store.delToken();
            return Promise.resolve({ data: undefined, error: true, message: "登录状态失效" });
        } else {
            const { data = {} } = response;
            return Promise.resolve({
                data: undefined,
                error: true,
                message: data?.error ?? data?.msg ?? data?.message
            });
        }
    }
);
export default instance;
