import axios from "@/apis/axios";
import {useGlobalStore} from "@/stores/global";

/**
 * 头条回传接口
 * @param params
 * @returns
 */
export const backToutiao = (params: any) => {
    return axios.post(`/hx-server/api-server/proxy`, {
        url: "https://analytics.oceanengine.com/api/v2/conversion",
        method: "POST",
        body: params,
        json: true
    });
};

/**
 * 微盟回传接口
 * @param params
 * @returns
 */
export const backWeiMen = (params: any) => {
    return axios.post(`/hx-server/api-server/proxy`, {
        url: "http://tracking.e.qq.com/conv",
        method: "POST",
        body: params,
        json: true
    });
};
/**
 * 自定义回传接口
 * @param params
 * @returns
 */
export const backURl = (method: string, url: any, params = {}) => {
    return axios.post(`/hx-server/api-server/proxy`, {
        url: url,
        method,
        qs: params
        // json: true,
    });
};
/**
 * 大搜索回传接口
 * @param params
 */
export const dasousuoBack = (params: any) => {
    let url = `https://ocpc.baidu.com/ocpcapi/api/uploadConvertData`;
    return axios.post(`/hx-server/api-server/proxy`, {
        url,
        method: "POST",
        body: params,
        json: true
    });
};

/**
 * 判断渠道是否回传
 * @param params
 * @returns
 */
export const returnControl = (type: any) => {
    const { channel, orderId, uid } = useGlobalStore();
    return new Promise((resolve, reject) => {
        axios
            .post(`/hx-server/api-server/returnControl`, {
                type,
                relation_id: orderId || uid, //订单ID
                channel_id: channel //渠道ID
            })
            .then((res) => {
                console.log(res);

                if (res.data.state) {
                    resolve(true);
                } else {
                    reject(false);
                }
            });
    });
};

/**
 * 自定义回传接口
 * @param params
 * @returns
 */
export const youkaBack = () => {
    // const { channel} = useGlobalStore();
    // return axios.post(`/hx-server/api-server/unionLogin/callBack/${channel}`);
};
