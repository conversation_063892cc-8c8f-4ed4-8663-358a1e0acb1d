import axios from "@/apis/axios";

/**
 * 获取产品列表
 * @param params
 * @returns
 */
export const getProdcutList = (params: any) => {
    return axios.get(`/hx-server/api-server/product`, { params });
};
/**
 * 提交产品
 * @param params
 * @returns
 */
export const submitProduct = (id: any, params: any) => {
    params.list = `[${params.list.join(",")}]`;
    return axios.get(`/hx-server/api-server/thirdCommit/${id}/pushNotice`, { params: params });
};

/**
 * 获取协议
 * @param _c 渠道代码
 * @param id 订单id
 * @returns
 */
export const getAgreement = (params: any) => {
    return axios.get(`/hx-server/api-server/agreement`, { params }).then((res) => {
        // res.data = (res?.data ?? []).map((item: any) => {
        //     item.value = item.value.split("com")[1];
        //     return item;
        // });
        return res;
    });
};
