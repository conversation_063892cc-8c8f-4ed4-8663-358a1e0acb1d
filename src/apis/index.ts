import axios from "./axios";
import { useGlobalStore } from "@/stores/global";

export const getAppConfig = (channel: string) => {
    return axios.get(`/hx-server/api-server/index?channel_id=${channel}`);
};
export const getHomeDetail = (channel: string) => {
    return axios.get(`/hx-server/api/home/<USER>
};

export const getCaptcha = (params: any) => {
    const store = useGlobalStore();
    params.channel_id = store.channel;
    return axios.post("/hx-server/api-server/captcha", params);
};
// 登陆
export const fetchLogin = (params: any) => {
    return axios({
        url: `/hx-server/api-server/login?channel_id=${params.channel_id}`,
        method: "post",
        data: params
    });
};

// 获取用户地址
export const getUserInfo = () => {
    return axios.get("/hx-server/api-server/getUserInfo");
};
export const getCertificationConfig = (type: string) => {
    return {
        data: [
            {
                id: 1,
                icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth1.png",
                label: "实名信息",
                placeholder: "填写",
                value: "",
                innerTitle: "实名信息",
                required: true,
                children: [
                    {
                        key: "name",
                        label: "真实姓名",
                        placeholder: "请输入",
                        type: "input",
                        value: "",
                        amount: 10000,
                        rules: [
                            { required: true, message: "姓名不能为空" },
                            { pattern: "^[一-龥]+(·[一-龥]+)*$", message: "姓名格式不符合规范" }
                        ]
                    },
                    {
                        key: "idno",
                        amount: 10000,
                        label: "身份证号",
                        placeholder: "请输入",
                        type: "input",
                        value: "",
                        rules: [
                            { required: true, message: "身份证号不能为空" },
                            {
                                pattern:
                                    "^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|X)$",
                                message: "身份证不符合规范"
                            }
                        ]
                    }
                ],
                key: "certification",
                sort: null
            },
            {
                icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth2.png",
                label: "公积金",
                value: "",
                innerTitle: "",
                formTitle: "您的公积金缴纳时长是？",
                required: true,
                type: "select",
                rules: [{ required: true, message: "请先选择公积金信息" }],
                options: [
                    { label: "6个月以上", amount: 50000, value: "6个月以上" },
                    { label: "6个月以下", amount: 30000, value: "6个月以下" },
                    { label: "无公积金", value: "无公积金" }
                ],
                key: "reserved_funds",
                sort: 70,
                group: {
                    title: "资质信息",
                    sort: 8
                }
            },
            {
                icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth3.png",
                label: "社保",
                innerTitle: "",
                formTitle: "您的社保缴纳时长是？",
                required: true,
                type: "select",
                rules: [{ required: true, message: "请先选择社保信息" }],
                options: [
                    { label: "6个月以上", value: "6个月以上", amount: 50000 },
                    { label: "6个月以下", value: "6个月以下", amount: 30000 },
                    { label: "无社保", value: "无社保" }
                ],
                key: "social_security",
                sort: 72,
                group: {
                    title: "资质信息",
                    sort: 7
                }
            },
            {
                icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth4.png",
                label: "房产",
                innerTitle: "",
                formTitle: "您名下是否有房产？",
                required: true,
                type: "select",
                rules: [{ required: true, message: "请先选择房产信息" }],
                options: [
                    { label: "有房（红本在手）", value: "有房（红本在手）", amount: 100000 },
                    { label: "有房（住房按揭中）", value: "有房（住房按揭中）", amount: 100000 },
                    { label: "有房（抵押中）", value: "有房（抵押中）", amount: 100000 },
                    { label: "无房", value: "无房" }
                ],
                key: "real_estate",
                sort: 66,
                group: {
                    title: "资质信息",
                    sort: 9
                }
            },
            {
                icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth5.png",
                label: "车产",
                innerTitle: "",
                formTitle: "您名下是否有车产？",
                required: true,
                type: "select",
                rules: [{ required: true, message: "请先选择房产信息" }],
                options: [
                    { label: "有车（全款）", value: "有车（全款）", amount: 50000 },
                    { label: "有车（按揭中）", value: "有车（按揭中）", amount: 50000 },
                    { label: "有车（按揭已结清）", value: "有车（按揭已结清）", amount: 50000 },
                    { label: "无车", value: "无车" }
                ],
                key: "car",
                sort: 68,
                group: {
                    title: "资质信息",
                    sort: 10
                }
            },
            {
                icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth6.png",
                label: "芝麻分",
                innerTitle: "",
                formTitle: "芝麻分",
                required: true,
                type: "zhima",
                rules: [
                    {
                        pattern: "^3[5-9][0-9]$|^[4-8][0-9][0-9]$|^9[0-4][0-9]$|^950$|^无芝麻分$",
                        message: "芝麻分不符合规范"
                    }
                ],
                key: "zhima",
                sort: 74,
                group: {
                    title: "资质信息",
                    sort: 11
                }
            },
            {
                icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth7.png",
                label: "保单",
                innerTitle: "",
                formTitle: "您是否有人身相关的保险？",
                required: true,
                type: "select",
                rules: [{ required: true, message: "请先选择投保信息" }],
                options: [
                    { label: "投保两年以上", value: "投保两年以上", amount: 20000 },
                    { label: "投保两年以下", value: "投保两年以下", amount: 10000 },
                    { label: "无", value: "无" }
                ],
                key: "insurance",
                sort: 64,
                group: {
                    title: "资质信息",
                    sort: 13
                }
            },
            {
                icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth8.png",
                label: "职业",
                innerTitle: "",
                formTitle: "您的职业是？",
                required: true,
                type: "select",
                rules: [{ required: true, message: "请先选择职业信息" }],
                options: [
                    { label: "上班族", value: "上班族", amount: 20000 },
                    { label: "公务员/国企/事业单位", value: "公务员/国企/事业单位", amount: 50000 },
                    { label: "企业主", value: "企业主", amount: 50000 },
                    { label: "个体户", value: "个体户", amount: 20000 },
                    { label: "自由职业", value: "自由职业", amount: 20000 },
                    { label: "待业", value: "待业" }
                ],
                key: "industry",
                sort: 92,
                group: {
                    title: "职业信息",
                    sort: 16
                }
            },
            {
                icon: "https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/auth1.png",
                label: "信用记录",
                innerTitle: "",
                formTitle: "您的信用记录如何？",
                required: true,
                type: "select",
                rules: [{ required: true, message: "请先选择" }],
                options: [
                    { label: "无逾期", value: "无逾期", amount: 5 },
                    { label: "无贷款", value: "无贷款", amount: 10 },
                    { label: "逾期少于90天", value: "逾期少于90天", amount: 1 },
                    { label: "逾期超过90天", value: "逾期超过90天" },
                    { label: "一年内无逾期", value: "一年内无逾期", amount: 3 },
                    { label: "一年内有逾期", value: "一年内有逾期", amount: 2 }
                ],
                key: "credit_record",
                sort: 94,
                group: {
                    title: "资质信息",
                    sort: 12
                }
            },
            {
                icon: "https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/auth1.png",
                label: "工作城市",
                innerTitle: "",
                formTitle: "您的工作城市是？",
                required: true,
                type: "city",
                rules: [{ required: true, message: "请先选收入信息" }],
                key: "working_city",
                sort: 93,
                group: {
                    title: "基本信息",
                    sort: 1
                }
            },
            {
                icon: "https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/auth1.png",
                label: "贷款额度",
                placeholder: "请输入贷款额度",
                formTitle: "期望的贷款额度",
                innerTitle: "",
                required: true,
                type: "select",
                rules: [],
                unit: "万",
                col: 3,
                editConfig: {
                    label: "自定义额度",
                    unit: "万元",
                    rules: [
                        { pattern: "^[0-9]{0,100}$", message: "贷款额度格式不正确" },
                        {
                            range: [1, 1000],
                            message: ["贷款额度区间为1万-1000万", "贷款额度区间为1万-1000万"]
                        }
                    ],
                    valueType: "number"
                },
                options: [
                    { value: 3, isRecommended: true },
                    { value: 5, isRecommended: true },
                    { value: 10 },
                    { value: 15 },
                    { value: 20 },
                    { value: "更高额度", isEdit: true }
                ],
                key: "loan_amount",
                sort: 99,
                group: {
                    title: "意向信息",
                    sort: 5
                }
            }
        ]
    };
};
export const check2rz = (params: any) => {
    return axios.post("/hx-server/api-server/sys2rz", params);
};
export const commitInfo = (params: any) => {
    // 给贷款额度添加单位 万
    try {
        params.auth = params.auth.map((val: any) => {
            if (val.key === "loan_amount") {
                const status = val.value.toString().indexOf("万") == -1;
                if (status) {
                    val.value = val.value + "万";
                }
            }
            return val;
        });
    } catch (error) {
        throw Error("数据转换异常");
    }
    return axios.post("/hx-server/api-server/commitInfo", params);
};
export const getRecommend = (id: any) => {
    return axios.get("/hx-server/api-server/getProductList", {
        params: { orderId: id }
    });
};
export const getProductInfo = (params: any) => {
    return axios.get("/hx-server/api-server/getProductInfo", { params });
};

export const commitProducts = (params: any) => {
    return axios.post("/hx-server/api-server/commitSubmit", params);
};
/**
 * 进入表单页面时需要调用的回调
 * @param channel
 */
export const callBack = (channel: any) => {
    return axios.get(`/hx-server/api-server/unionLogin/callBack/${channel}`);
};
export const getRecord = (params: any) => {
    return axios.get("/hx-server/api-server/getLoanRecord", { params });
};

// 获取推荐产品
export const getPromote = () => {
    return axios.get("/hx-server/api-server/promote");
};

// 获取进件成功产品
export const getCommitResult = (params: any) => {
    return axios.get("/hx-server/api-server/commitResult", { params });
};

// 确认额度
export const confirmProductsAmount = (params?: any) => {
    return axios.get("/hx-server/api-server/confirm", params);
};

// 老用户匹配产品列表
export const oldUserMatchProduct = (params?: any) => {
    return axios.get("/hx-server/api-server/explore", params);
};

// 结果页强跳h5或微信客服
export const resultJump = (params?: any) => {
    return axios.get("/hx-server/api-server/jump", params);
};
