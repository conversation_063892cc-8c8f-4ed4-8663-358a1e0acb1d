import { useGlobalStore } from "@/stores/global";
import { getUrlData, sessionStorage } from "@/utils/index";
import { backToutiao, backURl, returnControl, youkaBack } from "@/apis/serve/conversionServe";

declare type ChannelType =
    | "quannei"
    | "toutiao"
    | "toutiaolm"
    | "bafnag"
    | "muya"
    | "youka"
    | "dasousuo";
const loadConversion = () => {
    const channel: ChannelType = useGlobalStore().channel;
    switch (channel) {
        case "quannei":
            (function (root) {
                var ksscript = document.createElement("script");
                ksscript.setAttribute("charset", "utf-8");
                ksscript.src = "//p2-yx.adkwai.com/udata/pkg/ks-ad-trace-sdk/ks-trace.3.2.0.min.js";
                var s: any = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(ksscript, s);
            })(window);
            break;
        case "toutiao":
            sessionStorage.setItem("toutiao", getUrlData());
            break;
        case "toutiaolm":
            sessionStorage.setItem("toutiaolm", getUrlData());
            break;
        case "bafnag":
            sessionStorage.setItem("bafnag", getUrlData());
            break;
        case "weimeng":
            console.log("weimeng");
            break;
        case "muya":
            sessionStorage.setItem("muya", getUrlData());
            break;
        case "dasousuo":
            window._agl = window._agl || [];
            (function () {
                _agl.push(["production", "_f7L2XwGXjyszb4d1e2oxPybgD"]);
                (function () {
                    var agl = document.createElement("script");
                    agl.type = "text/javascript";
                    agl.async = true;
                    agl.src =
                        "https://fxgate.baidu.com/angelia/fcagl.js?production=_f7L2XwGXjyszb4d1e2oxPybgD";
                    var s = document.getElementsByTagName("script")[0];
                    s.parentNode.insertBefore(agl, s);
                })();
            })();
            break;
        default:
            break;
    }
};

const reportConversion = (
    type: "formSubmit" | "formSuccess" | "loginSucess" | "register" | "formInit"
) => {
    const channel: ChannelType = useGlobalStore().channel;
    const orderId = useGlobalStore().orderId;
    const w: any = window;
    switch (channel) {
        case "quannei":
            switch (type) {
                case "formSuccess":
                    returnControl("submit").then(() => {
                        if (w._ks_trace) {
                            console.log("quannei");
                            w._ks_trace.push({
                                event: "form",
                                convertId: 495750,
                                cb: function () {
                                    console.log("Your callback function here!");
                                }
                            });
                        }
                    });

                default:
                    break;
            }
            break;
        case "toutiao":
            switch (type) {
                case "formSuccess":
                    returnControl("submit").then(() => {
                        backToutiao({
                            event_type: "form",
                            context: {
                                ad: {
                                    callback: sessionStorage.getItem("toutiao")?.clickid
                                }
                            },
                            timestamp: Date.now()
                        });
                    });
                    break;
                default:
                    break;
            }
            break;
        case "toutiaolm":
            switch (type) {
                case "formSuccess":
                    returnControl("submit").then((res) => {
                        backToutiao({
                            event_type: "form",
                            context: {
                                ad: {
                                    callback: sessionStorage.getItem("toutiaolm")?.clickid
                                }
                            },
                            timestamp: Date.now()
                        });
                    });
                    break;
                default:
                    break;
            }
            break;
        case "bafnag":
            switch (type) {
                case "formSuccess":
                    returnControl("submit").then((res) => {
                        backToutiao({
                            event_type: "form",
                            context: {
                                ad: {
                                    callback: sessionStorage.getItem("bafnag")?.clickid
                                }
                            },
                            timestamp: Date.now()
                        });
                    });
                    break;
                default:
                    break;
            }
            break;
        case "muya":
            switch (type) {
                case "register":
                    let params = sessionStorage.getItem("muya");
                    // console.log(params);
                    returnControl("reg").then((res) => {
                        if (params.get_mincallBack) {
                            backURl("GET", decodeURIComponent(params.get_mincallBack));
                        }
                    });
                    break;
                default:
                    break;
            }
            break;
        case "dasousuo":
            switch (type) {
                case "register":
                    let params = sessionStorage.getItem("muya");
                    // console.log(params);
                    returnControl("reg").then((res) => {
                        if (params.get_mincallBack) {
                            backURl("GET", decodeURIComponent(params.get_mincallBack));
                        }
                    });
                    break;
                default:
                    break;
            }
            break;
        default:
            switch (type) {
                case "formInit":
                    youkaBack();
                    break;
                default:
                    break;
            }
            break;
    }
};

export { loadConversion, reportConversion };

export default {};
