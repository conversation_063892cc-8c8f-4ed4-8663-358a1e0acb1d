import { useGlobalStore } from "@/stores/global";
import { backURl, dasousuoBack, returnControl, youkaBack } from "@/apis/serve/conversionServe";
import { sessionStorage } from "@/utils";

declare type ChannelType =
    | "quannei"
    | "toutiao"
    | "toutiaolm"
    | "bafnag"
    | "muya"
    | "youka"
    | "dasousuo";
const loadConversion = () => {
    const channel: ChannelType = useGlobalStore().channel;
    switch (channel) {
        case "quannei":
            (function (root) {
                var ksscript = document.createElement("script");
                ksscript.setAttribute("charset", "utf-8");
                ksscript.src = "//p2-yx.adkwai.com/udata/pkg/ks-ad-trace-sdk/ks-trace.3.2.0.min.js";
                var s: any = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(ksscript, s);
            })(window);
            break;
        case "dasousuo":
            sessionStorage.setItem("dasousuo", window.location.href);
            break;
        default:
            break;
    }
};

const reportConversion = (
    type: "formSubmit" | "formSuccess" | "loginSucess" | "register" | "formInit"
) => {
    const channel: ChannelType = useGlobalStore().channel;
    const orderId = useGlobalStore().orderId;
    const w: any = window;
    switch (channel) {
        case "dasousuo":
            const url: string = sessionStorage.getItem("dasousuo");
            switch (type) {
                case "loginSucess":
                    // console.log("注册");
                    returnControl("reg").then((res) => {
                        dasousuoBack({
                            token: `TCVQDvrFfIbiQiyJhh82IAgAwVIlPaBk@7Y82RoUNqi7Egz7E6sfduP8n3k7SdPVA`,
                            conversionTypes: [
                                {
                                    logidUrl: url,
                                    newType: 5
                                }
                            ]
                        });
                    });
                    break;
                case "formSuccess":
                    // console.log("提交");
                    returnControl("submit").then((res) => {
                        dasousuoBack({
                            token: `TCVQDvrFfIbiQiyJhh82IAgAwVIlPaBk@7Y82RoUNqi7Egz7E6sfduP8n3k7SdPVA`,
                            conversionTypes: [
                                {
                                    logidUrl: url,
                                    newType: 3
                                }
                            ]
                        });
                    });
                    break;
                default:
                    break;
            }
            break;
        default:
            switch (type) {
                case "formInit":
                    youkaBack();
                    break;
                default:
                    break;
            }
            break;
    }
};

export { loadConversion, reportConversion };

export default {};
