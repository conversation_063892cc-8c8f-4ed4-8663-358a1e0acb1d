<template>
    <LayoutPage class="intention-page">
        <div class="page-tips">
            <div class="mark"></div>
            一分钟填写，借钱一步到位
        </div>
        <div class="intention-main">
            <div
                v-for="item in formData"
                v-show="initState || item.state"
                :key="item.groupTilte"
                class="group"
            >
                <div class="group-title" @click="onClick">{{ item.groupTilte }}</div>
                <van-collapse v-model="activeName" accordion>
                    <van-collapse-item
                        v-for="i in item.children"
                        :key="i"
                        :disabled="disabledList.includes(i.type)"
                        :lazy-render="false"
                        :name="i.key"
                        :title="i.label"
                        @click="onShowPicker(i.type)"
                    >
                        <template #title>
                            <div class="font" v-text="i.label"></div>
                        </template>
                        <template #value>
                            <div
                                v-if="!inputItme.includes(i.label)"
                                class="font"
                                v-text="i.value ? i.value + (i.unit ?? '') : '请选择'"
                            ></div>
                            <div v-else>
                                <van-field
                                    ref="inpRef"
                                    v-model="i.value"
                                    :placeholder="i.placeholder"
                                    class="font"
                                    input-align="right"
                                    style="height: 20px; padding: 0"
                                />
                            </div>
                        </template>
                        <template v-if="inputItme.includes(i.label)" #right-icon></template>
                        <template v-if="i.type === 'picker'">
                            <teleport to="#intention-form">
                                <div>
                                    <van-popup v-model:show="showPicker" position="bottom" round>
                                        <AuthPicker
                                            v-model="i.value"
                                            :beforeSave="beforeSave"
                                            :form="i"
                                            :options="i.options"
                                            :title="i.formTitle ?? i.label"
                                            @change="onPickerChange"
                                        />
                                    </van-popup>
                                </div>
                            </teleport>
                        </template>
                        <template v-else-if="i.type === 'zhima'">
                            <teleport to="#intention-form">
                                <div>
                                    <van-popup v-model:show="showZhima" position="bottom" round>
                                        <AuthZhima
                                            v-model="i.value"
                                            :beforeSave="beforeSave"
                                            :form="i"
                                            @change="onPickerChange"
                                        />
                                    </van-popup>
                                </div>
                            </teleport>
                        </template>
                        <template v-else-if="i.type === 'select'">
                            <AuthSelect
                                v-model="i.value"
                                :conf="{ unit: i?.unit, placeholder: i?.placeholder }"
                                :form="i"
                                :options="i.options"
                                :title="i.formTitle ?? i.label"
                                @change="onPickerChange"
                            />
                        </template>
                    </van-collapse-item>
                </van-collapse>
            </div>
        </div>
        <!-- 页面footer插槽 -->
        <template #pageFooter>
            <div id="intention-form" class="intention-form">
                <div class="intention-tips">
                    <img alt="" src="@/assets/images/intention-tips.png" />
                    我们非常重视您的隐私安全，未经您授权绝不对外展示
                </div>
                <div class="form-submit">
                    <AppButton
                        :class="submitState ? '' : 'disabledBtn'"
                        class="global-btn"
                        data-collect="completeSubmitPre"
                        type="primary"
                        @Click="onSubmit"
                    >
                        立即申请
                    </AppButton>
                </div>
            </div>
        </template>
        <!-- 匹配成功弹窗 -->
        <van-dialog v-model:show="result" @confirm="dialogConfirm">
            <template #title>
                <div class="dialog-title">提交成功</div>
            </template>

            <template #default>
                <span class="circle cur" v-html="CheckCircleFill2"> </span>
                <div class="dialog-main">
                    <div>您的信息已提交成功，审核通过后将</div>
                    <div>
                        会有审核人员 <span>电话审核</span>或
                        <span>短信通知</span>
                    </div>
                    <div>您，请注意接听来电</div>
                </div>
            </template>
        </van-dialog>
    </LayoutPage>
</template>
<script lang="ts" setup>
import { useAuth } from "@/hook/useAuth";
import { computed, onBeforeMount, ref, watch } from "vue";
import { useTheme } from "@/pages/home/<USER>/themeHook/useTheme";
import { Dialog, Toast } from "vant";
import AuthPicker from "@/components/gdtAuth/picker.vue";
import AuthSelect from "@/components/gdtAuth/select.vue";
import AuthZhima from "@/components/gdtAuth/zhima.vue";
import { CheckCircleFill2 } from "@/assets/svg/index";
import { validate } from "@/utils/procedure";
import AppButton from "@/components/App/Button.vue";

const VanDialog = Dialog.Component;
const inputItme = ["姓名", "年龄"];
const disabledList = ["picker", "input", "zhima"];
const userDetails = ["name", "idno", "sex", "birthday"];
const showPicker = ref(false);
const showZhima = ref(false);
const theme = useTheme();
const { form, getFormData, submitForm, idCard, result, initState } = useAuth();
const activeName = ref<any>();
const formData = computed(() => {
    form.value.sort((a, b) => {
        return a.group.sort - b.group.sort;
    });
    return form.value.reduce((pre: any, element: any) => {
        for (let index = 0; index < pre.length; index++) {
            const i = pre[index];
            if (i.groupTilte == element.group.title) {
                i.children.push(element);
                return pre;
            }
        }

        pre.push({
            groupTilte: element.group.title,
            children: [element],
            state: false
        });
        return pre;
    }, []);
});

const submitState = ref<boolean>(false);
// 广点通头条指针
const classifyPointer = ref<number>(0);

const onClick = () => {
    console.log(formData.value);
};
const inpRef = ref();

watch(
    form,
    () => {
        submitState.value = form.value.every((element: any) => element.value);
        // 逐步展示form逻辑
        for (let index = 0; index < formData.value.length; index++) {
            const element = formData.value[index];
            if (index >= classifyPointer.value) {
                // debugger;
                element.state = classifyPointer.value == index;
                // element.children.every((element: any) => {
                //     console.log(element.key, element.value);
                //     console.log(formData);

                //     return element.value;
                // });
                if (element.children.every((element: any) => element.value)) {
                    ++classifyPointer.value;
                }
            }
        }
    },
    {
        deep: true,
        immediate: true
    }
);

onBeforeMount(getFormData);

const onShowPicker = (type: string) => {
    switch (type) {
        case "picker":
            showPicker.value = true;
            break;
        case "zhima":
            showZhima.value = true;
            break;
        default:
            break;
    }
};

const onPickerChange = () => {
    showPicker.value = false;
    showZhima.value = false;
    for (let index = 0; index < form.value.length; index++) {
        const element = form.value[index];
        if (!element.value) {
            // console.log(element.key);
            switch (element.key) {
                case "working_city":
                    showPicker.value = true;
                    activeName.value = -1;
                    break;
                case "name":
                    inpRef.value[0].focus();
                    activeName.value = -1;
                    break;
                case "birthday":
                    inpRef.value[1].focus();
                    activeName.value = -1;
                    break;
                case "zhima":
                    showZhima.value = true;
                    activeName.value = -1;
                    break;
                default:
                    activeName.value = element.key;
                    break;
            }

            let height =
                (document.documentElement.clientHeight || document.body.clientHeight) + 500;
            console.log(height);

            document.documentElement.scrollTop = height;
            document.body.scrollTop = height;
            try {
                scrollTo(0, height);
            } catch (error) {
                console.log("置顶失败");
            }
            break;
        }
    }
};

const beforeSave = (obj: any, value: any) => {
    return validate(value, obj.rules ?? []).catch((m: string) => {
        Toast.fail(m);
        return false;
    });
};

const onSubmit = () => {
    // console.log(form);
    form.value.forEach((element: any) => {
        if (userDetails.includes(element.key)) {
            idCard.value[element.key] = element.value;
        }
    });
    submitForm();
};
const dialogConfirm = () => {
    window.history.back();
};
</script>
<style lang="less" scoped>
.intention-page {
    background-color: #f6f6f6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;

    :deep(.page_scroll) {
        flex: 1;
    }

    .page-tips {
        background-color: v-bind("theme.formHeaderBgc");
        font-size: 13px;
        font-weight: 400;
        color: #333;
        line-height: 30px;
        width: 100vw;
        z-index: 99;
        text-align: center;
        height: 30px;
        box-sizing: border-box;

        .mark {
            background-image: url("@/assets/images/customAuth/formHeaderIcon.png");
            background-repeat: no-repeat;
            background-size: cover;
            width: 16px;
            height: 16px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 4px;
            top: -1px;
            position: relative;
        }
    }

    .intention-main {
        padding-bottom: 80px;

        .top-box {
            height: 41.59px;
            width: 100vw;
        }

        .group {
            margin-bottom: 10px;

            .group-title {
                height: 50px;
                line-height: 50px;
                padding-left: 10px;
                color: #222;
                font-weight: 500;
                font-size: 18px;
            }

            .font {
                font-weight: 500;
                color: #222;
            }
        }
    }

    .intention-form {
        position: fixed;
        bottom: 0;
        left: 0;
        background-color: #f6f6f6;
        width: 100%;

        .intention-tips {
            color: #e18354;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 30px;

            img {
                width: 12px;
                height: 14px;
                margin-right: 4px;
            }
        }

        .global-btn {
            width: 100vw;
            height: 50px;
            background-color: v-bind("theme.authBtnBgc") !important;
            display: block;
            border-radius: 0;
            color: #fff;
            text-align: center;
            line-height: 50px;
        }

        .disabledBtn {
            background-color: #99999950 !important;
        }
    }
}

:deep(.van-popup--bottom.van-popup--round) {
    border-radius: 0;
}
</style>
<!-- 提交成功弹窗样式 -->
<style lang="less" scoped>
:deep(.van-overlay) {
    background: rgba(0, 0, 0, 0.7);
}

:deep(.van-dialog__footer) {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

:deep(.van-button__content) {
    background-color: v-bind("theme.authBtnBgc");
    color: #fff;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

:deep(.van-dialog) {
    overflow: visible;
}

.dialog-title {
    font-weight: 600;
    font-size: 18px;
    margin-top: 30px;
}

.circle {
    position: absolute;
    top: -65px;
    left: 50%;
    margin-left: -65px;
    // width: 80px;
    // height: 80px;
    border-radius: 50%;
    border: 1px solid rgb(238, 85, 51);
    z-index: 9999;

    :deep(svg) {
        display: none;
    }

    overflow: hidden;

    &.cur {
        border: none;

        :deep(svg) {
            width: 130px;
            height: 130px;
            display: block;
            color: #81d490;
            // background-color: #fff;
        }
    }
}

.dialog-main {
    text-align: center;
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 30px;
    margin-top: 20px;

    span {
        color: #e5712d;
    }
}
</style>
