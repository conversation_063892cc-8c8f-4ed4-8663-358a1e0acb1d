<template>
    <LayoutPage class="downloadPage">
        <div class="down-info">
            <div class="qrcode">
                <img src="https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/1650441927382.png" />
            </div>
            <div class="down-info-description">
                <div class="down-info-description-title">
                    <span>浩瀚 <a> v1.0.0</a></span>
                </div>
                <div class="down-info-description-subtitle">
                    <span>微信无法下载 ，可使用浏览器打开</span>
                </div>
            </div>
            <div class="down-info-button" @click="onDownload">立即下载</div>
        </div>
    </LayoutPage>
</template>
<script lang="ts" setup>
const onDownload = () => {
    const ios = "https://apps.apple.com/cn/app/%E4%BA%BF%E4%BF%A1%E7%AE%A1%E5%AE%B6/id1630912831";
    const android =
        "https://a.app.qq.com/o/simple.jsp?pkgname=com.haohan666.yixin&g_f=1178424&fromcase=70048&scenevia=YDGW#";
    const url = /(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent) ? ios : android;
    window.open(url, "_blank");
};
</script>
<style lang="less" scoped>
.downloadPage {
    background: #fff;

    :deep(.page_scroll) {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .down-info {
        text-align: center;
        padding-bottom: 50px;
        width: 200px;
    }

    .down-info-description {
        font-size: 12px;
        color: #999;
        line-height: 1.4;
        margin-top: 12px;
    }

    .down-info-description-title {
        font-size: 15px;

        a {
            color: #333;
        }
    }

    .down-info-description-subtitle {
        margin-top: 8px;
    }

    .down-info-content {
        margin-top: 10px;
    }

    .qrcode {
        width: 200px;
        height: 200px;
        margin: 0 auto;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .down-info-button {
        margin-top: 18px;
        font-size: 9px;
        color: #fff;
        height: auto;
        padding: 6px;
        background-color: var(--color-primary-bg);
    }
}
</style>
