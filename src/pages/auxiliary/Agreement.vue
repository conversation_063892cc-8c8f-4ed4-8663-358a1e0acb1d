<template>
    <LayoutPage>
        <div v-if="data?.partner_agreement?.length > 0" class="agreement-list">
            <a v-for="item in data.partner_agreement" :href="item.value">
                <div class="agreement-content">
                    <div class="agreement-content-main">{{ item.key }}</div>
                    <div class="agreement-content-arrow">
                        <svg
                            height="1em"
                            style="vertical-align: -0.125em"
                            viewBox="0 0 48 48"
                            width="1em"
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                        >
                            <g
                                id="RightOutline-RightOutline"
                                fill="none"
                                fill-rule="evenodd"
                                stroke="none"
                                stroke-width="1"
                            >
                                <g id="RightOutline-RightOutlined">
                                    <rect
                                        id="RightOutline-矩形"
                                        fill="#FFFFFF"
                                        height="48"
                                        opacity="0"
                                        width="48"
                                        x="0"
                                        y="0"
                                    ></rect>
                                    <path
                                        id="RightOutline-right"
                                        d="M17.3947957,5.11219264 L35.5767382,22.6612572 L35.5767382,22.6612572 C36.1304785,23.2125856 36.1630514,24.0863155 35.6744571,24.6755735 L35.5767382,24.7825775 L17.3956061,42.8834676 C17.320643,42.9580998 17.2191697,43 17.1133896,43 L13.9866673,43 C13.7657534,43 13.5866673,42.8209139 13.5866673,42.6 C13.5866673,42.4936115 13.6290496,42.391606 13.7044413,42.316542 L32.3201933,23.7816937 L32.3201933,23.7816937 L13.7237117,5.6866816 C13.5653818,5.53262122 13.5619207,5.27937888 13.7159811,5.121049 C13.7912854,5.04365775 13.8946805,5 14.0026627,5 L17.1170064,5 C17.2206403,5 17.3202292,5.04022164 17.3947957,5.11219264 Z"
                                        fill="currentColor"
                                        fill-rule="nonzero"
                                    ></path>
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
            </a>
        </div>
    </LayoutPage>
</template>
<script lang="ts" setup>
import { getProductInfo } from "@/apis";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const data = ref<any>({});
onMounted(() => {
    getProductInfo({ product_id: route.query.id }).then((res: any) => {
        data.value = res.data;
    });
});
</script>
<style lang="less" scoped>
.agreement-list {
    background-color: #fff;
    padding-left: 12px;
    border-bottom: 1px solid #eeeeee;

    a {
        color: #333;
        outline: none;
    }
}

.agreement-content {
    display: flex;
    align-items: center;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    padding-right: 16px;
    height: 50px;
    line-height: 50px;
    font-size: 17px;
    border-top: 1px solid #eeeeee;

    .agreement-content-main {
        flex: 1;
        color: #333;
    }

    .agreement-content-arrow {
        color: #cccccc;
    }
}
</style>
