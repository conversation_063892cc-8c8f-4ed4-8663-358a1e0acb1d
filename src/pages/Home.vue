<template>
    <template v-if="toutiaoChannel.includes(store.channel)">
        <newToutiaoGdtPage></newToutiaoGdtPage>
    </template>
    <template v-else-if="toutiaoChannel1.includes(store.channel)">
        <ToutiaoHome></ToutiaoHome>
    </template>
    <template v-else-if="zuoXingHuangChannel.includes(store.channel)">
        <ZuoXingHuangPage />
    </template>
    <template v-else>
        <HomeView></HomeView>
    </template>
</template>
<script lang="ts" setup>
import HomeView from "@/pages/home/<USER>";
import ToutiaoHome from "@/pages/home/<USER>/toutiaoHome.vue";
import newToutiaoGdtPage from "@/pages/home/<USER>/index.vue";
import { useGlobalStore } from "@/stores/global";
import { toutiaoChannel, zuoXingHuangChannel } from "@/common/channel";
import ZuoXingHuangPage from "@/pages/home/<USER>/zuoXingHuangPage.vue";

const toutiaoChannel1 = [
    "toutiaolm",
    "jinyan",
    "bafang1",
    "1752908950974471",
    "TTzhongzhou",
    "ttkhlj"
];
const store = useGlobalStore();
</script>
<style lang="less" scoped></style>
