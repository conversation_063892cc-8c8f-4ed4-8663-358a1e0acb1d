import xh from "../theme/xinghu";
import toutiao from "../theme/toutiao";
import { useGlobalStore } from "@/stores/global";
import { shallowRef } from "vue";
import { toutiaoChannel } from "@/common/channel";

const theme = shallowRef<any>({});
export const useTheme = () => {
    const store = useGlobalStore();
    if (store.channel == "gdtxh") {
        theme.value = xh;
    } else if (toutiaoChannel.includes(store.channel)) {
        theme.value = toutiao;
    }
    return theme;
};
