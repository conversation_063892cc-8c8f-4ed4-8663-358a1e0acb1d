<template>
    <div class="loginPage">
        <img alt="" class="pageImg" src="@/assets/images/hhjTop.png" />
        <LoginLogic>
            <template #main="scope">
                <div class="login">
                    <AppInput v-model="scope.form.phone" class="phone" placeholder="请输入手机号">
                        <template #prefix>
                            <div class="phone-iconBox">
                                <div class="phone-icon"></div>
                            </div>
                        </template>
                    </AppInput>
                    <AppInput
                        v-show="scope.phoneState"
                        v-model="scope.form.code"
                        :data-collect="scope.codeStatus ? 'sendCaptcha' : 'reSend'"
                        class="code"
                        placeholder="请输入验证码"
                    >
                        <template #suffix>
                            <div class="code-btn" @click="scope.fetchCaptcha">
                                <template v-if="!scope.codeLoading">
                                    <template v-if="scope.codeTime < 0"
                                        >{{ scope.codeStatus ? "获取验证码" : "重新获取" }}
                                    </template>
                                    <template v-else>{{ scope.codeTime }}S</template>
                                </template>
                            </div>
                        </template>
                    </AppInput>
                    <AppButton class="login-btn" @click="scope.doLogin()">立即申请</AppButton>
                    <div class="login-agreement">
                        <div
                            :class="{ 'on-switch': scope.switchAgreement }"
                            class="switch"
                            @click="scope.changeSwitch"
                        ></div>
                        <div class="agreement-text">
                            我已阅读并同意
                            <a
                                v-for="item in scope.agreement ?? []"
                                :key="item.key"
                                :href="item.value"
                                >{{ item.key }}</a
                            >
                        </div>
                    </div>
                </div>
            </template>
        </LoginLogic>
        <img alt="" class="pageImg" src="@/assets/images/hhjBottom.png" />
    </div>
</template>
<script lang="ts" setup>
import LoginLogic from "@/gadget/Login/loginLogic.vue";
import AppInput from "@/components/App/Input.vue";
import AppButton from "@/components/App/Button.vue";
import { ref } from "vue";

const popupShow = ref<any>(false);

const onShow = (scope: any) => {
    if (scope.checkPhoneAndAgreement()) {
        popupShow.value = true;
    }
};
</script>
<style lang="less" scoped>
:deep(.van-popup--center) {
    border-radius: 12px;
    top: 40%;
    overflow: visible;
}

.loginPage {
    background-color: #fff;

    .pageImg {
        width: 100vw;
        object-fit: fill;
        display: block;
    }

    .login {
        padding: 0 33px;
        // background: url("@/assets/images/rpjCenter.png");
        background-size: 100% auto;

        .phone {
            height: 60px;
            border-radius: 30px;
            border: 1px solid #e9e9e9;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 20px;
            margin-bottom: 11px;
            margin-top: 20px;
            padding: 0 23px;

            .phone-iconBox {
                .phone-icon {
                    background: url("@/assets/images/hhjPhone.png");
                    width: 17px;
                    height: 20px;
                }
            }
        }

        .code {
            height: 60px;
            border-radius: 30px;
            border: 1px solid #e9e9e9;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 20px;
            padding-left: 23px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;

            .code-btn {
                font-size: 12px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #104cb5;
                line-height: 17px;
                border-left: 1px solid #c9c9c9;
                width: 94px;
                text-align: center;
            }
        }

        .login-btn {
            height: 60px;
            background: #1d6ffd;
            border-radius: 30px;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #ffffff;
            line-height: 22px;
            margin-bottom: 11px;
        }

        .login-agreement {
            padding: 0 10px;
            margin-bottom: 25px;
            line-height: 22px;

            .switch {
                width: 14px;
                height: 14px;
                background-image: url("@/assets/images/offAgreement.jpg");
                background-size: cover;
                margin-right: 5px;
                display: inline-block;
                position: relative;
                top: 2px;
                border-radius: 50%;
            }

            .on-switch {
                background: url("@/assets/images/true.png") #1d6ffd no-repeat;
            }

            .agreement-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                display: inline;

                a {
                    color: #1d6ffd;
                }
            }
        }
    }

    .popup {
        height: 224px;
        background: #ffffff;
        width: 80vw;
        padding: 20px 22px 23px 22px;
        position: relative;

        .popup-close {
            width: 21px;
            height: 21px;
            position: absolute;
            background: url("@/assets/images/360jtClose.png") no-repeat;
            background-size: cover;
            top: -31px;
            right: 0px;
            z-index: 99999;
        }

        .popup-title {
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #333333;
            line-height: 28px;
            text-align: center;
            margin-bottom: 17px;
        }

        .popup-code {
            height: 60px;
            background: #eef2fe;
            border-radius: 30px;
            border: 1px solid #c7c8c7;
            margin-bottom: 16px;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            // color: #cccccc;
            line-height: 22px;

            .code {
                line-height: 60px;
                padding: 0 25px;
                font-size: 12px;
                color: #104cb5;
            }
        }

        .popup-btn {
            height: 60px;
            background: #104cb5;
            border-radius: 30px;
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #ffffff;
            // line-height: 28px;
        }
    }
}
</style>
