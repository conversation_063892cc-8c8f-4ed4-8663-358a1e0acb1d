<template>
    <LoginLogic :agreementState="false">
        <template #main="scope">
            <div class="page">
                <header class="page-header clearfix">
                    <div class="header-title">最高可借金额</div>
                    <div class="header-money">
                        <span> ¥ </span>
                        <span> 200,000 </span>
                    </div>
                    <div class="header-describe">综合年化利率6%起，10000元1天最低日息2元</div>
                </header>
                <section class="page-main">
                    <div class="login clearfix">
                        <div class="login-limit">
                            <div class="limit-left">借款额度</div>
                            <div class="limit-right">
                                最高可借
                                <span>¥200,000</span>
                                元
                            </div>
                        </div>
                        <div class="login-limit">
                            <div class="limit-left">借款期限</div>
                            <div class="limit-right">可分1-12个月</div>
                        </div>
                        <!-- form -->
                        <div class="form">
                            <div
                                v-if="scope.loginStatus"
                                class="form-input"
                                style="margin-bottom: 16px"
                            >
                                <input
                                    v-model="scope.form.phone"
                                    placeholder="请输入手机号"
                                    type="text"
                                />
                                <div class="form-iconBox">
                                    <img
                                        alt=""
                                        src="@/assets/images/phone.png"
                                        style="width: 14px; height: 18px"
                                    />
                                </div>
                            </div>
                            <div v-if="scope.loginStatus" class="form-input">
                                <input
                                    v-model="scope.form.code"
                                    placeholder="请输入验证码"
                                    type="text"
                                />
                                <div class="form-iconBox">
                                    <img
                                        alt=""
                                        src="@/assets/images/code.png"
                                        style="width: 20px; height: 16px"
                                    />
                                </div>
                                <div class="form-code" @click="scope.fetchCaptcha">
                                    <template v-if="!scope.codeLoading">
                                        <template v-if="scope.codeTime < 0"
                                            >{{ scope.codeStatus ? "获取验证码" : "重发" }}
                                        </template>
                                        <template v-else>
                                            <span style="color: #666">
                                                {{ scope.codeTime }}s后重发
                                            </span>
                                        </template>
                                    </template>
                                </div>
                            </div>
                            <AppButton
                                class="global-btn form-submit"
                                data-collect="login"
                                type="primary"
                                @click="onSubmit(scope)"
                                >马上领取
                            </AppButton>
                        </div>
                        <!-- 协议 -->
                        <div class="detail-agreement">
                            <div
                                :class="{ active: scope?.switchAgreement }"
                                class="detail-agreement_mark"
                                @click="scope.changeSwitch"
                            ></div>
                            <span>我同意并授权</span>
                            <a
                                v-for="item in scope?.agreement ?? []"
                                :key="item.key"
                                :href="item.value"
                                >{{ item.key }}</a
                            >
                        </div>
                    </div>
                    <div class="advantage clearfix">
                        <div class="advantage-title">平台优势</div>
                        <div class="advantage-describe">
                            <div class="advantage-details">
                                <img alt="" src="@/assets/images/home/<USER>" />
                                <div>极速放款</div>
                            </div>
                            <div class="advantage-details">
                                <img alt="" src="@/assets/images/home/<USER>" />
                                <div>更加合规</div>
                            </div>
                            <div class="advantage-details">
                                <img alt="" src="@/assets/images/home/<USER>" />
                                <div>利率更低</div>
                            </div>
                        </div>
                    </div>
                </section>
                <footer class="page-footer">
                    <Statement style="background-color: #fff"></Statement>
                </footer>
            </div>
            <van-dialog
                v-model:show="visible"
                show-cancel-button
                title="温馨提示"
                @confirm="onSub(scope)"
            >
                <div class="detail-agreement pop">
                    <span>请阅读并同意</span>
                    <a v-for="item in scope?.agreement ?? []" :href="item.value">{{ item.key }}</a>
                </div>
            </van-dialog>
        </template>
    </LoginLogic>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import LoginLogic from "@/gadget/Login/loginLogic.vue";
import AppButton from "@/components/App/Button.vue";
import Statement from "@/gadget/statement/index.vue";
import { Dialog } from "vant";

const VanDialog = Dialog.Component;
const visible = ref<any>(false);
const onSubmit = (scope: any) => {
    if (!scope.switchAgreement) {
        visible.value = true;
    } else {
        scope.doLogin();
    }
};
const onSub = (scope: any) => {
    scope.changeSwitch();
    scope.doLogin();
};
</script>
<style lang="less" scoped>
.page {
    background-color: #f8f8f8;

    .page-header {
        background: url("@/assets/images/home/<USER>") no-repeat;
        // background-size: 375px 240px;
        background-size: cover;
        // width: 375px;
        height: 64vw;
        // padding-bottom: 64%;
        box-sizing: border-box;
        text-align: center;

        .header-title {
            font-size: 14px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 16px;
            margin-top: 52px;
        }

        .header-money {
            font-size: 48px;
            font-family: D-DIN-DIN-Bold, D-DIN-DIN;
            font-weight: bold;
            color: #ffffff;
            line-height: 56px;

            span:nth-child(1) {
                font-size: 28px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 33px;
            }
        }

        .header-describe {
            font-size: 12px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 18px;
        }
    }

    .page-main {
        .login {
            background-color: #fff;
            padding: 0 20px;

            .login-limit {
                display: flex;
                margin-bottom: 12px;
                justify-content: space-between;

                &:nth-child(1) {
                    margin-top: 7px;
                }

                .limit-left {
                    font-size: 14px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #888888;
                    line-height: 18px;
                }

                .limit-right {
                    font-size: 14px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    line-height: 18px;

                    span {
                        color: #ee5533;
                    }
                }
            }

            .form {
                .form-input {
                    position: relative;

                    input {
                        height: 44px;
                        background: #f6f6f6;
                        border-radius: 22px 22px 22px 22px;
                        opacity: 1;
                        border: none;
                        width: 100%;
                        padding: 0 20px 0 62px;
                    }

                    .form-iconBox {
                        position: absolute;
                        top: 12px;
                        left: 0;
                        width: 53px;
                        height: 20px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-right: 1px solid #d8d8d8;
                    }

                    .form-code {
                        .form-iconBox();
                        width: 94px;
                        border: none;
                        border-left: 1px solid #d8d8d8;
                        right: 0;
                        left: inherit;
                        font-size: 14px;
                        font-family: PingFang SC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #ee5533;
                        line-height: 16px;
                    }
                }

                .form-submit {
                    background: linear-gradient(133deg, #fe8431 0%, #f74e14 100%);
                    margin-top: 14px;
                }
            }

            .detail-agreement {
                margin-top: 12px;
                font-size: 11px;
                font-weight: 400;
                color: #888888;
                line-height: 16px;
                margin-bottom: 22px;
                position: relative;
                padding-left: 20px;
                text-align: left;

                &.pop {
                    padding-bottom: 20px;
                }

                .detail-agreement_mark {
                    height: 20px;
                    width: 20px;
                    position: absolute;
                    left: -4px;
                    top: -3px;
                    background-image: url("@/assets/images/未选@2x.png");
                    background-repeat: no-repeat;
                    background-size: cover;

                    &::before {
                        display: none;
                        content: " ";
                        width: 100%;
                        height: 100%;
                        background-image: url("@/assets/images/已选@2x.png");
                        background-repeat: no-repeat;
                        background-size: cover;
                        position: relative;
                    }

                    &.active::before {
                        // opacity: 1;
                        display: block;
                        // background-image: url('@/assets/images/已选@2x.png');
                    }
                }

                a {
                    color: #333333;
                }
            }
        }

        .advantage {
            margin-top: 16px;
            background-color: #fff;
            min-height: 132px;
            margin-bottom: 16px;

            .advantage-title {
                font-size: 16px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #666666;
                line-height: 19px;
                height: 22px;
                text-align: center;
                margin-top: 13px;
                margin-bottom: 17px;
            }

            .advantage-describe {
                display: flex;
                justify-content: space-around;
                padding: 0 20px;
                text-align: center;
                font-size: 12px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 14px;

                img {
                    margin-bottom: 6px;
                    width: 34px;
                    display: inline-block;
                    height: 34px;
                }
            }
        }
    }
}

.detail-agreement {
    margin-top: 12px;
    font-size: 11px;
    font-weight: 400;
    color: #888888;
    line-height: 16px;
    margin-bottom: 4px;
    position: relative;
    padding: 0 20px;

    &.pop {
        padding-bottom: 20px;
    }

    .detail-agreement_mark {
        height: 20px;
        width: 20px;
        position: absolute;
        left: -4px;
        top: -3px;
        background-image: url("@/assets/images/未选@2x.png");
        background-repeat: no-repeat;
        background-size: cover;

        &::before {
            display: none;
            content: " ";
            width: 100%;
            height: 100%;
            background-image: url("@/assets/images/已选@2x.png");
            background-repeat: no-repeat;
            background-size: cover;
            position: relative;
        }

        &.active::before {
            // opacity: 1;
            display: block;
            // background-image: url('@/assets/images/已选@2x.png');
        }
    }

    a {
        color: #333333;
    }
}
</style>
