<template>
    <div class="loginPage">
        <img alt="" class="pageImg" src="@/assets/images/rpjTop.png" style="height: 228px" />
        <LoginLogic>
            <template #main="scope">
                <div class="login">
                    <AppInput v-model="scope.form.phone" class="phone" placeholder="请输入手机号">
                    </AppInput>
                    <AppButton class="login-btn" @click="onShow(scope)">立即申请</AppButton>
                    <div class="login-agreement">
                        <div
                            :class="{ 'on-switch': scope.switchAgreement }"
                            class="switch"
                            @click="scope.changeSwitch"
                        ></div>
                        <div class="agreement-text">
                            我已阅读并同意
                            <a
                                v-for="item in scope.agreement ?? []"
                                :key="item.key"
                                :href="item.value"
                                >{{ item.key }}</a
                            >
                        </div>
                    </div>
                </div>
                <van-popup v-model:show="popupShow" :close-on-click-overlay="false">
                    <div class="popup">
                        <div class="popup-close" @click="popupShow = false"></div>
                        <div class="popup-title">请填写验证码</div>
                        <AppInput
                            v-model="scope.form.code"
                            :data-collect="scope.codeStatus ? 'sendCaptcha' : 'reSend'"
                            class="popup-code"
                            placeholder="请输入验证码"
                        >
                            <template #suffix>
                                <div class="code" @click="scope.fetchCaptcha">
                                    <template v-if="!scope.codeLoading">
                                        <template v-if="scope.codeTime < 0"
                                            >{{ scope.codeStatus ? "获取验证码" : "重新获取" }}
                                        </template>
                                        <template v-else>{{ scope.codeTime }}S</template>
                                    </template>
                                </div>
                            </template>
                        </AppInput>
                        <AppButton class="popup-btn" data-collect="login" @click="scope.doLogin"
                            >立即申请
                        </AppButton>
                    </div>
                </van-popup>
            </template>
        </LoginLogic>
        <img alt="" class="pageImg" src="@/assets/images/rpjBottom.png" style="margin-top: -1px" />
    </div>
</template>
<script lang="ts" setup>
import LoginLogic from "@/gadget/Login/loginLogic.vue";
import AppInput from "@/components/App/Input.vue";
import AppButton from "@/components/App/Button.vue";
import { ref } from "vue";

const popupShow = ref<any>(false);

const onShow = (scope: any) => {
    if (scope.checkPhoneAndAgreement()) {
        popupShow.value = true;
    }
};
</script>
<style lang="less" scoped>
:deep(.van-popup--center) {
    border-radius: 12px;
    top: 40%;
    overflow: visible;
}

.loginPage {
    background-color: #fff;

    .pageImg {
        width: 100vw;
        object-fit: fill;
        display: block;
    }

    .login {
        padding: 0 33px;
        background: url("@/assets/images/rpjCenter.png");
        background-size: 100% auto;
        margin-top: -1px;
        display: table;
        content: "";

        .phone {
            margin-top: 1px;
            height: 60px;
            background: #f7f7f7;
            border-radius: 30px;
            border: 1px solid #c7c8c7;
            line-height: 60px;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            // color: #cccccc;
            padding: 0 27px;
            margin-bottom: 16px;
        }

        .login-btn {
            height: 58px;
            background: #104cb5;
            border-radius: 30px;
            font-size: 22px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #ffffff;
            line-height: 58px;
            margin-bottom: 10px;
        }

        .login-agreement {
            padding: 0 10px;
            margin-bottom: 25px;
            line-height: 22px;

            .switch {
                width: 14px;
                height: 14px;
                background-image: url("@/assets/images/offAgreement.jpg");
                background-size: cover;
                margin-right: 5px;
                display: inline-block;
                position: relative;
                top: 2px;
                border-radius: 50%;
            }

            .on-switch {
                background: url("@/assets/images/true.png") #104cb5 no-repeat;
            }

            .agreement-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                display: inline;

                a {
                    color: #104cb5;
                }
            }
        }
    }

    .popup {
        height: 224px;
        background: #ffffff;
        width: 80vw;
        padding: 20px 22px 23px 22px;
        position: relative;

        .popup-close {
            width: 21px;
            height: 21px;
            position: absolute;
            background: url("@/assets/images/rpj1.png") no-repeat;
            background-size: cover;
            top: -31px;
            right: 0px;
            z-index: 99999;
        }

        .popup-title {
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #333333;
            line-height: 28px;
            text-align: center;
            margin-bottom: 17px;
        }

        .popup-code {
            height: 60px;
            background: #eef2fe;
            border-radius: 30px;
            border: 1px solid #c7c8c7;
            margin-bottom: 16px;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            // color: #cccccc;
            line-height: 22px;

            .code {
                line-height: 60px;
                padding: 0 25px;
                font-size: 12px;
                color: #104cb5;
            }
        }

        .popup-btn {
            height: 60px;
            background: #104cb5;
            border-radius: 30px;
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #ffffff;
            // line-height: 28px;
        }
    }
}
</style>
