<template>
    <div class="loginPage">
        <img alt="" class="pageImg" src="@/assets/images/sbTop.png" />
        <LoginLogic>
            <template #main="scope">
                <div class="login">
                    <AppInput v-model="scope.form.phone" class="phone" placeholder="请输入手机号">
                    </AppInput>
                    <AppButton class="login-btn" @click="onShow(scope)">立即申请</AppButton>
                </div>
                <van-popup v-model:show="popupShow" :close-on-click-overlay="false">
                    <div class="popup">
                        <div class="popup-close" @click="popupShow = false"></div>
                        <div class="popup-title">请确认信息</div>
                        <div class="popup-tips">
                            {{ scope.form.phone }}
                            <img alt="" src="@/assets/images/sbWrite.png" />
                        </div>
                        <AppInput
                            v-model="scope.form.code"
                            :data-collect="scope.codeStatus ? 'sendCaptcha' : 'reSend'"
                            class="popup-code"
                            placeholder="请输入验证码"
                        >
                            <template #suffix>
                                <div class="code" @click="scope.fetchCaptcha">
                                    <template v-if="!scope.codeLoading">
                                        <template v-if="scope.codeTime < 0"
                                            >{{ scope.codeStatus ? "获取验证码" : "重新获取" }}
                                        </template>
                                        <template v-else>{{ scope.codeTime }}S</template>
                                    </template>
                                </div>
                            </template>
                        </AppInput>
                        <AppButton class="popup-btn" data-collect="login" @click="scope.doLogin"
                            >立即申请
                        </AppButton>
                        <div class="login-agreement">
                            <div
                                :class="{ 'on-switch': scope.switchAgreement }"
                                class="switch"
                                @click="scope.changeSwitch"
                            ></div>
                            <div class="agreement-text">
                                我已阅读并同意
                                <a
                                    v-for="item in scope.agreement ?? []"
                                    :key="item.key"
                                    :href="item.value"
                                    >{{ item.key }}</a
                                >
                            </div>
                        </div>
                    </div>
                </van-popup>
            </template>
        </LoginLogic>
        <img alt="" class="pageImg" src="@/assets/images/sbBottom.png" style="margin-top: -1px" />
    </div>
</template>
<script lang="ts" setup>
import LoginLogic from "@/gadget/Login/loginLogic.vue";
import AppInput from "@/components/App/Input.vue";
import AppButton from "@/components/App/Button.vue";
import { Toast } from "vant";
import { ref } from "vue";

const popupShow = ref<any>(false);

const onShow = (scope: any) => {
    if (scope.phoneState) {
        popupShow.value = true;
        return;
    }
    Toast.fail("请填写合法的手机号");
};
</script>
<style lang="less" scoped>
:deep(.van-popup--center) {
    border-radius: 15px;
    top: 40%;
    overflow: visible;
}

.loginPage {
    background-color: #fff;

    .pageImg {
        width: 100vw;
        object-fit: fill;
        display: block;
    }

    .login {
        padding: 0 12px;
        background-size: 100% auto;
        background-color: #f4f4f4;
        margin-top: -1px;
        display: table;
        content: "";
        width: 100%;

        .phone {
            height: 60px;
            background: #ffffff;
            border-radius: 10px;
            border: 1px solid #e3e3e3;
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            margin: 16px 24px 24px;
        }

        .login-btn {
            height: 63px;
            background: #ff9833;
            border-radius: 10px;
            font-size: 20px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 28px;
        }
    }

    .popup {
        height: 414px;
        background: #ffffff;
        width: 90vw;
        padding: 20px 22px 23px 22px;
        position: relative;
        border-radius: 15px;

        .popup-close {
            width: 12px;
            height: 12px;
            position: absolute;
            background: url("@/assets/images/sbClone.png") no-repeat;
            background-size: cover;
            top: 18px;
            left: 18px;
        }

        .popup-title {
            font-size: 20px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 28px;
            margin-top: 28px;
            margin-bottom: 8px;
            text-align: center;
        }

        .popup-tips {
            font-size: 30px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 42px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 44px;

            img {
                width: 16px;
                height: 16px;
                margin-left: 17px;
            }
        }

        .popup-code {
            height: 62px;
            border-radius: 5px;
            border: 1px solid #e3e3e3;
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 25px;
            margin-bottom: 40px;

            .code {
                line-height: 60px;
                font-size: 18px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #cccccc;
                padding: 0 18px;
                position: relative;

                &::before {
                    content: "";
                    display: inline-block;
                    position: absolute;
                    width: 1px;
                    height: 13px;
                    background: #d8d8d8;
                    top: 24px;
                    left: 0;
                }
            }
        }

        .popup-btn {
            height: 63px;
            background: #ff9833;
            border-radius: 5px;
            font-size: 20px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 28px;
            margin-bottom: 10px;
        }

        .login-agreement {
            padding: 0 10px;
            margin-bottom: 25px;
            line-height: 22px;

            .switch {
                width: 14px;
                height: 14px;
                background-image: url("@/assets/images/offAgreement.jpg");
                background-size: cover;
                margin-right: 5px;
                display: inline-block;
                position: relative;
                top: 2px;
                border-radius: 50%;
            }

            .on-switch {
                background: url("@/assets/images/true.png") #60be8a no-repeat;
            }

            .agreement-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                display: inline;

                a {
                    color: #60be8a;
                }
            }
        }
    }
}
</style>
