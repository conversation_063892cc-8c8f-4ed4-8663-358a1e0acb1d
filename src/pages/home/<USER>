<template>
    <div class="loginPage">
        <img alt="" class="pageImg" src="@/assets/images/r3Top.png" />
        <LoginLogic>
            <template #main="scope">
                <div class="login">
                    <AppInput v-model="scope.form.phone" class="phone" placeholder="请输入手机号">
                        <template #prefix>
                            <div class="phone-iconBox">
                                <div class="phone-icon"></div>
                            </div>
                        </template>
                    </AppInput>
                    <AppInput
                        v-show="scope.phoneState"
                        v-model="scope.form.code"
                        :data-collect="scope.codeStatus ? 'sendCaptcha' : 'reSend'"
                        class="code"
                        placeholder="请输入验证码"
                    >
                        <template #suffix>
                            <div class="code-btn" @click="scope.fetchCaptcha">
                                <template v-if="!scope.codeLoading">
                                    <template v-if="scope.codeTime < 0"
                                        >{{ scope.codeStatus ? "获取验证码" : "重新获取" }}
                                    </template>
                                    <template v-else>{{ scope.codeTime }}S</template>
                                </template>
                            </div>
                        </template>
                    </AppInput>
                    <AppButton class="login-btn" @click="scope.doLogin()">立即申请</AppButton>
                    <div class="login-agreement">
                        <div
                            :class="{ 'on-switch': scope.switchAgreement }"
                            class="switch"
                            @click="scope.changeSwitch"
                        ></div>
                        <div class="agreement-text">
                            我已阅读并同意
                            <a
                                v-for="item in scope.agreement ?? []"
                                :key="item.key"
                                :href="item.value"
                                >{{ item.key }}</a
                            >
                        </div>
                    </div>
                </div>
            </template>
        </LoginLogic>
        <img alt="" class="pageImg" src="@/assets/images/r3Bottom.png" />
    </div>
</template>
<script lang="ts" setup>
import LoginLogic from "@/gadget/Login/loginLogic.vue";
import AppInput from "@/components/App/Input.vue";
import AppButton from "@/components/App/Button.vue";
import { ref } from "vue";

const popupShow = ref<any>(false);

const onShow = (scope: any) => {
    if (scope.checkPhoneAndAgreement()) {
        popupShow.value = true;
    }
};
</script>
<style lang="less" scoped>
:deep(.van-popup--center) {
    border-radius: 12px;
    top: 40%;
    overflow: visible;
}

.loginPage {
    background-color: #fff;

    .pageImg {
        width: 100vw;
        object-fit: fill;
        display: block;
    }

    .login {
        padding: 0 33px;
        background: url("@/assets/images/r3Center.png") no-repeat;
        background-size: 100% 100%;
        display: table;
        content: "";

        .phone {
            margin-bottom: 19px;
            margin-top: 28px;
            padding: 0 36px;

            height: 49px;
            background: #f1f1f1;
            border-radius: 30px;
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            // color: #cfcfcf;
            line-height: 25px;
        }

        .code {
            height: 49px;
            background: #f1f1f1;
            border-radius: 30px;
            display: flex;
            align-items: center;
            padding-left: 36px;
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            // color: #cfcfcf;
            line-height: 25px;
            margin-bottom: 19px;

            .code-btn {
                height: 25px;
                font-size: 18px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #59aca3;
                line-height: 25px;
                border-left: 1px solid #cacaca;
                width: 112px;
                text-align: center;
            }
        }

        .login-btn {
            margin-bottom: 7px;
            height: 49px;
            background: linear-gradient(270deg, #ffb138 0%, #fd532d 100%);
            border-radius: 30px;
            font-size: 20px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 28px;
        }

        .login-agreement {
            padding: 0 10px;
            margin-bottom: 25px;
            line-height: 22px;

            .switch {
                width: 14px;
                height: 14px;
                background-image: url("@/assets/images/offAgreement.jpg");
                background-size: cover;
                margin-right: 5px;
                display: inline-block;
                position: relative;
                top: 2px;
                border-radius: 50%;
            }

            .on-switch {
                background: url("@/assets/images/true.png") #63a1df no-repeat;
            }

            .agreement-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                display: inline;

                a {
                    color: #63a1df;
                }
            }
        }
    }
}
</style>
