<template>
    <div class="loginPage">
        <img alt="" class="pageImg" src="@/assets/images/hbTop.png" />
        <LoginLogic>
            <template #main="scope">
                <div class="login">
                    <AppInput v-model="scope.form.phone" class="phone" placeholder="请输入手机号">
                        <template #prefix>
                            <div class="phone-iconBox">
                                <div class="phone-icon">手机号</div>
                            </div>
                        </template>
                    </AppInput>
                    <div class="login-btnBox">
                        <AppButton class="login-btn" @click="onShow(scope)">立即申请</AppButton>
                    </div>
                    <!-- <div class="login-agreement">
                        <div
                            class="switch"
                            :class="{ 'on-switch': scope.switchAgreement }"
                            @click="scope.changeSwitch"
                        ></div>
                        <div class="agreement-text">
                            我已阅读并同意
                            <a
                                :href="item.value"
                                v-for="item in scope.agreement ?? []"
                                :key="item.key"
                                >{{ item.key }}</a
                            >
                        </div>
                    </div> -->
                </div>
                <van-popup v-model:show="popupShow" :close-on-click-overlay="false">
                    <div class="popup">
                        <div class="popup-close" @click="popupShow = false"></div>
                        <AppInput
                            v-model="scope.form.code"
                            :data-collect="scope.codeStatus ? 'sendCaptcha' : 'reSend'"
                            class="popup-code"
                            placeholder="请输入验证码"
                        >
                            <template #suffix>
                                <div class="code" @click="scope.fetchCaptcha">
                                    <template v-if="!scope.codeLoading">
                                        <template v-if="scope.codeTime < 0"
                                            >{{ scope.codeStatus ? "获取验证码" : "重新获取" }}
                                        </template>
                                        <template v-else>{{ scope.codeTime }}S</template>
                                    </template>
                                </div>
                            </template>
                        </AppInput>
                        <AppButton class="popup-btn" data-collect="login" @click="scope.doLogin"
                            >立即申请
                        </AppButton>
                        <div class="login-agreement">
                            <div
                                :class="{ 'on-switch': scope.switchAgreement }"
                                class="switch"
                                @click="scope.changeSwitch"
                            ></div>
                            <div class="agreement-text">
                                我已阅读并同意
                                <a
                                    v-for="item in scope.agreement ?? []"
                                    :key="item.key"
                                    :href="item.value"
                                    >{{ item.key }}</a
                                >
                            </div>
                        </div>
                    </div>
                </van-popup>
            </template>
        </LoginLogic>
        <img alt="" class="pageImg" src="@/assets/images/hbBottom.png" style="margin-top: -1px" />
    </div>
</template>
<script lang="ts" setup>
import LoginLogic from "@/gadget/Login/loginLogic.vue";
import AppInput from "@/components/App/Input.vue";
import AppButton from "@/components/App/Button.vue";
import { Toast } from "vant";
import { ref } from "vue";

const popupShow = ref<any>(false);

const onShow = (scope: any) => {
    if (scope.phoneState) {
        popupShow.value = true;
        return;
    }
    Toast.fail("请填写合法的手机号");
};
</script>
<style lang="less" scoped>
:deep(.van-popup--center) {
    border-radius: 12px;
    top: 40%;
    overflow: visible;
}

.loginPage {
    background-color: #fff;

    .pageImg {
        width: 100vw;
        object-fit: fill;
        display: block;
    }

    .login {
        padding: 0 33px;
        background: url("@/assets/images/hbCenter.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -1px;
        display: table;
        width: 100%;
        content: "";

        .phone {
            margin-top: 28px;
            padding: 0 13px;
            margin-bottom: 48px;
            height: 56px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #80a4e2;
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 25px;

            :deep(input) {
                text-align: right;
            }

            .phone-iconBox {
                .phone-icon {
                    font-size: 18px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #333333;
                    line-height: 25px;
                }
            }
        }

        .login-btnBox {
            width: 250px;
            height: 66px;
            background: linear-gradient(180deg, #ffffff 0%, #fec689 100%);
            border-radius: 33px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            margin-bottom: 16px;

            .login-btn {
                width: 239px;
                height: 57px;
                background: linear-gradient(180deg, #ffd8ab 0%, #c38f23 100%);
                box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
                border-radius: 33px;
                font-size: 24px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #683f0d;
                line-height: 33px;
            }
        }
    }

    .popup {
        height: 224px;
        background: #ffffff;
        width: 80vw;
        padding: 20px 22px 23px 22px;
        position: relative;
        border-radius: 12px;

        .popup-close {
            width: 21px;
            height: 21px;
            position: absolute;
            background: url("@/assets/images/rpj1.png") no-repeat;
            background-size: cover;
            top: -31px;
            right: 0px;
            z-index: 99999;
        }

        .popup-code {
            height: 60px;
            background: #fff8ef;
            border-radius: 30px;
            border: 1px solid #80a4e2;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #cccccc;
            line-height: 22px;
            margin-bottom: 16px;

            .code {
                line-height: 60px;
                padding: 0 25px;
                font-size: 12px;
                color: #104cb5;
            }
        }

        .popup-btn {
            height: 60px;
            background: #104cb5 linear-gradient(180deg, #ffd8ab 0%, #c38f23 100%);
            border-radius: 30px;
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #683f0d;
            line-height: 28px;
            // line-height: 28px;
        }

        .login-agreement {
            padding: 0 10px;
            margin-top: 10px;

            .switch {
                width: 14px;
                height: 14px;
                background-image: url("@/assets/images/offAgreement.jpg");
                background-size: cover;
                margin-right: 5px;
                display: inline-block;
                position: relative;
                top: 2px;
                border-radius: 50%;
            }

            .on-switch {
                background: url("@/assets/images/true.png") #104cb5 no-repeat;
            }

            .agreement-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                display: inline;

                a {
                    color: #104cb5;
                }
            }
        }
    }
}
</style>
