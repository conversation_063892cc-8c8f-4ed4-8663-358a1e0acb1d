<template>
    <div class="loginPage">
        <img alt="" class="pageImg" src="@/assets/images/360jt1.png" />
        <div>
            <LoginLogic>
                <template #main="scope">
                    <div class="login">
                        <AppInput
                            v-model="scope.form.phone"
                            class="phone"
                            placeholder="请输入手机号"
                        >
                            <template #prefix>
                                <div class="phone-iconBox">
                                    <div class="phone-icon"></div>
                                </div>
                            </template>
                        </AppInput>
                        <AppButton class="login-btn" @click="onShow(scope)">立即申请</AppButton>
                        <div class="login-agreement">
                            <div
                                :class="{ 'on-switch': scope.switchAgreement }"
                                class="switch"
                                @click="scope.changeSwitch"
                            ></div>
                            <div class="agreement-text">
                                我已阅读并同意
                                <a
                                    v-for="item in scope.agreement ?? []"
                                    :key="item.key"
                                    :href="item.value"
                                    >{{ item.key }}</a
                                >
                            </div>
                        </div>
                    </div>
                    <van-popup v-model:show="popupShow" :close-on-click-overlay="false">
                        <div class="popup">
                            <div class="popup-close" @click="popupShow = false"></div>
                            <AppInput
                                v-model="scope.form.code"
                                :data-collect="scope.codeStatus ? 'sendCaptcha' : 'reSend'"
                                class="popup-code"
                                placeholder="请输入验证码"
                            >
                                <template #suffix>
                                    <div class="code" @click="scope.fetchCaptcha">
                                        <template v-if="!scope.codeLoading">
                                            <template v-if="scope.codeTime < 0"
                                                >{{ scope.codeStatus ? "获取验证码" : "重新获取" }}
                                            </template>
                                            <template v-else>{{ scope.codeTime }}S</template>
                                        </template>
                                    </div>
                                </template>
                            </AppInput>
                            <div class="popup-tips">收不到验证码？点击重新获取</div>
                            <AppButton class="popup-btn" data-collect="login" @click="scope.doLogin"
                                >立即申请
                            </AppButton>
                        </div>
                    </van-popup>
                </template>
            </LoginLogic>
        </div>
        <img alt="" class="pageImg" src="@/assets/images/360jt2.png" style="margin-top: -1px" />
    </div>
</template>
<script lang="ts" setup>
import LoginLogic from "@/gadget/Login/loginLogic.vue";
import AppInput from "@/components/App/Input.vue";
import AppButton from "@/components/App/Button.vue";
import { ref } from "vue";

const popupShow = ref<any>(false);

const onShow = (scope: any) => {
    if (scope.checkPhoneAndAgreement()) {
        popupShow.value = true;
    }
};
</script>
<style lang="less" scoped>
:deep(.van-popup--center) {
    border-radius: 5px;
    top: 40%;
}

.loginPage {
    background-color: #fff;

    .pageImg {
        width: 100vw;
        object-fit: fill;
    }

    .login {
        padding: 0 33px;
        background-size: 100% auto;
        background: url("@/assets/images/jtCenter.png") no-repeat;
        background-size: 100% 100%;
        display: table;
        content: "";
        margin-top: -1px;

        .phone {
            height: 60px;
            background: #f7f7f7;
            border-radius: 30px;
            border: 1px solid #c7c8c7;
            line-height: 60px;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            // color: #cccccc;
            padding: 0 27px;
            margin-bottom: 16px;
            margin-top: 14px;

            .phone-iconBox {
                .phone-icon {
                    background: url("@/assets/images/360jt3.png");
                    width: 14px;
                    height: 20px;
                }
            }
        }

        .login-btn {
            height: 58px;
            background: linear-gradient(90deg, #ffeac6 0%, #ffdea1 100%);
            border-radius: 30px;
            font-size: 22px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            line-height: 58px;
            color: #a12e16;
            margin-bottom: 10px;
        }

        .login-agreement {
            padding: 0 10px;
            margin-bottom: 25px;
            line-height: 22px;

            .switch {
                width: 14px;
                height: 14px;
                background-image: url("@/assets/images/offAgreement.jpg");
                background-size: cover;
                margin-right: 5px;
                display: inline-block;
                position: relative;
                top: 2px;
                border-radius: 50%;
            }

            .on-switch {
                background: url("@/assets/images/true.png") #ffdea1 no-repeat;
            }

            .agreement-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                display: inline;

                a {
                    color: #ffffff;
                }
            }
        }
    }

    .popup {
        height: 259px;
        background: #f4f4f4;
        width: 80vw;
        padding: 0px 22px 23px 22px;
        display: table;
        content: "";
        position: relative;

        .popup-close {
            width: 12px;
            height: 12px;
            position: absolute;
            background: url("@/assets/images/360jtClose.png");
            top: 11px;
            right: 27px;
        }

        .popup-code {
            margin-top: 40px;
            margin-bottom: 16px;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            // color: #cccccc;
            line-height: 22px;
            height: 68px;
            background: #ffffff;
            border-radius: 5px;
            border: 1px solid #c7c8c7;
            display: flex;
            align-items: center;

            .code {
                line-height: 55px;
                width: 100px;
                height: 55px;
                background: #cdced4;
                border-radius: 5px;
                font-size: 16px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                text-align: center;
                margin-right: 6px;
            }
        }

        .popup-tips {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #bababa;
            line-height: 17px;
            text-align: right;
        }

        .popup-btn {
            height: 61px;
            background: #52b37c;
            box-shadow: 0px 2px 4px 0px rgba(82, 179, 124, 0.5);
            border-radius: 30px;
            font-size: 22px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #ffffff;
            line-height: 30px;
            margin-top: 20px;
        }
    }
}
</style>
