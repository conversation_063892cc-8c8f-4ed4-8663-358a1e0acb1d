<template>
    <LoginLogic :agreementState="false">
        <template #main="scope">
            <div class="page">
                <header class="page-header clearfix">
                    <div class="header-card">
                        <div class="title">
                            <img class="logo" src="@/assets/images/home/<USER>" />
                            浩瀚有借
                        </div>
                        <div class="money-title">最高可借金额</div>
                        <div class="card-money">
                            <span> ¥ </span>
                            <span> 200,000 </span>
                            <span>元</span>
                        </div>
                        <div class="header-describe">
                            日息<span class="mainfont">0.02%起</span>(10000元1天最低低息2元)
                        </div>
                    </div>
                </header>
                <section class="page-main clearfix">
                    <div class="login clearfix">
                        <div class="login-limit">
                            <div class="limit-left">借款额度</div>
                            <div class="limit-right">
                                最高可借
                                <span>¥200,000</span>
                                元
                            </div>
                        </div>
                        <div class="login-limit">
                            <div class="limit-left">借款期限</div>
                            <div class="limit-right">可分1-12个月</div>
                        </div>
                        <!-- form -->
                        <div class="form">
                            <div
                                v-if="scope.loginStatus"
                                class="form-input"
                                style="margin-bottom: 20px"
                            >
                                <input
                                    v-model="scope.form.phone"
                                    placeholder="请输入手机号"
                                    type="text"
                                    maxlength="11"
                                />
                                <div class="form-iconBox">
                                    <img
                                        alt=""
                                        src="@/assets/images/phone.png"
                                        style="width: 14px; height: 18px"
                                    />
                                </div>
                            </div>

                            <div
                                v-if="pageConfig.idcard == 1"
                                class="form-input"
                                style="margin-bottom: 20px"
                            >
                                <input
                                    v-model="scope.form.name"
                                    placeholder="请输入姓名"
                                    type="text"
                                    maxlength="20"
                                />
                                <div class="form-iconBox">
                                    <img
                                        alt=""
                                        src="@/assets/images/name.png"
                                        style="width: 14px; height: 18px"
                                    />
                                </div>
                            </div>

                            <!-- 跳过验证码
                            <div v-if="scope.loginStatus" class="form-input">
                                <input
                                    v-model="scope.form.code"
                                    placeholder="请输入验证码"
                                    type="text"
                                />
                                <div class="form-iconBox">
                                    <img
                                        alt=""
                                        src="@/assets/images/code.png"
                                        style="width: 20px; height: 16px"
                                    />
                                </div>
                                <div class="form-code" @click="scope.fetchCaptcha">
                                    <template v-if="!scope.codeLoading">
                                        <template v-if="scope.codeTime < 0"
                                            >{{ scope.codeStatus ? "获取验证码" : "重发" }}
                                        </template>
                                        <template v-else>
                                            <span> {{ scope.codeTime }}s后重发 </span>
                                        </template>
                                    </template>
                                </div>
                            </div>
                        -->
                            <AppButton
                                class="global-btn form-submit"
                                data-collect="login"
                                type="primary"
                                @click="onSubmit(scope)"
                                >马上领取
                            </AppButton>
                        </div>
                        <!-- 协议 -->
                        <div class="detail-agreement">
                            <div
                                :class="{ active: scope?.switchAgreement }"
                                class="detail-agreement_mark"
                                @click="scope.changeSwitch"
                            ></div>
                            <span>我同意并授权</span>
                            <a
                                v-for="item in scope?.agreement ?? []"
                                :key="item.key"
                                :href="item.value"
                                >{{ item.key }}</a
                            >
                        </div>
                        <div class="advantage clearfix">
                            <div class="advantage-details">
                                <img
                                    class="advantage-detail-img"
                                    alt=""
                                    src="@/assets/images/home/<USER>"
                                />
                                <div>极速放款</div>
                            </div>
                            <img class="arrows" src="@/assets/images/home/<USER>" />
                            <div class="advantage-details">
                                <img
                                    class="advantage-detail-img"
                                    alt=""
                                    src="@/assets/images/home/<USER>"
                                />
                                <div>更加合规</div>
                            </div>
                            <img class="arrows" src="@/assets/images/home/<USER>" />
                            <div class="advantage-details">
                                <img
                                    class="advantage-detail-img"
                                    alt=""
                                    src="@/assets/images/home/<USER>"
                                />
                                <div>利率更低</div>
                            </div>
                        </div>
                    </div>
                </section>
                <footer class="page-footer">
                    <Statement></Statement>
                </footer>
            </div>
            <VanDialog
                v-model:show="visible"
                show-cancel-button
                title="温馨提示"
                @confirm="onSub(scope)"
                confirm-button-color="#354bdb"
            >
                <div class="dialog pop">
                    <span>请阅读并同意</span>
                    <a
                        v-for="item in scope?.agreement ?? []"
                        :key="item.value"
                        :href="item.value"
                        >{{ item.key }}</a
                    >
                </div>
            </VanDialog>
        </template>
    </LoginLogic>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import LoginLogic from "@/gadget/Login/loginLogic.vue";
import AppButton from "@/components/App/Button.vue";
import Statement from "@/gadget/statement/index.vue";
import { Dialog } from "vant";
import { useGlobalStore } from "@/stores/global";
import { getAppConfig } from "@/apis";

const store = useGlobalStore();
const pageConfig = ref({});

onMounted(() => {
    pageConfig.value = store.pageConfig;
    console.log(`store -->`, pageConfig.value, store.pageConfig);
});

const VanDialog = Dialog.Component;
const visible = ref<any>(false);
const onSubmit = (scope: any) => {
    if (!scope.switchAgreement) {
        visible.value = true;
    } else {
        scope.doLogin();
    }
};
const onSub = (scope: any) => {
    scope.changeSwitch();
    scope.doLogin();
};
</script>
<style lang="less" scoped>
.page {
    background-color: #f8f8f8;

    .page-header {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: 100vw 240px;
        box-sizing: border-box;
        min-height: 240px;
        margin-bottom: 16px;
        .header-card {
            background: url("@/assets/images/home/<USER>") no-repeat;
            background-size: 100% 191px;
            margin: 89px 20px 0 20px;
            height: 191px;
            box-shadow: 0px 3px 15px 0px #e9ecff;
            border-radius: 10px;
            padding: 26px 20px;
            .title {
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 22px;
                color: #333333;
                line-height: 30px;
                text-align: left;
                font-style: normal;
                display: flex;
                align-items: center;
                .logo {
                    width: 22px;
                    height: 22px;
                    margin-right: 5px;
                }
            }
            .money-title {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                margin-top: 12px;
            }
            .card-money {
                margin-top: 3px;
                font-family: D-DIN, D-DIN;
                font-weight: bold;
                font-size: 32px;
                color: #333333;
                line-height: 35px;
                text-align: left;
                font-style: normal;
                & span:nth-child(1) {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 20px;
                }
                & span:nth-child(3) {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 14px;
                }
            }
            .header-describe {
                margin-top: 20px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #999999;
                line-height: 18px;
                font-style: normal;
                span {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 14px;
                    color: #3c56e4;
                    line-height: 18px;
                    text-align: center;
                    font-style: normal;
                    margin: 0 4px;
                }
            }
        }
    }
    .page-main {
        .login {
            background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
            box-shadow: 0px 3px 15px 0px rgba(60, 86, 228, 0.14);
            border-radius: 10px;
            margin: 0 20px;
            padding: 20px;

            .login-limit {
                display: flex;
                margin-bottom: 12px;
                justify-content: space-between;

                .limit-left {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    line-height: 20px;
                    text-align: left;
                    font-style: normal;
                }

                .limit-right {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    line-height: 20px;
                    text-align: center;
                    font-style: normal;

                    span {
                        color: #3c56e4ff;
                    }
                }
            }

            .form {
                margin-top: 16px;
                .form-input {
                    position: relative;

                    input {
                        height: 44px;
                        background: #f3f3f3ff;
                        border-radius: 4px;
                        opacity: 1;
                        border: none;
                        width: 100%;
                        padding: 0 20px 0 62px;
                    }

                    .form-iconBox {
                        position: absolute;
                        top: 12px;
                        left: 0;
                        width: 53px;
                        height: 20px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-right: 1px solid #d8d8d8;
                    }

                    .form-code {
                        position: absolute;
                        top: 0;
                        right: 0;
                        text-align: center;
                        width: 102px;
                        height: 44px;
                        background: #3c56e4;
                        border-radius: 0px 4px 4px 0px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: #ffffff;
                        line-height: 44px;
                        font-style: normal;
                    }
                }

                .form-submit {
                    background: #3c56e4ff;
                    margin-top: 20px;
                }
            }

            .detail-agreement {
                margin-top: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #888888;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                position: relative;
                padding-left: 20px;

                &.pop {
                    padding-bottom: 20px;
                }

                .detail-agreement_mark {
                    height: 20px;
                    width: 20px;
                    position: absolute;
                    left: -4px;
                    top: 0px;
                    background-image: url("@/assets/images/home/<USER>");
                    background-repeat: no-repeat;
                    background-size: cover;

                    &::before {
                        display: none;
                        content: " ";
                        width: 100%;
                        height: 100%;
                        background-image: url("@/assets/images/home/<USER>");
                        background-repeat: no-repeat;
                        background-size: cover;
                        position: relative;
                    }

                    &.active::before {
                        // opacity: 1;
                        display: block;
                        // background-image: url('@/assets/images/已选@2x.png');
                    }
                }

                a {
                    color: #333333;
                }
            }
        }

        .advantage {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 24px;
            .arrows {
                width: 16px;
                height: 16px;
            }
            .advantage-details {
                text-align: center;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #555555;
                line-height: 17px;
                font-style: normal;
                display: flex;
                flex-direction: column;
                align-items: center;
                .advantage-detail-img {
                    width: 32px;
                    height: 32px;
                }
            }
        }
    }
}
.dialog {
    margin-top: 12px;
    font-size: 11px;
    font-weight: 400;
    color: #888888;
    line-height: 16px;
    margin-bottom: 4px;
    position: relative;
    padding: 0 20px;

    &.pop {
        padding-bottom: 20px;
    }

    .detail-agreement_mark {
        height: 20px;
        width: 20px;
        position: absolute;
        left: -4px;
        top: -3px;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: cover;

        &::before {
            display: none;
            content: " ";
            width: 100%;
            height: 100%;
            background-image: url("@/assets/images/home/<USER>");
            background-repeat: no-repeat;
            background-size: cover;
            position: relative;
        }

        &.active::before {
            // opacity: 1;
            display: block;
            // background-image: url('@/assets/images/已选@2x.png');
        }
    }

    a {
        color: #333333;
    }
}
</style>
