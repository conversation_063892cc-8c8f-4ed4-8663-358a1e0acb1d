<template>
    <div>
        <div class="page-header">
            <div
                :class="{
                    'header-slogan-quannei': store.channel === 'quannei' || store.channel === 'tttr'
                }"
                class="header-slogan"
            ></div>
        </div>
        <div class="page-main">
            <div class="page-main-bg"></div>
            <div class="page-main_content">
                <div class="detail_title">产品详情</div>
                <div v-if="store.channel === 'zhongtang'" class="detail_row">
                    <div class="row_label">年化</div>
                    <div class="row_value">IRR24%以内</div>
                </div>
                <div v-if="store.channel === 'tttr'" class="detail_row"></div>
                <div v-else class="detail_row">
                    <div class="row_label">日息</div>
                    <div class="row_value">
                        <span>0.02%起</span>
                        (10000元1天最低低息2元)
                    </div>
                </div>
                <div class="detail_row">
                    <div class="row_label">最高额度</div>
                    <div class="row_value">最高可借¥200,000元</div>
                </div>
                <div class="detail_row">
                    <div class="row_label">借款期限</div>
                    <div v-if="store.channel === 'quannei'" class="row_value">可分1-96个月</div>
                    <div v-else class="row_value">可分1-12个月</div>
                </div>
                <!--封装的login模块-->
                <GadGetLogin :beforeLogin="beforeLogin">
                    <template #login="scope">
                        <div v-if="scope.loginStatus" class="detail-yugu_top"></div>
                        <div class="detail-yugu">当前预估额度为：<span>151,326元</span></div>
                        <AppButton
                            class="global-btn"
                            data-collect="login"
                            type="primary"
                            @click="scope.doLogin"
                            >马上领取
                        </AppButton>
                        <van-dialog
                            v-model:show="visible"
                            show-cancel-button
                            title="温馨提示"
                            @confirm="onOkAgreement(scope.doLogin)"
                        >
                            <div class="detail-agreement pop">
                                <span>请阅读并同意</span>
                                <a v-for="item in data?.agreement ?? []" :href="item.value">{{
                                    item.key
                                }}</a>
                            </div>
                        </van-dialog>
                    </template>
                </GadGetLogin>

                <div class="detail-agreement">
                    <div
                        :class="{ active: agreement }"
                        class="detail-agreement_mark"
                        @click="agreement = !agreement"
                    ></div>
                    <span>我同意并授权</span>
                    <a v-for="item in data?.agreement ?? []" :key="item.key" :href="item.value">{{
                        item.key
                    }}</a>
                </div>
            </div>
        </div>
    </div>

    <div class="page-process">
        <div class="process-item">
            <div class="icon-1"></div>
            <p>填写资料</p>
        </div>
        <div class="process-arrow"></div>
        <div class="process-item">
            <div class="icon-2"></div>
            <p>专员审核</p>
        </div>
        <div class="process-arrow"></div>
        <div class="process-item">
            <div class="icon-3"></div>
            <p>放款提现</p>
        </div>
    </div>
    <Statement style="background-color: #f2f2f2"></Statement>
</template>
<script lang="ts" setup>
import { onBeforeMount, ref } from "vue";
import GadGetLogin from "@/gadget/Login/index.vue";
import AppButton from "@/components/App/Button.vue";
import { Dialog } from "vant";
import { getHomeDetail } from "@/apis/index";
import { useGlobalStore } from "@/stores/global";
import Statement from "@/gadget/statement/index.vue";

const store = useGlobalStore();
const VanDialog = Dialog.Component;

const data = ref<any>({});
const agreement = ref<boolean>(!!store.token);
const visible = ref<boolean>(false);
const beforeLogin = () => {
    return new Promise((resolve) => {
        if (!agreement.value) {
            visible.value = true;
        }
        resolve(agreement.value);
    });
};
const onOkAgreement = (doLogin: any) => {
    console.log(11);

    agreement.value = true;
    doLogin();
};

onBeforeMount(async () => {
    const result = await getHomeDetail(store.channel);
    data.value = result.data;
});
</script>

<style lang="less" scoped>
.page {
    background-color: #fff;
}

.page-header {
    height: 264px;
    background-image: url("@/assets/images/bg.png");
    background-repeat: no-repeat;
    background-size: cover;
    padding: 0 20px;
    // padding: 16px 20px;
    display: flex;
    align-items: center;
    padding-bottom: 80px;

    .header-slogan {
        height: 136px;
        background-image: url("@/assets/images/title.png");
        background-repeat: no-repeat;
        background-size: auto 100%;
        flex: 1;
    }

    .header-slogan-quannei {
        background-image: url("@/assets/images/title2.png");
    }

    .app-slogan {
        font-size: 24px;
        font-weight: 600;
        color: #ffffff;
        line-height: 33px;
    }

    .app-tips {
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
    }

    .app-edu {
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        line-height: 20px;
        margin-top: 6px;

        span {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            line-height: 35px;
            font-family: "Din";
        }
    }
}

.page-main {
    margin: 0 20px;
    margin-top: -70px;
    // background: linear-gradient(180deg, #E96638 0%, #E4967A 13%, #DDDDDD 100%);

    // filter: blur(14px);
    // box-shadow: 0 0 20px 2px #E4967A;
    position: relative;

    .page-main-bg {
        background: linear-gradient(180deg, #e96638 0%, #e4967a 13%, #dddddd 100%);
        border-radius: 20px;
        filter: blur(14px);
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .page-main_content {
        background-color: #fff;
        min-height: 200px;
        padding: 20px;
        position: relative;
        z-index: 2;
        border-radius: 8px;
        overflow: hidden;
    }

    .detail_title {
        height: 28px;
        line-height: 28px;
        font-size: 20px;
        font-weight: 600;
        color: #333333;
        line-height: 28px;
        padding-left: 12px;
        position: relative;
        margin-bottom: 4px;

        &::before {
            display: block;
            content: " ";
            position: absolute;
            left: 0;
            top: 50%;
            margin-top: -7px;
            width: 4px;
            height: 14px;
            background: #ee5533;
            border-radius: 2px;
        }
    }

    .detail_row {
        height: 44px;
        line-height: 18px;
        padding-bottom: 12px;
        padding-top: 14px;
        border-bottom: 1px solid #f6f6f6;
        display: flex;
        flex-direction: row;

        .row_label {
            color: #888888;
            font-size: 12px;
        }

        .row_value {
            flex: 1;
            font-size: 12px;
            font-weight: 400;
            color: #333333;
            line-height: 18px;
            text-align: right;

            span {
                color: #f13d2f;
                padding-right: 4px;
            }
        }
    }

    .detail-yugu_top {
        height: 16px;
    }

    .detail-yugu {
        margin-top: -6px;
        line-height: 20px;
        color: #555555;
        font-size: 12px;

        span {
            color: #e56334;
        }
    }

    .global-btn {
        margin-top: 16px;
    }
}

.detail-agreement {
    margin-top: 12px;
    font-size: 11px;
    font-weight: 400;
    color: #888888;
    line-height: 16px;
    margin-bottom: 4px;
    position: relative;
    padding: 0 20px;

    &.pop {
        padding-bottom: 20px;
    }

    .detail-agreement_mark {
        height: 20px;
        width: 20px;
        position: absolute;
        left: -4px;
        top: -3px;
        background-image: url("@/assets/images/未选@2x.png");
        background-repeat: no-repeat;
        background-size: cover;

        &::before {
            display: none;
            content: " ";
            width: 100%;
            height: 100%;
            background-image: url("@/assets/images/已选@2x.png");
            background-repeat: no-repeat;
            background-size: cover;
            position: relative;
        }

        &.active::before {
            // opacity: 1;
            display: block;
            // background-image: url('@/assets/images/已选@2x.png');
        }
    }

    a {
        color: #333333;
    }
}

.page-process {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-top: 16px;
    justify-content: center;
    padding-bottom: 20px;

    .process-item {
        .icon-1,
        .icon-2,
        .icon-3 {
            background-repeat: no-repeat;
            background-size: cover;
            width: 31px;
            height: 30px;
            margin: 0 auto;
        }

        .icon-1 {
            background-image: url("@/assets/images/p1.png");
        }

        .icon-2 {
            background-image: url("@/assets/images/p2.png");
        }

        .icon-3 {
            background-image: url("@/assets/images/p3.png");
        }

        p {
            font-size: 12px;
            font-weight: 400;
            color: #555555;
            line-height: 18px;
            height: 18px;
            margin-top: 4px;
        }
    }

    .process-arrow {
        background-image: url("@/assets/images/arrow.png");
        background-repeat: no-repeat;
        background-size: cover;
        width: 17px;
        height: 16px;
        margin: 0 10px;
        margin-bottom: 22px;
    }
}

.page-tips {
    background-color: inherit;
    padding: 20px;
    text-align: center;

    .tips-title {
        padding-top: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #888888;
        line-height: 18px;
    }

    .tips-copyright {
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #888888;
        line-height: 17px;
        margin: 16px 0;
    }

    .tips-desc {
        font-size: 10px;
        font-weight: 400;
        color: #a9a9a9;
        line-height: 16px;
        padding-bottom: 32px;
    }
}
</style>
