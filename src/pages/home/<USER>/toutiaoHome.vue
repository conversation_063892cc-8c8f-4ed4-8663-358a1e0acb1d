<template>
    <LoginLogic :agreementState="false">
        <template #main="scope">
            <div class="page">
                <section class="pageMain">
                    <div class="card">
                        <div class="estimatedAmount">
                            <img alt="" src="@/assets/images/homeIcon1.png" />
                            <span> 您的最高预估额度(元) </span>
                        </div>
                        <div class="amount">200,000</div>
                        <div class="describe">具体额度以实际审批为准</div>
                        <div class="card-tags">
                            <div>
                                <img alt="" src="@/assets/images/homeIcon3.png" />
                                <span>匹配合规机构</span>
                            </div>
                            <div>
                                <img alt="" src="@/assets/images/homeIcon2.png" />
                                <span>放款审批快</span>
                            </div>
                            <div>
                                <img alt="" src="@/assets/images/homeIcon4.png" />
                                <span>分期方式灵活</span>
                            </div>
                        </div>
                    </div>
                    <div class="login">
                        <div class="login-phoneBox form-row">
                            <AppInput v-model="scope.form.phone" placeholder="请输入手机号">
                            </AppInput>
                        </div>
                        <div class="agreementBox">
                            <div class="detail-agreement">
                                <div
                                    :class="{ cur: scope.switchAgreement }"
                                    class="circle"
                                    @click="scope.changeSwitch()"
                                    v-html="CheckCircleFill"
                                ></div>
                                <span>我同意并授权</span>
                                <a
                                    v-for="item in scope?.agreement ?? []"
                                    :key="item.key"
                                    :href="item.value"
                                    >{{ item.key }}</a
                                >
                            </div>
                        </div>
                        <AppButton
                            class="global-btn subBtn"
                            data-collect="login"
                            type="primary"
                            @click="onCheckout(scope)"
                            >立即申请
                        </AppButton>
                    </div>
                </section>
                <div class="page-tips">
                    <p>温馨提示：贷款额度、放款时间等以实际审批为准</p>
                    <p>贷款有风险，借贷需谨慎</p>
                    <p>请根据个人能力合理贷款、理性消费，避免逾期</p>
                    <p>公积金相关资料仅作为贷款服务验资证明</p>
                    <p>放款路径与公积金账号无关</p>
                    <p>贷款资金将由合作银行、持牌放贷机构等提供</p>
                    <p v-if="store.channel === 'toutiaolm'">综合年化利率7.2%-24%（单利）</p>
                    <p v-else>综合年化利率6%-24%（单利）</p>
                    <p>页面信息及服务由湖南浩瀚汇通互联网小额贷款有限公司提供</p>
                    <div>
                        <p>湖南浩瀚汇通互联网小额贷款有限公司</p>
                        <p>湘ICP备18003428号</p>
                    </div>
                </div>
            </div>
            <van-dialog
                v-model:show="visible"
                :show-confirm-button="false"
                @confirm="onOkAgreement(scope)"
            >
                <div class="agreementDialog">
                    <img alt="" src="@/assets/images/toutiaoClone.png" @click="visible = false" />
                    <div class="agreementDialog-title">服务协议与隐私保护</div>
                    <div class="agreementDialog-content">
                        <span> 为了更好地保护您的合法权益，请阅读并同意 </span>
                        <a v-for="item in scope?.agreement ?? []" :href="item.value">{{
                            item.key
                        }}</a>
                    </div>
                    <div class="agreementDialog-btn">
                        <div @click="visible = false">不同意</div>
                        <div @click="onOkAgreement(scope)">同意</div>
                    </div>
                </div>
            </van-dialog>
            <!-- 验证码弹窗 -->
            <van-dialog v-model:show="codeVisible" :show-confirm-button="false">
                <div class="code">
                    <div class="code-title">请输入短信验证码</div>
                    <img
                        alt=""
                        src="@/assets/images/toutiaoClone.png"
                        @click="codeVisible = false"
                    />
                    <div class="code-content">
                        <span>验证码已发送至{{ scope.form.phone }}</span>
                    </div>
                    <div class="code-box form-row">
                        <AppInput
                            v-model="scope.form.code"
                            formType="number"
                            placeholder="请输入验证码"
                        >
                            <template #suffix>
                                <AppButton
                                    :data-collect="scope.codeStatus ? 'sendCaptcha' : 'reSend'"
                                    font-size="12px"
                                    style="
                                        width: 100px;
                                        font-size: 14px;
                                        color: #e5712d;
                                        background-color: #f6f6f6;
                                    "
                                    type="primary"
                                    @click="scope.fetchCaptcha"
                                >
                                    <template v-if="!scope.codeLoading">
                                        <template v-if="scope.codeTime < 0"
                                            >{{ scope.codeStatus ? "获取验证码" : "重发" }}
                                        </template>
                                        <template v-else>
                                            <span style="color: #666">
                                                {{ scope.codeTime }}s后重发
                                            </span>
                                        </template>
                                    </template>
                                </AppButton>
                            </template>
                        </AppInput>
                    </div>
                    <AppButton
                        :disabled="!scope.form.code"
                        class="code-btn"
                        type="primary"
                        @click="scope.doLogin()"
                    >
                        立即申请
                    </AppButton>
                </div>
            </van-dialog>
        </template>
    </LoginLogic>
</template>
<script lang="ts" setup>
// 该页面用于头条融通
import { ref } from "vue";
import LoginLogic from "@/gadget/Login/loginLogic.vue";
import AppButton from "@/components/App/Button.vue";
import { Dialog, Toast } from "vant";
import { useGlobalStore } from "@/stores/global";
import { setTitle } from "@/utils/index";
import AppInput from "@/components/App/Input.vue";
import { CheckCircleFill } from "@/assets/svg/index";

setTitle("51信狐");
const store = useGlobalStore();
const VanDialog = Dialog.Component;

const data = ref<any>({});
const agreement = ref<boolean>(!!store.token);
const visible = ref<boolean>(false);
const codeVisible = ref<boolean>(false);

// 未勾选协议弹窗
const onOkAgreement = (scope: any) => {
    scope.changeSwitch();
    visible.value = false;
    codeVisible.value = true;
    // 发送验证码
    scope.fetchCaptcha();
};
// 检验手机号协议
const onCheckout = (scope: any) => {
    console.log(1);

    if (!scope.phoneState) {
        Toast.fail("手机号不合法");
    } else if (!scope.switchAgreement) {
        visible.value = true;
    } else {
        codeVisible.value = true;
        // 发送验证码
        scope.fetchCaptcha();
    }
};
</script>

<style lang="less" scoped>
.agreementDialog {
    padding: 31px;
    text-align: center;
    position: relative;

    > img {
        position: absolute;
        width: 16px;
        height: 16px;
        top: 13px;
        right: 16px;
    }

    .agreementDialog-title {
        font-size: 18px;
        font-weight: 600;
    }

    .agreementDialog-content {
        margin: 16px 0;
        font-size: 14px;
        text-align: initial;
        color: #444;

        a {
            color: #444;
        }
    }

    .agreementDialog-btn {
        display: flex;
        justify-content: space-between;

        div {
            width: 120px;
            height: 56px;
            border: 1px solid #f66801;
            border-radius: 30px;
            text-align: center;
            line-height: 56px;
            color: #f66801;

            &:nth-child(2) {
                border: none;
                color: #fff;
                background-color: #f66801;
            }
        }
    }
}

.code {
    padding: 0 20px;
    position: relative;
    padding: 32px;

    > img {
        width: 16px;
        height: 16px;
        position: absolute;
        right: 20px;
        top: 14px;
    }

    .code-title {
        font-size: 18px;
        text-align: center;
        font-weight: 600;
    }

    .code-content {
        text-align: center;
        font-size: 14px;
        margin-top: 10px;
        margin-bottom: 20px;
        color: #999;
    }

    .code-box {
        margin-bottom: 30px;

        :deep(.app-input) {
            height: 55px;
            font-size: 14px;
        }
    }

    .code-btn {
        // margin-bottom: 20px;
        height: 50px;
        border-radius: 20px;
        background-color: #e5712d;
    }
}

.page {
    background-color: #fff;

    .pageMain {
        height: 400px;
        // background-image: url("@/assets/images/homebgc.png");
        background: linear-gradient(
            180deg,
            #f66801,
            rgba(249, 156, 88, 0.66) 60%,
            hsla(0, 0%, 100%, 0)
        );
        padding: 24px 16px 0 16px;

        .login {
            margin-top: 24px;

            .login-phoneBox {
                background-color: #fff;
                border-radius: 10px;
            }
        }

        .card {
            height: 211px;
            background: #ffffff;
            box-shadow: 0px 2px 5px 0px rgba(238, 86, 52, 0.2);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;

            .estimatedAmount {
                margin-top: 20px;
                display: flex;
                justify-content: center;
                align-items: center;

                img {
                    width: 16.39px;
                    height: 15.03px;
                    margin-right: 5px;
                }

                span {
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                }
            }

            .amount {
                margin-top: 12px;
                width: 157px;
                height: 45px;
                text-align: center;
                line-height: 45px;
                font-size: 44px;
                font-family: DINAlternate-Bold, DINAlternate;
                font-weight: bold;
                color: #272d4b;
                background-image: url("@/assets/images/homebgc2.png");
                z-index: 99;
            }

            .describe {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 17px;
            }

            .card-tags {
                height: 50px;
                background: linear-gradient(180deg, #f6f9ea 0%, #fefffa 100%);
                border-radius: 0px 0px 8px 8px;
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 15px;

                div {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    img {
                        margin-right: 1px;
                    }

                    span {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #c09742;
                        line-height: 17px;
                    }
                }
            }
        }

        .agreementBox {
            padding: 0 5px;
        }

        .detail-agreement {
            margin-top: 12px;
            font-size: 11px;
            font-weight: 400;
            color: #888888;
            line-height: 16px;
            margin-bottom: 4px;
            position: relative;
            padding: 0 20px;
            padding-right: 0;

            &.pop {
                padding-bottom: 20px;
            }

            .circle {
                width: 14px;
                height: 14px;
                border-radius: 50%;
                border: 1px solid #999;
                position: absolute;
                left: 3px;
                top: 1px;

                :deep(svg) {
                    display: none;
                }
            }

            .cur {
                border: none;

                :deep(svg) {
                    width: 14px;
                    height: 14px;
                    display: block;
                    color: #f3792c;
                }
            }

            a {
                color: #c09742;
            }
        }

        .subBtn {
            margin-top: 21px;
            height: 50px;
            background: linear-gradient(177deg, #f7c13d 0%, #f3792c 100%, #f3792c 100%);
            border-radius: 25px;
        }
    }

    .page-tips {
        background-color: #fff;
        margin-top: 200px;
        text-align: center;

        p {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 18px;
        }

        > p {
            color: #cbad6f;
        }

        div {
            margin-top: 8px;

            p {
                font-size: 8px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 11px;
            }
        }
    }
}
</style>
