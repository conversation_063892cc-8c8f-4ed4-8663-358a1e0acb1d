<template>
    <div class="page">
        <section class="pageMain">
            <div class="card">
                <div class="estimatedAmount">
                    <img alt="" src="@/assets/images/homeIcon1.png" />
                    <span> 您的最高预估额度(元) </span>
                </div>
                <div v-if="store.channel === 'bafnag'" class="amount">200,000</div>
                <div v-else class="amount">198,000</div>
                <div class="describe">具体额度以实际审批为准</div>
                <div class="card-tags">
                    <!-- <div>
                        <img src="@/assets/images/homeIcon3.png" alt="" />
                        <span>匹配合规机构</span>
                    </div>
                    <div>
                        <img src="@/assets/images/homeIcon2.png" alt="" />
                        <span>放款审批快</span>
                    </div>
                    <div>
                        <img src="@/assets/images/homeIcon4.png" alt="" />
                        <span>分期方式灵活</span>
                    </div> -->
                </div>
            </div>
            <GadGetLogin :beforeLogin="beforeLogin">
                <template #login="scope">
                    <div class="agreementBox">
                        <div class="detail-agreement">
                            <div
                                :class="{ active: agreement }"
                                class="detail-agreement_mark"
                                @click="agreement = !agreement"
                            ></div>
                            <span>我同意并授权</span>
                            <a
                                v-for="item in data?.agreement ?? []"
                                :key="item.key"
                                :href="item.value"
                                >{{ item.key }}</a
                            >
                        </div>
                    </div>
                    <AppButton
                        class="global-btn subBtn"
                        data-collect="login"
                        type="primary"
                        @click="scope.doLogin"
                        >立即申请
                    </AppButton>
                    <van-dialog
                        v-model:show="visible"
                        show-cancel-button
                        title="温馨提示"
                        @confirm="onOkAgreement(scope.doLogin)"
                    >
                        <div class="detail-agreement pop">
                            <span>请阅读并同意</span>
                            <a v-for="item in data?.agreement ?? []" :href="item.value">{{
                                item.key
                            }}</a>
                        </div>
                    </van-dialog>
                </template>
            </GadGetLogin>
        </section>
        <div class="page-tips">
            <p>温馨提示：贷款额度、放款时间等以实际审批为准</p>
            <p>贷款有风险，借贷需谨慎</p>
            <p>请根据个人能力合理贷款、理性消费，避免逾期</p>
            <p>公积金相关资料仅作为贷款服务验资证明</p>
            <p>放款路径与公积金账号无关</p>
            <p>资金来源：湖南浩瀚汇通互联网小额贷款有限公司</p>
            <p>综合年化利率6%-24%（单利）</p>
            <p>页面信息及服务由湖南浩瀚汇通互联网小额贷款有限公司提供</p>
            <div>
                <p>湖南浩瀚汇通互联网小额贷款有限公司</p>
                <p>湘ICP备18003428号</p>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
// 该页面用于头条融通
import { onBeforeMount, onMounted, ref } from "vue";
import GadGetLogin from "@/gadget/Login/index.vue";
import AppButton from "@/components/App/Button.vue";
import { Dialog } from "vant";
import { getHomeDetail } from "@/apis/index";
import { useGlobalStore } from "@/stores/global";
import { sessionStorage, setTitle } from "@/utils/index";

setTitle("51信狐");
const store = useGlobalStore();
const VanDialog = Dialog.Component;

const data = ref<any>({});
const agreement = ref<boolean>(!!store.token);
const visible = ref<boolean>(false);
const beforeLogin = () => {
    return new Promise((resolve) => {
        if (!agreement.value) {
            visible.value = true;
        }
        resolve(agreement.value);
    });
};
const onOkAgreement = (doLogin: any) => {
    agreement.value = true;
    doLogin();
};

onBeforeMount(async () => {
    const result = await getHomeDetail(store.channel);
    data.value = result.data;
});
onMounted(() => {
    sessionStorage.removeItem(sessionStorage.getKey("token"));
    store.token = "";
});
</script>

<style lang="less" scoped>
.page {
    background-color: #fff;

    .pageMain {
        height: 487px;
        background-image: url("@/assets/images/homebgc.png");
        padding: 24px 16px 0 16px;

        .card {
            height: 211px;
            background: #ffffff;
            box-shadow: 0px 2px 5px 0px rgba(238, 86, 52, 0.2);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;

            .estimatedAmount {
                margin-top: 20px;
                display: flex;
                justify-content: center;
                align-items: center;

                img {
                    width: 16.39px;
                    height: 15.03px;
                    margin-right: 5px;
                }

                span {
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                }
            }

            .amount {
                margin-top: 12px;
                width: 157px;
                height: 45px;
                text-align: center;
                line-height: 45px;
                font-size: 44px;
                font-family: DINAlternate-Bold, DINAlternate;
                font-weight: bold;
                color: #272d4b;
                background-image: url("@/assets/images/homebgc2.png");
                z-index: 99;
            }

            .describe {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 17px;
            }

            .card-tags {
                height: 50px;
                background: linear-gradient(180deg, #f6f9ea 0%, #fefffa 100%);
                border-radius: 0px 0px 8px 8px;
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 15px;

                div {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    img {
                        margin-right: 1px;
                    }

                    span {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #c09742;
                        line-height: 17px;
                    }
                }
            }
        }

        .agreementBox {
            padding: 0 5px;
        }

        .detail-agreement {
            margin-top: 12px;
            font-size: 11px;
            font-weight: 400;
            color: #888888;
            line-height: 16px;
            margin-bottom: 4px;
            position: relative;
            padding: 0 20px;
            // padding-right: 0;

            &.pop {
                padding-bottom: 20px;
            }

            .detail-agreement_mark {
                height: 20px;
                width: 20px;
                position: absolute;
                left: -4px;
                top: -3px;
                background-image: url("@/assets/images/未选@2x.png");
                background-repeat: no-repeat;
                background-size: cover;

                &::before {
                    display: none;
                    content: " ";
                    width: 100%;
                    height: 100%;
                    background-image: url("@/assets/images/已选@2x.png");
                    background-repeat: no-repeat;
                    background-size: cover;
                    position: relative;
                }

                &.active::before {
                    // opacity: 1;
                    display: block;
                    // background-image: url('@/assets/images/已选@2x.png');
                }
            }

            a {
                color: #c09742;
            }
        }

        .subBtn {
            margin-top: 21px;
            height: 50px;
            background: linear-gradient(177deg, #f7c13d 0%, #f3792c 100%, #f3792c 100%);
            border-radius: 25px;
        }
    }

    .page-tips {
        background-color: #fff;
        margin-top: 200px;
        text-align: center;

        p {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 18px;
        }

        > p {
            color: #cbad6f;
        }

        div {
            margin-top: 8px;

            p {
                font-size: 8px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 11px;
            }
        }
    }
}
</style>
