import { onBeforeMount, ref, nextTick } from "vue";
import { certificationConfig } from "@/pages/auth/data";
import type { CertificationConfig, CommitInfoResult, From, IdCard } from "../../auth.d";
import { useGlobalStore } from "@/stores/global";
import {
    callBack,
    commitInfo,
    commitProducts,
    getProductInfo,
    getRecommend,
    getUserInfo
} from "@/apis";
import { Toast } from "vant";
import { useRoute, useRouter } from "vue-router";
import { reportConversion } from "@/actions/conversion";
import SchemaValidator, { type Rules } from "async-validator";
import { getIdCardInfo, validate } from "@/utils/procedure";
import { submitH5Commit } from "@/gadget/productList/submit";
import { sleep } from "@/utils";

export const useAuth = () => {
    // from配置
    const fromConfig = ref<CertificationConfig[]>([]);
    // 基本信息from
    const form = ref<From>({
        reserved_funds: "",
        social_security: "",
        real_estate: "",
        car: "",
        zhima: "",
        insurance: "",
        industry: "",
        credit_record: "",
        loan_amount: "",
        working_city: ""
    });
    // 实名信息from
    const idCard = ref<IdCard>({ name: "", idno: "" });
    // 城市弹窗
    const cityStatus = ref(false);
    // 实名信息弹窗
    const idCardStatus = ref(false);
    const store = useGlobalStore();
    // 缓存key名
    const localStorageKey = `authData_${store.uid}`;
    const router = useRouter();
    const route = useRoute();

    /**
     * 初始化
     */
    const init = async () => {
        //获取配置
        certificationConfig
            .sort((a, b) => b.sort - a.sort)
            .forEach((element: CertificationConfig, index) => {
                switch (element.group) {
                    case "certification":
                        break;
                    default:
                        fromConfig.value?.push(element);
                        break;
                }
            });

        // 提取缓存数据赋值表单
        const storageData = JSON.parse(localStorage.getItem(localStorageKey) ?? "{}");
        if (Object.keys(storageData).length) {
            form.value = storageData.form;
            idCard.value = storageData.idCard;
        } else {
            // 确保form.value已初始化
            form.value = {
                reserved_funds: "",
                social_security: "",
                real_estate: "",
                car: "",
                zhima: "",
                insurance: "",
                industry: "",
                credit_record: "",
                loan_amount: "",
                working_city: ""
            };
        }

        getCity();

        // 如果url有yxtoken字段，进入页面调用接口
        if (route.query?.yxtoken) {
            callBack(route.query._c);
        }
    };
    /**
     * 获取当前城市信息，给城市赋默认值
     */
    const getCity = async () => {
        try {
            const cityInfo = await getUserInfo();
            if (form.value) {
                form.value.working_city =
                    form.value.working_city || (cityInfo.data?.working_city ?? "天津市/天津市");
                console.log(`form.value -->`, cityInfo?.data?.working_city);
            }
        } catch (error) {
            console.log(`error -->`, error);
        }
    };

    /**
     * 选项点击事件
     * @param key 键
     */
    const onCardClick = (key: string) => {
        switch (key) {
            case "working_city":
                cityStatus.value = true;
                break;
            default:
                break;
        }
    };
    const $nuxtLink = (path: string, options: any) => {
        router.push({
            path,
            ...options
        });
    };

    /**
     * 把数据保存到localStorage里面
     */
    const saveData = () => {
        const data = {
            form: { ...form.value },
            idCard: { ...idCard.value }
        };
        // 保存到localStorage中
        localStorage.setItem(localStorageKey, JSON.stringify(data));
    };

    /**
     * 实名信息校验
     * @returns
     */
    const checkIdCard = async (): Promise<boolean> => {
        // 身份证校验规则
        const rules_idno: Rules = {
            idno: {
                type: "string",
                required: true,
                pattern:
                    /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|X)$/,
                message: "身份证不符合规范"
            }
        };
        //姓名校验规则
        const rules: Rules = {
            name: {
                type: "string",
                required: true,
                pattern:
                    /^[\u00B7\u3007\u3400-\u4DBF\u4E00-\u9FFF\uE000-\uF8FF\uD840-\uD8C0\uDC00-\uDFFF\uF900-\uFAFF]+$/,
                message: "请输入正确的姓名"
            }
        };
        // 性别生日校验规则
        const rules_age_sex: Rules = {
            sex: { type: "string", required: true, message: "请选择性别" },
            birthday: { type: "string", required: true, message: "请输入正确的生日" }
        };
        // 信息流规则组
        const rules_flow: Rules = {
            ...rules,
            ...rules_age_sex
        };
        // 非信息流规则组
        const rules_full: Rules = {
            ...rules,
            ...rules_idno
        };

        idCard.value.name = (idCard.value.name ?? "").replace(/\s*/g, "");
        idCard.value.idno = (idCard.value.idno ?? "").replace(/\s*/g, "");
        const { idno, name, sex, birthday } = idCard.value;
        const validator = new SchemaValidator(store.isFlow ? rules_flow : rules_full);
        const result: any = await validator
            .validate(store.isFlow ? { name, sex, birthday } : { idno, name })
            .catch(({ errors }) => {
                Toast.fail(errors[0]?.message ?? "校验失败！");
                return false;
            });
        return result;
    };

    /**
     * 校验数据
     */
    const check = async () => {
        const age = parseInt(idCard.value.birthday ?? "");
        const IdCardData = await checkIdCard();
        if (!IdCardData) {
            return false;
        } else if (
            (!/^[1-9]\d{1,2}$/.test(idCard.value.birthday ?? "") || age < 20 || age > 100) &&
            store.isFlow
        ) {
            Toast.fail("年龄不合法");
            return false;
        }

        // 校验好的实名信息赋值
        idCard.value = IdCardData as unknown as IdCard;
        return true;
    };

    /**
     * 提交
     */
    const onSubmit = async () => {
        idCardStatus.value = false;

        const submitCheck = await check();
        if (submitCheck) {
            saveData();
            const authArr = [];
            const { name, idno } = idCard.value;
            for (const key in form.value) {
                authArr.push({
                    key: key,
                    value: form.value[key]
                });
            }
            const other = getIdCardInfo(idCard.value);
            const toastIdCard = Toast.loading({
                duration: 0,
                message: "数据提交信息中...."
            });
            const commitInfoResult: CommitInfoResult = await commitInfo({
                other,
                auth: authArr
            });
            if (commitInfoResult.error) {
                Toast.fail({
                    duration: 800,
                    message: commitInfoResult.message
                });
            } else {
                toastIdCard.message = "提交成功！";
                const ret: any = commitInfoResult?.data;
                store.orderId = ret.orderId;
                // 指定渠道功能，撞库成功的产品全部推送
                if (store.isFlow) {
                    if (ret.list.length !== 0) {
                        const commitInfoResult: any = await commitProducts({
                            orderId: ret.orderId,
                            list: ret.list.slice(0, 1),
                            name: name,
                            idno
                        });
                        reportConversion("formSuccess");
                        if (commitInfoResult?.data) {
                            // 暂定
                        } else {
                            Toast.fail(commitInfoResult?.message);
                        }
                    }
                    // 销毁页面
                    $nuxtLink("/procedure/isFlowResult", { replace: true });
                } else if (ret.list.length === 0) {
                    // 正常匹配产品逻辑
                    $nuxtLink("/procedure/result", { replace: true, query: { status: "failure" } });
                } else if (ret.auth_type === "all") {
                    console.log(`type==all -->`, 111);
                    // $nuxtLink("/procedure/wholeProcessGoods", {
                    //     query: { order_id: ret.orderId, product_id: ret?.list[0] },
                    //     replace: true
                    // });

                    const res: any = await getProductInfo({
                        order_id: ret.orderId,
                        product_id: ret?.list[0]
                    });
                    localStorage.setItem("recommendList", JSON.stringify([res.data ?? {}]));
                    store.recommendList = [res.data ?? {}];

                    await submitH5Commit({
                        orderId: ret.orderId,
                        list: [ret?.list[0]],
                        name: idCard.value.name,
                        idno: idCard.value.idno
                    });
                } else {
                    // 调整不跳转机构产品推荐页，直接跳转结果页
                    // $nuxtLink("/procedure/recommend", {
                    //     query: { orderId: ret.orderId },
                    //     replace: true
                    // });

                    console.log(`1 -->`, 1);
                    try {
                        await sleep(1000);
                        const { data } = await getRecommend(ret.orderId);
                        localStorage.setItem("recommendList", JSON.stringify(data));
                        store.recommendList = data;
                        await submitH5Commit({
                            orderId: ret.orderId,
                            list: data?.map((item: any) => item.product_id),
                            name: idCard.value.name,
                            idno: idCard.value.idno
                        });
                    } catch (error) {}
                }
            }
            toastIdCard.clear();
        }
    };
    /**
     * 检验表单是否填写完成，进行下一步
     * @returns 布尔值
     */
    const onNext = async () => {
        for (let index = 0; index < fromConfig.value.length; index++) {
            const config: CertificationConfig = fromConfig.value[index];
            if (!form.value[config.keyword]) {
                Toast.fail("请补充" + config?.label);
                return;
            }
            if (config.rules?.length) {
                const c = await validate(form.value[config.keyword], config.rules).catch((msg) => {
                    Toast.fail(msg);
                });
                if (!c) {
                    return;
                }
            }
        }
        idCardStatus.value = true;
    };
    onBeforeMount(init);
    return {
        onCardClick,
        fromConfig,
        form,
        cityStatus,
        onSubmit,
        idCardStatus,
        idCard,
        store,
        saveData,
        onNext
    };
};
