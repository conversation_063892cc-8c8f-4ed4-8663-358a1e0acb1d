import type { CertificationConfig, GroupConfig } from "@/pages/auth/auth.d";
import { cityOptions } from "@/utils/areaData";

// 定义分组信息
export const groupConfig: GroupConfig = {
    loan_info: {
        id: "loan_info",
        title: "借款信息",
        sort: 1
    },
    basic_info: {
        id: "basic_info",
        title: "基本信息",
        sort: 2
    }
};

export const certificationConfig: CertificationConfig[] = [
    // 实名信息暂时不用
    {
        id: 1,
        icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth1.png",
        label: "实名信息",
        placeholder: "填写",
        value: "",
        innerTitle: "实名信息",
        required: true,
        children: [
            {
                keyword: "name",
                label: "真实姓名",
                placeholder: "请输入",
                type: "input",
                value: "",
                amount: 10000,
                rules: [
                    { required: true, message: "姓名不能为空" },
                    { pattern: "^[一-龥]+(·[一-龥]+)*$", message: "姓名格式不符合规范" }
                ]
            },
            {
                keyword: "idno",
                amount: 10000,
                label: "身份证号",
                placeholder: "请输入",
                type: "input",
                value: "",
                rules: [
                    { required: true, message: "身份证号不能为空" },
                    {
                        pattern:
                            "^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|X)$",
                        message: "身份证不符合规范"
                    }
                ]
            }
        ],
        keyword: "certification",
        sort: 0,
        group: "certification"
    },
    {
        icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth2.png",
        label: "公积金",
        value: "",
        innerTitle: "",
        formTitle: "您的公积金缴纳时长是？",
        required: true,
        type: "select",
        rules: [{ required: true, message: "请先选择公积金信息" }],
        options: [
            { label: "无公积金", value: "无公积金" },
            { label: "有公积金", value: "6个月以上" }
        ],
        keyword: "reserved_funds",
        sort: 4,
        group: "basic_info"
    },
    {
        icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth3.png",
        label: "社保",
        innerTitle: "",
        formTitle: "您的社保缴纳时长是？",
        required: true,
        type: "select",
        rules: [{ required: true, message: "请先选择社保信息" }],
        options: [
            { label: "无社保", value: "无社保" },
            { label: "有社保", value: "6个月以上" }
        ],
        keyword: "social_security",
        sort: 3,
        group: "basic_info"
    },
    {
        icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth4.png",
        label: "房产",
        innerTitle: "",
        formTitle: "您名下是否有房产？",
        required: true,
        type: "select",
        rules: [{ required: true, message: "请先选择房产信息" }],
        options: [
            { label: "有房", value: "有房（红本在手）" },
            { label: "无房", value: "无房" }
        ],
        keyword: "real_estate",
        sort: 6,
        group: "basic_info"
    },
    {
        icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth5.png",
        label: "车产",
        innerTitle: "",
        formTitle: "您名下是否有车产？",
        required: true,
        type: "select",
        rules: [{ required: true, message: "请先选择房产信息" }],
        options: [
            { label: "有车", value: "有车（全款）" },
            { label: "无车", value: "无车" }
        ],
        keyword: "car",
        sort: 5,
        group: "basic_info"
    },
    {
        icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth6.png",
        label: "芝麻分",
        innerTitle: "",
        formTitle: "您的芝麻信用分是？",
        required: true,
        type: "select",
        rules: [{ required: true, message: "请先选择芝麻分" }],
        options: [
            { label: "600分以下", value: 599 },
            { label: "600-649分", value: 649 },
            { label: "650-699分", value: 699 },
            { label: "700分以上", value: 700 }
        ],
        keyword: "zhima",
        sort: 3,
        group: "loan_info"
    },
    /*
    {
        icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth7.png",
        label: "保单",
        innerTitle: "",
        formTitle: "您是否有人身相关的保险？",
        required: true,
        type: "select",
        rules: [{ required: true, message: "请先选择投保信息" }],
        options: [
            { label: "投保两年以上", value: "投保两年以上", amount: 20000 },
            { label: "投保两年以下", value: "投保两年以下", amount: 10000 },
            { label: "无", value: "无" }
        ],
        keyword: "insurance",
        sort: 1,
        group: "basic_info"
    },
    */
    {
        icon: "https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/auth8.png",
        label: "职业",
        innerTitle: "",
        formTitle: "您的职业是？",
        required: true,
        type: "select",
        rules: [{ required: true, message: "请先选择职业信息" }],
        options: [
            { label: "上班族", value: "上班族", amount: 20000 },
            { label: "企业主", value: "企业主", amount: 50000 }
        ],
        keyword: "industry",
        sort: 2,
        group: "basic_info"
    },
    {
        icon: "https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/auth1.png",
        label: "信用记录",
        innerTitle: "",
        formTitle: "您的信用记录如何？",
        required: true,
        type: "select",
        rules: [{ required: true, message: "请先选择" }],
        options: [
            { label: "无逾期", value: "无逾期", amount: 5 },
            { label: "有逾期", value: "一年内有逾期", amount: 6 }
        ],
        keyword: "credit_record",
        sort: 2,
        group: "loan_info"
    },
    {
        icon: "https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/auth1.png",
        label: "工作城市",
        innerTitle: "",
        formTitle: "您的工作城市是？",
        required: true,
        type: "city",
        rules: [{ required: true, message: "请先选收入信息" }],
        keyword: "working_city",
        sort: 8,
        options: cityOptions
    },
    {
        icon: "https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/auth1.png",
        label: "贷款额度",
        formTitle: "期望的贷款额度",
        innerTitle: "",
        required: true,
        type: "select",
        rules: [
            { pattern: "^[1-9]{0,100}", message: "贷款额度格式不正确" },
            {
                range: [1, 1000],
                message: ["贷款额度区间为1万-1000万", "贷款额度区间为1万-1000万"]
            }
        ],
        unit: "万",
        col: 3,
        editConfig: {
            label: "自定义额度",
            unit: "万元",
            valueType: "number",
            placeholder: "请输入贷款额度"
        },
        options: [
            { value: 3, label: "3万" },
            { value: 5, label: "5万" },
            { value: 10, label: "10万" },
            { value: 20, label: "20万" }
        ],
        keyword: "loan_amount",
        sort: 1,
        group: "loan_info"
    }
];
