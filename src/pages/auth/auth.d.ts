declare namespace Auth {
    export interface Rules {
        pattern?: string;
        message?: string;
        range?: Array<number>;

        [property: string]: any;
    }

    export interface Options {
        label: string;
        value: any;
        amount?: any;
    }

    export interface CertificationConfig {
        options?: Options[] | any[];
        // 标题
        label: string;
        // 类型
        type?: string;
        // 输入框提示文字
        placeholder?: string;
        // 子项
        children?: {
            keyword: string;
            label: string;
            placeholder: string;
            type: string;
            value: string;
            amount: number;
            rules: any;
        }[];
        // 排序
        sort: number;
        // 输入框配置
        editConfig?: {
            label: "自定义额度";
            unit: "万元";
            valueType: "number";
            placeholder: string;
        };
        // 单位
        unit?: string;
        keyword: string;
        group?: string;
        rules?: any[];

        [property: string]: any;
    }

    export interface Form {
        reserved_funds: string;
        social_security: string;
        real_estate: string;
        car: string;
        zhima: string;
        insurance: string;
        industry: string;
        credit_record: string;
        loan_amount: string;
        working_city: string;
        [property: string]: any;
    }

    export interface IdCard {
        name: string;
        idno?: string;
        sex?: string;
        birthday?: string;
        [property: string]: any;
    }

    export interface CommitInfoResult {
        error?: string;
        message?: string;
        data: any;
    }

    // 添加分组配置接口定义
    export interface GroupConfigItem {
        id: string;
        title: string;
        sort: number;
    }

    export interface GroupConfig {
        [key: string]: GroupConfigItem;
    }
}

// 为兼容现有代码，也导出顶级命名空间的接口
export type CertificationConfig = Auth.CertificationConfig;
export type Form = Auth.Form;
export type From = Auth.Form; // 保留兼容性
export type IdCard = Auth.IdCard;
export type CommitInfoResult = Auth.CommitInfoResult;
export type GroupConfigItem = Auth.GroupConfigItem;
export type GroupConfig = Auth.GroupConfig;
