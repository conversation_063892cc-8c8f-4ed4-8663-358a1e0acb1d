<template>
    <div class="auth-page">
        <!-- 页面提示 -->
        <div class="page-tips">请填写真实信息，有助于快速下款</div>

        <!-- 工作城市选择器 -->
        <div class="city-selector">
            <div class="city-capsule">
                <div class="city-left">
                    <div class="location-icon">
                        <van-icon name="location" color="#2E6BF1" size="25" />
                    </div>
                    <span class="city-label">工作城市</span>
                </div>
                <div class="city-right" @click="onCardClick('working_city')">
                    <span class="city-value">{{
                        displayForm.working_city || hardcodedCity || "城市"
                    }}</span>
                    <span class="arrow-right"></span>
                </div>
            </div>

            <!-- 表单提示 -->
            <div class="form-tip">
                <span>请务必选择您当前长期居住的城市，否则将影响借款结果</span>
            </div>

            <City
                v-bind="getCityConfig()"
                v-model="displayForm.working_city"
                v-model:cityStatus="cityStatus"
                @onChange="onCityChange"
                v-if="formReady"
            ></City>
            <!-- 添加一个备用隐藏的City组件，确保钩子被正确调用 -->
            <City
                v-bind="getCityConfig()"
                v-model="hardcodedCity"
                v-model:cityStatus="cityStatus"
                @onChange="onCityChange"
                v-else
                style="display: none"
            ></City>
        </div>

        <!-- 动态生成表单分组 -->
        <div v-if="formReady">
            <div
                v-for="groupId in Object.keys(groupedFields)"
                :key="groupId"
                class="info-group"
                v-show="formSteps[groupId]"
            >
                <div class="info-group-header">
                    <span>{{ getGroupTitle(groupId) }}</span>
                </div>

                <!-- 表单项列表 -->
                <div class="info-group-content">
                    <van-collapse v-model="activeCollapseName" accordion>
                        <!-- 动态生成表单项 -->
                        <template v-for="field in groupedFields[groupId]" :key="field.keyword">
                            <!-- 普通选项字段 -->
                            <van-collapse-item
                                v-if="displayField(field.keyword)"
                                :name="field.keyword"
                                :disabled="!fieldEnabled[field.keyword]"
                                :ref="(el) => (itemRefs[field.keyword] = el)"
                            >
                                <template #title>
                                    <div class="form-cell-title">
                                        <div class="form-cell-label">{{ field.label }}</div>
                                        <div class="form-cell-value">
                                            <span v-if="displayForm[field.keyword]">
                                                {{
                                                    field.keyword === "loan_amount"
                                                        ? `${displayForm[field.keyword]}万`
                                                        : getOptionLabel(
                                                              field,
                                                              displayForm[field.keyword]
                                                          )
                                                }}
                                            </span>
                                            <span v-else class="placeholder"
                                                >请选择{{ field.label }}</span
                                            >
                                        </div>
                                    </div>
                                </template>
                                <div class="form-cell-content">
                                    <div class="option-blocks">
                                        <div
                                            v-for="option in field.options"
                                            :key="option.value"
                                            class="option-block"
                                            :class="{
                                                active: displayForm[field.keyword] === option.value
                                            }"
                                            @click="selectOption(field.keyword, option.value)"
                                        >
                                            {{ option.label }}
                                        </div>
                                    </div>
                                </div>
                            </van-collapse-item>
                        </template>
                    </van-collapse>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-btn-container" v-if="formReady">
            <div
                class="submit-btn"
                :class="{ 'submit-btn-active': isFormComplete }"
                @click="onNext"
            >
                立即申请
            </div>
        </div>

        <!-- 身份信息弹窗 -->
        <van-popup v-model:show="idCardStatus" position="bottom" @clickCloseIcon="onCardClick">
            <div class="idCard">
                <img src="@/assets/images/idCard.png" alt="" class="idCard-img" />
                <div class="content clearfix">
                    <!-- 姓名 -->
                    <div class="form-row">
                        <AppInput v-model="idCard.name" placeholder="姓名">
                            <template #prefix>
                                <div class="form-icon">
                                    <div class="icon-name"></div>
                                </div>
                            </template>
                        </AppInput>
                    </div>
                    <template v-if="store.isFlow">
                        <!-- 年龄 -->
                        <div class="form-row">
                            <AppInput
                                v-model="idCard.birthday"
                                placeholder="年龄"
                                formType="number"
                            >
                                <template #prefix>
                                    <div class="form-icon">
                                        <div class="icon-birthday"></div>
                                    </div>
                                </template>
                            </AppInput>
                        </div>
                        <!-- 性别 -->
                        <div class="form-row">
                            <AppInput v-model="idCard.sex" placeholder="性别">
                                <template #content>
                                    <van-radio-group
                                        style="padding-left: 12px"
                                        v-model="idCard.sex"
                                        direction="horizontal"
                                    >
                                        <van-radio name="男" checked-color="#44C6B3FF"
                                            >男</van-radio
                                        >
                                        <van-radio name="女" checked-color="#44C6B3FF"
                                            >女</van-radio
                                        >
                                    </van-radio-group>
                                </template>
                                <template #prefix>
                                    <div class="form-icon">
                                        <div class="icon-sex"></div>
                                    </div>
                                </template>
                            </AppInput>
                        </div>
                    </template>
                    <template v-else>
                        <!-- 身份证 -->
                        <div class="form-row">
                            <AppInput v-model="idCard.idno" placeholder="身份证号">
                                <template #prefix>
                                    <div class="form-icon">
                                        <div class="icon-idno"></div>
                                    </div>
                                </template>
                            </AppInput>
                        </div>
                    </template>
                    <AppButton
                        class="global-btn"
                        type="primary"
                        data-collect="completeSubmit"
                        @click="onSubmit"
                        class-name="throttle"
                    >
                        立即查看额度
                    </AppButton>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script setup lang="ts">
// 移除未使用的组件导入
// import Select from "@/components/Auth/Select.vue";
// import ZhiMa from "@/components/Auth/ZhiMa.vue";
import City from "@/components/Auth/City.vue";
import AppInput from "@/components/App/Input.vue";
import AppButton from "@/components/App/Button.vue";
import { ref, onMounted, onBeforeUnmount, nextTick, computed, watch } from "vue";
import { useAuth } from "@/pages/auth/hook/useAuth/useAuth";
import { certificationConfig, groupConfig } from "@/pages/auth/data";
import type { CertificationConfig, GroupConfigItem, From } from "@/pages/auth/auth.d";

// 新增一个响应式变量来存储当前激活的折叠面板名称（手风琴模式下应为单个字符串）
const activeCollapseName = ref("");
// 存储每个折叠项的引用
const itemRefs = ref<Record<string, any>>({});

// 默认表单数据
const defaultForm: From = {
    reserved_funds: "",
    social_security: "",
    real_estate: "",
    car: "",
    zhima: "",
    insurance: "",
    industry: "",
    credit_record: "",
    loan_amount: "",
    working_city: ""
};

// 硬编码城市 - 用于解决城市不显示的问题
const hardcodedCity = ref<string>("");

// 安全的表单数据
const formReady = ref(false);
const displayForm = ref<From>({ ...defaultForm });

const { onCardClick, cityStatus, form, onSubmit, idCardStatus, idCard, store, saveData, onNext } =
    useAuth();

// 表单步骤控制
const formSteps = ref<Record<string, boolean>>({});

// 初始化表单步骤
Object.keys(groupConfig).forEach((groupId) => {
    const groupKey = groupId as keyof typeof groupConfig;
    const group = groupConfig[groupKey];
    formSteps.value[groupId] = group.sort === 1; // 只显示排序第一的组
});

// 从配置自动生成表单字段信息
const fieldConfigs = computed(() => {
    // 过滤并排序表单字段
    return certificationConfig
        .filter(
            (item) =>
                item.keyword !== "working_city" && item.keyword !== "certification" && item.group
        )
        .sort((a, b) => {
            // 先按组排序
            const groupA =
                a.group && groupConfig[a.group]
                    ? groupConfig[a.group]
                    : ({ sort: 999 } as GroupConfigItem);

            const groupB =
                b.group && groupConfig[b.group]
                    ? groupConfig[b.group]
                    : ({ sort: 999 } as GroupConfigItem);

            if (groupA.sort !== groupB.sort) {
                return groupA.sort - groupB.sort;
            }

            // 组内按字段排序
            return (a.sort || 0) - (b.sort || 0);
        });
});

// 获取表单字段关键字列表
const fieldKeywords = computed(() => {
    return fieldConfigs.value.map((item) => item.keyword);
});

// 自动生成字段依赖关系
const fieldDependencies = computed(() => {
    const dependencies: Record<string, string | null> = {};

    // 按组排序所有字段
    const sortedFields = [...fieldConfigs.value].sort((a, b) => {
        // 先按组排序
        const groupA =
            a.group &&
            typeof a.group === "string" &&
            groupConfig[a.group as keyof typeof groupConfig]
                ? groupConfig[a.group as keyof typeof groupConfig]
                : { sort: 999 };

        const groupB =
            b.group &&
            typeof b.group === "string" &&
            groupConfig[b.group as keyof typeof groupConfig]
                ? groupConfig[b.group as keyof typeof groupConfig]
                : { sort: 999 };

        if (groupA.sort !== groupB.sort) {
            return groupA.sort - groupB.sort;
        }

        // 组内按字段排序
        return (a.sort || 0) - (b.sort || 0);
    });

    // 构建依赖关系 - 每个字段依赖于排序中的前一个字段
    sortedFields.forEach((field, index) => {
        if (index === 0) {
            // 第一个字段没有依赖
            dependencies[field.keyword] = null;
        } else {
            // 其他字段依赖于前一个字段
            dependencies[field.keyword] = sortedFields[index - 1].keyword;
        }
    });

    return dependencies;
});

// 分组字段 - 从配置中获取分组信息
const groupedFields = computed(() => {
    // 创建分组结果对象
    const result: Record<string, CertificationConfig[]> = {};

    // 初始化每个分组
    Object.keys(groupConfig).forEach((groupId) => {
        result[groupId] = [];
    });

    // 将字段分配到对应的组
    fieldConfigs.value.forEach((field) => {
        const groupId = field.group || "";
        if (result[groupId]) {
            result[groupId].push(field);
        }
    });

    return result;
});

// 获取分组标题
const getGroupTitle = (groupId: string): string => {
    return groupId in groupConfig ? groupConfig[groupId as keyof typeof groupConfig].title : "";
};

// 根据选项的value值获取对应的label
const getOptionLabel = (field: CertificationConfig, value: any): string => {
    if (!field.options || !Array.isArray(field.options)) {
        return value; // 如果没有选项或选项不是数组，直接返回值
    }

    const option = field.options.find((opt) => opt.value === value);
    return option ? option.label : value; // 如果找到对应选项则返回label，否则返回原值
};

// 字段显示控制 - 动态生成
const fieldVisibility = ref<Record<string, boolean>>({});

// 监听字段可见性变化，自动展开新显示的字段
watch(
    fieldVisibility,
    (newValues, oldValues) => {
        // 查找新变为可见的字段
        Object.keys(newValues).forEach((fieldName) => {
            // 如果字段从不可见变为可见，则自动展开它
            if (newValues[fieldName] === true && (!oldValues || oldValues[fieldName] !== true)) {
                // 如果字段可用，则自动展开
                if (fieldEnabled.value[fieldName]) {
                    // 使用activeCollapseName来控制展开状态
                    nextTick(() => {
                        activeCollapseName.value = fieldName;
                    });
                }
            }
        });
    },
    { deep: true }
);

// 字段是否可用 - 动态生成
const fieldEnabled = ref<Record<string, boolean>>({});

// 初始化字段可见性和可用性
const initFieldsState = () => {
    // 初始化所有字段为不可见和不可用
    fieldConfigs.value.forEach((field) => {
        fieldVisibility.value[field.keyword] = false;
        fieldEnabled.value[field.keyword] = false;
    });

    // 第一个字段默认可见和可用
    if (fieldConfigs.value.length > 0) {
        const firstField = fieldConfigs.value[0].keyword;
        fieldVisibility.value[firstField] = true;
        fieldEnabled.value[firstField] = true;
        // 自动展开第一个字段
        nextTick(() => {
            activeCollapseName.value = firstField;
        });
    }
};

// 检查某个字段是否应该显示
const displayField = (fieldName: string): boolean => {
    return fieldVisibility.value[fieldName] === true;
};

// 初始化表单字段的显示状态
const initFormFieldsVisibility = () => {
    // 始终确保displayForm已初始化
    if (!displayForm.value) {
        displayForm.value = { ...defaultForm };
        console.log("Initializing displayForm with default values");
    }

    // 确保working_city有值
    if (!displayForm.value.working_city && hardcodedCity.value) {
        displayForm.value.working_city = hardcodedCity.value;
        console.log("Setting displayForm.working_city from hardcodedCity:", hardcodedCity.value);
    }

    // 初始化字段状态
    initFieldsState();

    // 初始化所有分组为不可见
    Object.keys(groupConfig).forEach((groupId) => {
        formSteps.value[groupId] = false;
    });

    // 默认显示第一个分组
    if (Object.keys(groupConfig).length > 0) {
        const firstGroupId = Object.keys(groupConfig)[0];
        formSteps.value[firstGroupId] = true;
    }

    // 遍历字段，根据依赖关系和已有值来设置可见性
    let lastFieldWithValue = null;
    let firstVisibleField = null;

    for (const field of fieldConfigs.value) {
        const fieldName = field.keyword;
        // 使用displayForm检查字段
        const hasValue = !!displayForm.value[fieldName];
        const dependency = fieldDependencies.value[fieldName];

        // 如果字段有值或者它的依赖项有值，则显示此字段
        if (hasValue || (dependency && displayForm.value[dependency])) {
            fieldVisibility.value[fieldName] = true;
            fieldEnabled.value[fieldName] = true;

            // 记录第一个可见字段（如果尚未记录）
            if (!firstVisibleField) {
                firstVisibleField = fieldName;
            }

            // 记录最后一个有值的字段
            if (hasValue) {
                lastFieldWithValue = fieldName;
            }

            // 确定该字段所属的分组，并显示该分组
            Object.keys(groupConfig).forEach((groupId) => {
                if (groupedFields.value[groupId].some((f) => f.keyword === fieldName)) {
                    formSteps.value[groupId] = true;
                }
            });
        }
    }

    // 如果有部分字段有值，则展开最后一个有值的字段的下一个字段
    if (
        lastFieldWithValue &&
        lastFieldWithValue !== fieldKeywords.value[fieldKeywords.value.length - 1]
    ) {
        const lastIndex = fieldKeywords.value.indexOf(lastFieldWithValue);
        if (lastIndex < fieldKeywords.value.length - 1) {
            const nextField = fieldKeywords.value[lastIndex + 1];
            // 只有当下一个字段已经可见和可用时才展开
            if (fieldVisibility.value[nextField] && fieldEnabled.value[nextField]) {
                nextTick(() => {
                    activeCollapseName.value = nextField;
                });
            }
        }
    } else if (firstVisibleField) {
        // 如果没有字段有值，则展开第一个可见字段
        nextTick(() => {
            activeCollapseName.value = firstVisibleField;
        });
    }
};

// 获取城市选择器配置
const getCityConfig = () => {
    // 使用类型断言确保返回的对象包含所需的属性
    const config = (certificationConfig.find((item) => item.keyword === "working_city") ||
        {}) as CertificationConfig;

    // 确保返回的config包含options和其他必要属性
    if (!config.options) {
        console.warn("City config missing options", config);
        // 提供默认值以防止组件错误
        config.options = [];
    }

    return config;
};

// 表单完整性检查
const isFormComplete = computed(() => {
    // 使用displayForm检查表单完整性
    if (!formReady.value) {
        return false;
    }

    // 检查必填字段是否都已填写
    const requiredFields = Object.keys(fieldVisibility.value).filter(
        (key) => fieldVisibility.value[key] === true
    );
    return requiredFields.every((field) => !!displayForm.value[field]);
});

// 城市更改处理函数
const onCityChange = () => {
    console.log("City changed to:", displayForm.value.working_city);

    // 确保form与displayForm同步
    if (form && form.value) {
        form.value.working_city = displayForm.value.working_city;
    }

    // 同步更新hardcodedCity，确保UI立即显示
    hardcodedCity.value = displayForm.value.working_city;

    // 确保视图更新
    nextTick(() => {
        console.log("City updated in UI:", displayForm.value.working_city);
    });

    // 保存数据
    saveData();

    // 如果working_city是第一个字段，继续处理下一个字段
    onFieldComplete("working_city");
};

// 字段完成处理
const onFieldComplete = (fieldName: string) => {
    // 保存数据
    saveData();

    // 获取当前字段在顺序中的位置
    const currentIndex = fieldKeywords.value.indexOf(fieldName);

    // 如果不是最后一个字段，则处理下一个字段
    if (currentIndex < fieldKeywords.value.length - 1) {
        const nextField = fieldKeywords.value[currentIndex + 1];

        // 设置下一个字段可见和可用
        fieldVisibility.value[nextField] = true;
        fieldEnabled.value[nextField] = true;

        // 确定下一个字段所属的分组，并显示该分组
        Object.keys(groupConfig).forEach((groupId) => {
            if (groupedFields.value[groupId].some((f) => f.keyword === nextField)) {
                formSteps.value[groupId] = true;
            }
        });

        // 自动打开下一个表单项
        nextTick(() => {
            activeCollapseName.value = nextField;
        });
    }
};

// 选择选项
const selectOption = (fieldName: string, value: any) => {
    // 更新我们的本地安全副本
    displayForm.value[fieldName] = value;

    // 如果原始form存在，也更新它
    if (form && form.value) {
        form.value[fieldName] = value;
    }

    saveData();
    onFieldComplete(fieldName);
};

// 初始化拦截器 - 已移植到Result页面
const initInterceptor = () => {
    // 此处保留空函数，避免其他代码调用时出错
};

// 监听form变化，安全地更新displayForm
watch(
    () => form,
    (newForm) => {
        if (newForm && newForm.value) {
            console.log("Form changed:", newForm.value);
            // 复制form的值到displayForm
            displayForm.value = { ...defaultForm, ...newForm.value };
            formReady.value = true;
        }
    },
    { immediate: true, deep: true }
);

// 更精确地监听form.value.working_city的变化
watch(
    () => form?.value?.working_city,
    (newCity) => {
        if (newCity) {
            console.log("Working city changed:", newCity);
            displayForm.value.working_city = newCity;
        }
    },
    { immediate: true }
);

// 监听displayForm.working_city变化，确保所有相关值都同步更新
watch(
    () => displayForm.value.working_city,
    (newCity) => {
        if (newCity) {
            console.log("displayForm working_city changed:", newCity);
            // 确保hardcodedCity同步
            hardcodedCity.value = newCity;
            // 确保form.value.working_city同步
            if (form && form.value) {
                form.value.working_city = newCity;
            }
            // 保存数据
            saveData();
        }
    }
);

// 直接初始化城市数据
const initCityDirectly = async () => {
    try {
        // 尝试从localStorage直接获取城市数据
        const localStorageKey = `authData_${store.uid}`;
        const storageData = JSON.parse(localStorage.getItem(localStorageKey) ?? "{}");

        if (storageData.form?.working_city) {
            console.log("Found city in localStorage:", storageData.form.working_city);
            hardcodedCity.value = storageData.form.working_city;
            displayForm.value.working_city = storageData.form.working_city;
            if (form && form.value) {
                form.value.working_city = storageData.form.working_city;
            }

            // 触发城市组件onChange事件，确保UI更新
            nextTick(() => {
                onCityChange();
            });

            formReady.value = true;
            return true;
        }

        // 尝试直接从API获取城市信息
        const { getUserInfo } = await import("@/apis");
        const cityInfo = await getUserInfo();

        if (cityInfo.data?.working_city) {
            console.log("Got city from API:", cityInfo.data.working_city);
            hardcodedCity.value = cityInfo.data.working_city;
            displayForm.value.working_city = cityInfo.data.working_city;
            if (form && form.value) {
                form.value.working_city = cityInfo.data.working_city;
            }

            // 触发城市组件onChange事件，确保UI更新
            nextTick(() => {
                onCityChange();
            });

            formReady.value = true;
            return true;
        }

        // 如果都没有获取到，使用默认城市
        hardcodedCity.value = "天津市/天津市";
        displayForm.value.working_city = "天津市/天津市";
        if (form && form.value) {
            form.value.working_city = "天津市/天津市";
        }

        // 触发城市组件onChange事件，确保UI更新
        nextTick(() => {
            onCityChange();
        });

        console.log("Using default city: 天津市/天津市");
        return true;
    } catch (error) {
        console.error("Error initializing city data:", error);
        hardcodedCity.value = "天津市/天津市";
        return false;
    }
};

onMounted(async () => {
    // 尝试直接初始化城市数据
    await initCityDirectly();

    // 确保DOM已加载完成
    nextTick(() => {
        // 强制设置formReady为true，不再依赖form对象
        formReady.value = true;

        // 初始化表单字段的可见性
        initFormFieldsVisibility();

        // 延迟初始化以确保页面完全加载
        setTimeout(() => {
            initInterceptor();
        }, 300);
    });
});

onBeforeUnmount(() => {
    // 清理资源 - 挽留弹窗已移除

    // 确保页面退出时恢复滚动
    document.body.style.overflow = "";
    document.body.style.position = "";
    document.body.style.width = "";
});
</script>

<style scoped lang="less">
@import "auth";

.auth-page {
    background-color: #f5f7fa;
    min-height: 100vh;
    padding-bottom: 80px;
}

.page-tips {
    padding: 15px;
    font-size: 12px;
}

.city-selector {
    padding: 16px;
    margin: 0 15px 15px;
    border-radius: 10px;
    background-color: #fff;

    .city-capsule {
        display: flex;
        width: 100%;
        height: 60px;
        border-radius: 10px;
        overflow: hidden;
        font-size: 15px;
        font-weight: 500;
    }

    .city-left {
        display: flex;
        align-items: center;
        background: #f5f5f5;
        padding: 0 15px;
        flex: 1;
        height: 100%;

        .location-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
        }
    }

    .city-right {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #2e6bf1;
        padding: 0 15px;
        flex: 1;
        height: 100%;

        .city-value {
            margin-right: 4px;
            color: #fff;
        }

        .arrow-right {
            width: 16px;
            height: 16px;
            background: url('data:image/svg+xml;utf8,<svg t="1687309340548" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10164" width="200" height="200"><path d="M761.056 532.128c0.512-0.992 1.344-1.824 1.792-2.848 8.8-18.304 5.92-40.704-9.664-55.424L399.936 139.744c-19.264-18.208-49.632-17.344-67.872 1.888-18.208 19.264-17.376 49.632 1.888 67.872l316.96 299.84-315.712 304.288c-19.072 18.4-19.648 48.768-1.248 67.872 9.408 9.792 21.984 14.688 34.56 14.688 12 0 24-4.48 33.312-13.44l350.048-337.376c0.672-0.672 0.928-1.6 1.6-2.304 0.512-0.48 1.056-0.832 1.568-1.344C757.76 538.88 759.2 535.392 761.056 532.128z" fill="%23FFFFFF" p-id="10165"></path></svg>')
                center/contain no-repeat;
        }
    }
}

.form-tip {
    margin-top: 16px;
    font-size: 12px;
    color: #ff9734;
    text-align: center;
}

.form-group {
    background: #fff;
    border-radius: 8px;
    margin: 12px 15px;
    padding: 5px 0;

    .form-group-title {
        color: #1a73e8;
        font-size: 16px;
        font-weight: 500;
        padding: 10px 15px;
        text-align: center;
        position: relative;

        &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 15%;
            width: 70%;
            height: 1px;
            background-color: #f0f0f0;
        }
    }
}

.form-item {
    padding: 15px;

    .form-label {
        font-size: 15px;
        color: #333;
        margin-bottom: 8px;
    }
}

.submit-btn-container {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 20px;
    display: flex;
    justify-content: center;
}

.submit-btn {
    width: 90%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    border-radius: 25px;
    background: #cccccc;
}

.submit-btn-active {
    background: linear-gradient(90deg, #4080ff, #1a73e8);
    box-shadow: 0 5px 15px rgba(26, 115, 232, 0.3);
}

/* 保留原有的idCard弹窗样式 */
.idCard {
    .idCard-img {
        width: 100%;
    }
    .form-row {
        margin-bottom: 15px;
    }
}

/* 挽留弹窗样式已移植到Result.vue */

.info-group {
    margin: 15px;
    border-radius: 10px;
    // background-color: #fff;
    overflow: hidden;
    margin-bottom: 20px;
}

.info-group-header {
    margin: 0 8px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    color: #2e6bf1;
    border-radius: 10px 10px 0 0;
    background: linear-gradient(180deg, #ffffff 0%, #f2f5ff 100%);
    position: relative;

    // &::after {
    //     content: "";
    //     position: absolute;
    //     left: 0;
    //     bottom: 0;
    //     width: 100%;
    //     height: 1px;
    //     background-color: #e8e8e8;
    //     transform: scaleY(0.5);
    // }
}

.info-group-content {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;

    // 定制Vant折叠面板样式
    :deep(.van-collapse) {
        background-color: transparent;
        border: none;

        .van-collapse-item {
            margin-bottom: 1px;

            &::after {
                display: none;
            }

            // 第一个表单项不显示分割线
            &:first-child {
                .form-cell-title::before {
                    display: none;
                }
            }

            .van-cell {
                padding: 0;
                background-color: transparent;

                &::after {
                    display: none;
                }
            }

            .van-cell__title {
                flex: 1;
            }

            .van-cell__right-icon {
                display: none;
            }

            &--disabled {
                opacity: 0.6;
            }
            .van-collapse-item__content {
                padding-top: 0;
            }
        }
    }
}

.form-cell-title {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    // 折叠子项的分割线
    &::before {
        content: "";
        position: absolute;
        left: 15px;
        right: 15px;
        top: 0;
        height: 1px;
        background-color: #f0f0f0;
        transform: scaleY(0.5);
    }
}

.form-cell-label {
    font-size: 13px;
    color: #333;

    // 激活状态下的标签颜色
    :deep(.van-collapse-item--expanded) & {
        color: #2e6bf1;
        font-weight: 500;
    }
}

.form-cell-value {
    font-size: 13px;
    color: #333;
    display: flex;
    align-items: center;

    .placeholder {
        color: #999;
    }

    &::after {
        content: "";
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-left: 5px;
        border-top: 1px solid #adadad;
        border-right: 1px solid #adadad;
        transform: rotate(45deg);
        transition: transform 0.3s;

        :deep(.van-collapse-item--expanded) & {
            transform: rotate(135deg);
        }
    }
}

/* 添加动画样式 */
.slide-enter-active,
.slide-leave-active {
    transition: all 0.3s ease;
    max-height: 300px;
    overflow: hidden;
}

.slide-enter-from,
.slide-leave-to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
}

/* 表单项出现的动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: translateY(20px);
}

/* 块状选择样式 */
.option-blocks {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 5px 0;
}

.option-block {
    font-size: 11px;
    color: #777777;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 68px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 10px;
    background: #f5f5f5;

    &:hover {
        background-color: #e8e8e8;
        border-color: #dcdcdc;
    }

    &.active {
        background-color: #2e6bf1;
        color: #fff;
        border-color: #2e6bf1;
    }
}
</style>
