.intention-page {
    background-color: #f6f6f6;
    padding-top: 41.59px;
    padding-bottom: 100px;
    box-sizing: border-box;

    .page-tips {
        background-color: #fff6edff;
        padding: 11px;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        z-index: 99;
        font-size: 12px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #fa8918;
        line-height: 18px;

        .mark {
            background-image: url("@/assets/images/tips.png");
            background-repeat: no-repeat;
            background-size: cover;
            width: 16px;
            height: 16px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 4px;
            top: -1px;
            position: relative;
        }
    }

    .intention-form {
        background-color: #f6f6f6ff;
        margin-top: 10px;
        padding-bottom: 20px;
        padding-bottom: var(---safe-area-bottom);
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100vw;

        .form-submit {
            padding: 16px 10px;
        }

        .btn {
            background: var(--color-primary-bg);
            height: 44px;
            line-height: 44px;
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            color: #ffffff;
            border-radius: 22px;
            width: 100%;
            text-align: center;
        }
    }

    .container {
        padding: 6px 0;

        .card {
            margin: 10px;
            //height: 200px;
            background: #ffffff;
            border-radius: 8px;
            padding: 16px 10px 8px 10px;
            box-sizing: border-box;

            .title {
                position: relative;
                padding-left: 8px;
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                color: #333333;
                line-height: 20px;
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;

                .right {
                    font-size: 14px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    color: #555555;
                    line-height: 20px;

                    .sign {
                        color: #555555;
                    }

                    .arrows {
                        font-size: 12px;
                        color: #555555;
                    }
                }

                &::before {
                    display: block;
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 50%;
                    margin-top: -7px;
                    width: 4px;
                    height: 14px;
                    border-radius: 2px;
                    background-color: var(--color-primary-bg);
                }
            }
        }
    }
}
:deep(.van-popup--bottom) {
    background-color: transparent;
}
.idCard {
    background-color: transparent;
    .idCard-img {
        width: 100vw;
        height: 127px;
        margin-bottom: -10px;
    }
    .content {
        background-color: #fff;
        min-height: 223px;
        padding: 10px 40px 33px 40px;
        .icon-name,
        .icon-idno,
        .icon-sex,
        .icon-birthday {
            height: 20px;
            width: 20px;
            background-repeat: no-repeat;
            background-size: cover;
        }
        .icon-name {
            background-image: url("@/assets/images/name.png");
        }

        .icon-idno {
            background-image: url("@/assets/images/idno.png");
        }

        .icon-sex {
            background-image: url("@/assets/images/sex.png");
        }

        .icon-birthday {
            background-image: url("@/assets/images/birthday.png");
        }
    }
}
