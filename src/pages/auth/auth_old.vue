<template>
    <div class="intention-page">
        <div class="page-tips">
            <div class="mark"></div>
            我们非常重视您的隐私安全，未经您授权绝不对外展示
        </div>
        <div class="container">
            <div class="card" v-for="(item, index) in fromConfig" :key="index">
                <div class="title" @click="onCardClick(item.keyword as string)">
                    <span>{{ item.label }}</span>
                    <span class="right" v-if="item.type === 'city'">
                        <span class="iconfont icon-a-WorkCity sign"></span>
                        {{ form[item.keyword] ?? "城市" }}
                        <span class="iconfont icon-a-Rightjump arrows"></span>
                    </span>
                </div>
                <template v-if="item.type === 'select'">
                    <Select
                        v-model="form[item.keyword]"
                        v-bind="item"
                        @onChange="saveData"
                    ></Select>
                    <!-- <Select v-bind="item" @onChange="saveData"></Select> -->
                </template>
                <template v-else-if="item.type === 'zhima'">
                    <ZhiMa v-bind="item" v-model="form[item.keyword]" @onChange="saveData"></ZhiMa>
                </template>
                <template v-else-if="item.type === 'city'">
                    <City
                        v-bind="item"
                        v-model="form[item.keyword]"
                        v-model:cityStatus="cityStatus"
                        @onChange="saveData"
                    ></City>
                </template>
            </div>
        </div>
        <div id="intention-form" class="intention-form">
            <!--立即申请按钮  -->
            <div class="form-submit">
                <div class="btn" data-collect="completeSubmitPre" type="primary" @click="onNext">
                    立即申请
                </div>
            </div>
        </div>
        <van-popup v-model:show="idCardStatus" position="bottom" @clickCloseIcon="onCardClick">
            <div class="idCard">
                <img src="@/assets/images/idCard.png" alt="" class="idCard-img" />
                <div class="content clearfix">
                    <!-- 姓名 -->
                    <div class="form-row">
                        <AppInput v-model="idCard.name" placeholder="姓名">
                            <template #prefix>
                                <div class="form-icon">
                                    <div class="icon-name"></div>
                                </div>
                            </template>
                        </AppInput>
                    </div>
                    <template v-if="store.isFlow">
                        <!-- 年龄 -->
                        <div class="form-row">
                            <AppInput
                                v-model="idCard.birthday"
                                placeholder="年龄"
                                formType="number"
                            >
                                <template #prefix>
                                    <div class="form-icon">
                                        <div class="icon-birthday"></div>
                                    </div>
                                </template>
                            </AppInput>
                        </div>
                        <!-- 性别 -->
                        <div class="form-row">
                            <AppInput v-model="idCard.sex" placeholder="性别">
                                <template #content>
                                    <van-radio-group
                                        style="padding-left: 12px"
                                        v-model="idCard.sex"
                                        direction="horizontal"
                                    >
                                        <van-radio name="男" checked-color="#44C6B3FF"
                                            >男</van-radio
                                        >
                                        <van-radio name="女" checked-color="#44C6B3FF"
                                            >女</van-radio
                                        >
                                    </van-radio-group>
                                </template>
                                <template #prefix>
                                    <div class="form-icon">
                                        <div class="icon-sex"></div>
                                    </div>
                                </template>
                            </AppInput>
                        </div>
                    </template>
                    <template v-else>
                        <!-- 身份证 -->
                        <div class="form-row">
                            <AppInput v-model="idCard.idno" placeholder="身份证号">
                                <template #prefix>
                                    <div class="form-icon">
                                        <div class="icon-idno"></div>
                                    </div>
                                </template>
                            </AppInput>
                        </div>
                    </template>
                    <AppButton
                        class="global-btn"
                        type="primary"
                        data-collect="completeSubmit"
                        @click="onSubmit"
                        class-name="throttle"
                    >
                        立即查看额度
                    </AppButton>
                </div>
            </div>
        </van-popup>

        <!-- 挽留页弹窗 -->
        <div class="custom-dialog-container" v-if="retainVisible">
            <div class="custom-dialog-mask"></div>
            <div class="custom-dialog-content">
                <img src="@/assets/images/retain_bg.png" alt="挽留页" class="retain-img" />
                <div class="retain-btn-box">
                    <div class="retain-btn" @click="continueSubmit">继续完成申请</div>
                    <div class="retain-btn-text" @click="closeRetainDialog">我再想想</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import Select from "@/components/Auth/Select.vue";
import ZhiMa from "@/components/Auth/ZhiMa.vue";
import City from "@/components/Auth/City.vue";
import AppInput from "@/components/App/Input.vue";
import AppButton from "@/components/App/Button.vue";
import { ref, onMounted, onBeforeUnmount, nextTick, watch } from "vue";

import { useAuth } from "@/pages/auth/hook/useAuth/useAuth";

const {
    onCardClick,
    fromConfig,
    cityStatus,
    form,
    onSubmit,
    idCardStatus,
    idCard,
    store,
    saveData,
    onNext
} = useAuth();

// 挽留弹窗相关
const retainVisible = ref<boolean>(false);
// 防抖计时器
let debounceTimer: number | null = null;

// 监听弹窗状态变化，控制页面滚动
watch(retainVisible, (newValue) => {
    store.interceptTips = false;

    if (newValue) {
        // 弹窗显示，禁止页面滚动
        document.body.style.overflow = "hidden";
        document.body.style.position = "fixed";
        document.body.style.width = "100%";
        store.interceptTips = false;
    } else {
        // 弹窗关闭，恢复页面滚动
        document.body.style.overflow = "";
        document.body.style.position = "";
        document.body.style.width = "";
        store.interceptTips = false;
    }
});

// 关闭挽留弹窗
const closeRetainDialog = () => {
    retainVisible.value = false;
    // 重新设置历史状态
    pushHistoryStates();
};

// 继续完成申请
const continueSubmit = () => {
    retainVisible.value = false;
    // 暂停拦截并执行申请逻辑
    window.location.href = "https://m.yikaionline.com/pages/promotion/index";
};

// 推入多个历史状态
const pushHistoryStates = () => {
    // 多次推入状态以确保拦截成功
    for (let i = 0; i < 5; i++) {
        window.history.pushState(
            { noBackExitPage: true, timestamp: Date.now() + i },
            "",
            window.location.href
        );
    }
};

// 处理浏览器返回事件
const handlePopState = (e: PopStateEvent) => {
    // 防止事件冒泡
    e.stopPropagation();

    // 防抖，避免多次触发
    if (debounceTimer) clearTimeout(debounceTimer);

    debounceTimer = window.setTimeout(() => {
        // 显示挽留弹窗
        retainVisible.value = true;
        // 再次推入历史状态
        pushHistoryStates();
    }, 50);
};

// 初始化拦截器
const initInterceptor = () => {
    // 移除现有监听器避免重复
    window.removeEventListener("popstate", handlePopState);

    // 添加popstate事件监听
    window.addEventListener("popstate", handlePopState);

    // 初始化历史状态
    pushHistoryStates();

    // 确保在iOS Safari中也能正常工作
    setTimeout(() => {
        pushHistoryStates();
    }, 100);
};

onMounted(() => {
    // 确保DOM已加载完成
    nextTick(() => {
        // 延迟初始化以确保页面完全加载
        setTimeout(() => {
            initInterceptor();
        }, 300);
    });
});

onBeforeUnmount(() => {
    // 清理资源
    window.removeEventListener("popstate", handlePopState);
    if (debounceTimer) clearTimeout(debounceTimer);

    // 确保页面退出时恢复滚动
    document.body.style.overflow = "";
    document.body.style.position = "";
    document.body.style.width = "";
});
</script>
<style scoped lang="less">
@import "auth";

/* 挽留弹窗样式 */
.retain-img {
    width: 100%;
    display: block;
}

.retain-btn-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 97%;
    max-width: 464px;
    padding: 20px 0;
    height: 125px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(90deg, rgba(255, 242, 228, 1) 0%, rgba(250, 218, 185, 1) 100%);
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

.retain-btn {
    position: relative;
    color: #ffffff;
    font-size: 14px;
    line-height: 40px;
    text-align: center;
    margin: 10px 0;
    font-weight: 500;
    width: 70%;
    height: 80px;
    border-radius: 47px;
    background: linear-gradient(90deg, rgba(233, 86, 59, 1) 0%, rgba(231, 52, 52, 1) 100%);
    &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: calc(100% + 17px);
        height: calc(100% + 17px);
        background: linear-gradient(90deg, rgba(233, 86, 59, 1) 0%, rgba(231, 52, 52, 1) 100%);
        border-radius: 134.01px;
        opacity: 0.1;
        z-index: -1;
    }
}

.retain-btn-text {
    color: #858585;
    font-size: 12px;
    line-height: 25px;
    text-align: center;
    margin: 10px 0;
    font-weight: 500;
    width: 80%;
}

/* 自定义弹窗样式 */
.custom-dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    /* 防止滚动穿透 */
    touch-action: none;
}

.custom-dialog-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.custom-dialog-content {
    position: relative;
    width: 70%;
    max-width: 464px;
    z-index: 2001;
    // border-radius: 12px;
    overflow: hidden;
}
</style>
