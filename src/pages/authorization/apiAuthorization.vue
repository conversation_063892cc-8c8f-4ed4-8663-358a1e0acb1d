<template>
    <div class="recommend-page">
        <div class="recommend-main">
            <div class="header">
                <div class="title">
                    <img alt="" src="../../assets//images/authPage/logo.png" srcset="" />
                    <span>浩瀚有借</span>
                </div>
            </div>
            <!-- 无匹配产品 -->
            <div v-if="!scope.data.list?.length && scope.dataState" class="noMatchingProduct">
                <div class="noMatchingProduct-product">
                    <div v-for="(item, index) in scope.promoteData" :key="index">
                        <a
                            :href="
                                creatUrl(
                                    item?.partner_api ?? 'https://g.hhhtfin.com/h5/index?_c=fdsfs',
                                    { _c: store.channel }
                                )
                            "
                        >
                            <section class="product">
                                <div class="product-describe">
                                    <img :src="item?.logo" alt="" />
                                    <span>{{ item?.name }}</span>
                                    <div v-for="(i, j) in item.tags" :key="j" class="titleWrap-tag">
                                        {{ i }}
                                    </div>
                                </div>
                                <div class="product-list">
                                    <div
                                        v-for="(a, b) in item?.detail"
                                        :key="b"
                                        class="product-item"
                                    >
                                        <div class="value">
                                            {{ a?.value }}
                                        </div>
                                        <div class="key">
                                            {{ a?.key }}
                                        </div>
                                    </div>
                                </div>
                                <!-- <a > -->
                                <div class="product-apply">立即申请</div>
                                <!-- </a> -->
                            </section>
                        </a>
                    </div>
                </div>
                <div class="btn-box">
                    <AppButton class="noMatchingProduct-btn" type="primary" @click="onGoBack"
                        >返回上一步
                    </AppButton>
                </div>
                <div class="noMatchingProduct-tips" style="background-color: #fff">
                    <p class="title">特别提醒</p>
                    <div class="tipWrap">
                        <p class="tip">
                            *平台承诺不会向用户收取<span
                                :class="`theme_color_procedure`"
                                class="color"
                                >任何费用</span
                            >，以官方名义收款行为都是诈骗。
                        </p>
                        <p class="tip">
                            *贷款到账前以卡号错误、流水不足等理由收取“验资费”、“解冻费用”等行为都是<span
                                :class="`theme_color_procedure`"
                                class="color"
                                >诈骗</span
                            >，请<span :class="`theme_color_procedure`" class="color">拒绝付费</span
                            >，谨防被骗。
                        </p>
                        <p class="tip">*致电人员均属平台合作的第三方金融机构。</p>
                    </div>
                </div>
            </div>
            <!-- 单个产品 -->
            <div
                class="wholeProcess"
                v-if="scope.data.list?.length == 1"
                @click="
                    scope.nuxtLink('/procedure/product', {
                        query: {
                            id: scope.data.list[0].id,
                            _s: 1,
                            _u: route.query._u,
                            order_id: scope.data.order_id
                        }
                    })
                "
            >
                <div class="wholeProcessCard">
                    <!-- <div class="wholeProcessCard-tab">优质产品</div> -->
                    <div class="wholeProcessCard-main">
                        <img
                            :src="scope.data.list[0].logo"
                            alt=""
                            srcset=""
                            class="wholeProcessCard-main-logo"
                        />
                        <div>{{ scope.data.list[0].name }}</div>
                    </div>
                    <div class="wholeProcessCard-describe">
                        {{ scope.data.list[0].partner_name }}
                    </div>
                    <div class="wholeProcessCard-text">最高可借金额(元)</div>
                    <div class="wholeProcessCard-amount">
                        {{
                            scope.data?.list[0]?.loan_max
                                .toString()
                                ?.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                        }}
                    </div>
                    <div class="wholeProcessCard-tag">
                        <div
                            v-for="tag in scope.data.list[0].detail.slice(0, 2)"
                            :key="tag.key"
                            class="wholeProcessCard-tag-item"
                        >
                            <img
                                src="@/assets/images/authPage/wholeProcessIcon.png"
                                style="width: 14px; height: 14px; margin-right: 4px"
                            />
                            {{ tag.value }}{{ tag.key }}
                        </div>
                    </div>
                </div>
            </div>
            <!-- 多个产品 -->
            <div v-if="scope.data.list?.length > 1" class="cardList">
                <div
                    v-for="item in scope.data.list ?? []"
                    :key="item.id"
                    class="cardList-card"
                    @click="
                        scope.nuxtLink('/procedure/product', {
                            query: {
                                id: item.id,
                                _s: 1,
                                _u: route.query._u,
                                order_id: scope.data.order_id
                            }
                        })
                    "
                >
                    <div class="cardList-left">
                        <div
                            :class="{
                                'cardList-cur': scope.selectItems.includes(item.id)
                            }"
                            class="cardList-circle"
                            v-html="CheckCircleFill"
                            @click.stop="scope.doClickItem(item.id)"
                        ></div>
                    </div>
                    <div class="cardList-right">
                        <div class="header">
                            <img :src="item.logo" alt="" srcset="" />
                            <span class="cardList-title"> {{ item.name }} </span>
                        </div>
                        <div class="company">{{ item.partner_name }}</div>
                        <div class="tags">
                            <div v-for="tag in item.detail.slice(0, 2)" :key="tag.key" class="tag">
                                <div class="value">{{ tag.value }}</div>
                                <div class="key">{{ tag.key }}</div>
                            </div>
                            <div class="tag">
                                <div class="value">95%</div>
                                <div class="key">放款成功率</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 协议 -->
            <AgreementView
                v-if="scope.data?.list?.length"
                :data="{ url: scope.agreementUrl }"
                background="#fff"
            >
                <template #header>
                    <div v-if="scope.data.list?.length" :class="{ shake: c }" class="agreement">
                        <span
                            :class="scope.switchAgreementData ? 'cur' : ''"
                            class="circle"
                            @click="changeSwitch"
                            v-html="CheckCircleFill"
                        >
                        </span>
                        <img
                            class="tipsImg"
                            src="@/assets/images/authPage/tipsImg.png"
                            alt=""
                            srcset=""
                        />
                        <span class="agreement-text">
                            <span>
                                本平台已为您匹配到专业贷款咨询机构(
                                {{ scope.data.list[0].partner_name
                                }}{{
                                    scope.data.list.length > 1 ? "等" : ""
                                }}
                                )。如需了解授权详情，请查看并同意本平台及机构的</span
                            >
                            <span v-for="item in scope.agreementData" :key="item.value"
                                ><a :href="item.value" class="agreement-link">{{
                                    item.label
                                }}</a></span
                            >
                            <span v-for="item in scope.data?.all_protocol" :key="item.value"
                                ><a :href="item.value" class="agreement-link">{{
                                    item.label
                                }}</a></span
                            >
                        </span>
                    </div>
                    <!-- 融多多专属协议 -->
                    <template v-for="item in scope.data?.protocols" :key="item.value">
                        <AgreementView
                            :data="{ url: item?.value }"
                            :path="true"
                            height="50vh"
                        ></AgreementView>
                    </template>
                </template>
                <template #footer>
                    <signTips />
                </template>
            </AgreementView>

            <!-- 声明 -->
            <Statement />
        </div>
        <div v-if="scope.data.list?.length" class="footer safe-area-padding-bottom">
            <AppButton
                :style="
                    !scope.switchAgreementData || !scope.selectItems.length
                        ? 'opacity:0.2;'
                        : 'opacity:1;'
                "
                class="global-btn"
                data-collect="authAgreeClick"
                type="primary"
                @click="onSub(scope)"
            >
                我已同意上述协议并申请服务
            </AppButton>
        </div>
    </div>
</template>
<script setup lang="ts">
import { defineProps, ref } from "vue";
import { creatUrl } from "@/utils/index";
import Statement from "@/gadget/statement/index.vue";
import signTips from "@/gadget/signTips.vue";
import AgreementView from "@/gadget/agreementView/index.vue";
import { CheckCircleFill } from "@/assets/svg/index";
import AppButton from "@/components/App/Button.vue";
import { useRoute } from "vue-router";
import { useGlobalStore } from "@/stores/global";
import { Toast } from "vant";

interface Props {
    data: any;
    judgeStr: any;
    onJudge: any;
    agreementData: any;
    onSubmit: any;
    selectItems: any;
    nuxtLink: (path: string, options: any) => void;
    switchAgreementData: any;
    changeSwitch: any;
    dataState: any;
    doClickItem: any;
    promoteData: any;
    interceptVisible: any;
    interceptConfirm: any;
    interceptCancel: any;
    agreementContent: any;
    agreementUrl: any;
    showPopup: any;
    // 融多多专属
    agreementShow: any;
    agreementShowTime: any;
    agreementShowList: any;
    //----
}

const route = useRoute();
const scope = defineProps<Props>();
const store = useGlobalStore();
// 是否触发颤动动画
const c = ref(false);
let time: any;

const onSub = (scope: any) => {
    if (!scope.selectItems.length) {
        Toast("请选择产品");
    } else if (!scope.switchAgreementData) {
        scope.showPopup();
        // Toast("请阅读并勾选协议");
        clearTimeout(time);
        // 颤动动画
        c.value = true;
        time = setTimeout(() => {
            c.value = false;
        }, 820);
    } else {
        scope.onSubmit();
    }
};
const onGoBack = () => {
    window.history.back();
};
</script>
<style scoped lang="less">
//  颤动动画
.shake {
    animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
    transform: translate3d(0, 0, 0);
}

@keyframes shake {
    10%,
    90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%,
    80% {
        transform: translate3d(2px, 0, 0);
    }
    30%,
    50%,
    70% {
        transform: translate3d(-4px, 0, 0);
    }
    40%,
    60% {
        transform: translate3d(4px, 0, 0);
    }
}

.recommend-page {
    height: 100%;
    background: var(--authpage_background, #fff);

    .recommend-main {
        padding: 16px;
        padding-bottom: 70px;
        box-sizing: border-box;

        .header {
            .title {
                display: flex;
                align-items: center;
                margin-bottom: 11px;

                img {
                    width: 32px;
                    height: 32px;
                    margin-right: 8px;
                }

                span {
                    color: var(--authpage_title_h1_color);
                    line-height: 17px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 18px;
                    line-height: 25px;
                    font-style: normal;
                }
            }

            .tip {
                font-size: 11px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: var(--authpage_title_h2_color);
                line-height: 16px;
            }
        }

        .noMatchingProduct {
            margin-top: 80px;

            .noMatchingProduct-product {
                .product {
                    background-color: #fff;
                    height: 114px;
                    position: relative;
                    padding: 20px;
                    margin-bottom: 16px;
                    box-shadow: 0px 2px 6px 0px rgba(49, 91, 255, 0.1);
                    border-radius: 8px;

                    .product-list {
                        display: flex;
                        margin-top: 14px;

                        .product-item {
                            margin-right: 32px;
                            text-align: center;

                            .value {
                                font-size: 18px;
                                font-family: DINAlternate-Bold, DINAlternate;
                                font-weight: bold;
                                color: var(--ThemeColor);
                                line-height: 22px;
                            }

                            .key {
                                font-size: 12px;
                                font-family: PingFangSC-Regular, PingFang SC;
                                font-weight: 400;
                                color: #888888;
                                line-height: 18px;
                            }
                        }
                    }

                    .product-describe {
                        display: flex;
                        // justify-content: center;
                        align-items: center;

                        span {
                            font-size: 14px;
                            font-family: PingFangSC-Semibold, PingFang SC;
                            font-weight: 600;
                            color: #333333;
                            line-height: 20px;
                        }

                        img {
                            width: 16px;
                            height: 16px;
                            margin-right: 5px;
                        }

                        .titleWrap-tag {
                            width: 44px;
                            height: 14px;
                            font-size: 12px;
                            font-family: PingFangSC-Semibold, PingFang SC;
                            font-weight: 600;
                            line-height: 16px;
                            margin-left: 8px;
                            text-align: center;
                        }

                        div:nth-of-type(1) {
                            color: #c09359;
                            background: #f8f4ee;
                        }

                        div:nth-of-type(2) {
                            color: #e98f4d;
                            background: #f6ebe6;
                        }

                        div:nth-of-type(3) {
                            color: #958dda;
                            background: #eeecfe;
                        }
                    }

                    .product-apply {
                        background: var(--ThemeColor);
                        width: 83px;
                        border-radius: 15px;
                        height: 30px;
                        position: absolute;
                        top: 42px;
                        right: 20px;
                        font-size: 12px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #ffffff;
                        line-height: 17px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                }
            }

            .btn-box {
                padding: 0 40px;
                margin-top: 24px;

                .noMatchingProduct-btn {
                    height: 50px;
                    border-radius: 25px;
                    border: 1px solid var(--ThemeColor);
                    background-color: #fff;
                    font-size: 18px;
                    color: var(--ThemeColor);
                }
            }

            .notProduct_procedure {
                width: 156px;
                height: 156px;
                background: url("@/assets/images/xsd5.png") no-repeat;
                margin-bottom: 12px;
            }

            .noMatchingProduct-card {
                height: 265px;
                // background: #ffffff;
                box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
                border-radius: 8px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                img {
                    width: 156px;
                    height: 156px;
                    margin-bottom: 12px;
                }

                .noMatchingProduct-title {
                    margin-bottom: 9px;
                    font-size: 18px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #333333;
                    line-height: 25px;
                }

                .noMatchingProduct-describe {
                    height: 17px;
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #999999;
                    line-height: 17px;
                }
            }

            .noMatchingProduct-tips {
                margin-top: 24px;
                height: 202px;
                // background: #ffffff;
                box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
                border-radius: 8px;
                padding: 12px 16px;

                .title {
                    height: 23px;
                    font-size: 16px;
                    font-weight: 500;
                    color: #222222;
                    line-height: 23px;
                    margin-bottom: 6px;

                    &::before {
                        content: "";
                        display: inline-block;
                        width: 4px;
                        height: 14px;
                        background: var(--authBgc);
                        border-radius: 2px;
                        position: relative;
                        margin-right: 3px;
                        top: 1px;
                    }
                }

                .tip {
                    font-size: 13px;
                    font-weight: 400;
                    color: #666666;
                    line-height: 19px;
                    margin-bottom: 15px;

                    .color {
                        color: #f64729;
                    }
                }
            }
        }

        .wholeProcess {
            margin-bottom: 12px;

            .wholeProcessCard {
                height: 195px;
                border-radius: 8px;
                padding: 24px;
                box-sizing: border-box;
                position: relative;
                background: url("@/assets/images/authPage/wholeProcessBg.png") no-repeat, #fff;
                background-size: 100% 195px;
                margin-top: 10px;
                box-sizing: border-box;

                .wholeProcessCard-main {
                    display: flex;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 16px;
                    color: #333333;
                    line-height: 22px;
                    text-align: left;

                    .wholeProcessCard-main-logo {
                        width: 18px;
                        height: 18px;
                        border-radius: 4px;
                        margin-right: 6px;
                    }
                }

                .wholeProcessCard-describe {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 11px;
                    color: #a49484;
                    line-height: 16px;
                    text-align: left;
                }

                .wholeProcessCard-text {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #555555;
                    line-height: 20px;
                    text-align: center;
                    margin-top: 12px;
                    margin-bottom: 5px;
                }

                .wholeProcessCard-amount {
                    font-family: D-DIN, D-DIN;
                    font-weight: bold;
                    font-size: 40px;
                    color: #667eff;
                    line-height: 43px;
                    text-align: left;
                    font-style: normal;
                    height: 43px;
                    text-align: center;
                    background: url("@/assets/images/authPage/wholeProcessFontBg.png") no-repeat;
                    background-size: 100% 43px;
                    margin-bottom: 8px;
                }

                .wholeProcessCard-tag {
                    display: flex;
                    justify-content: space-around;

                    .wholeProcessCard-tag-item {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #555555;
                        line-height: 18px;
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }

        .cardList {
            margin-top: 10px;

            .cardList-card {
                height: 129px;
                background: #ffffff;
                box-shadow: 0px 2px 6px 0px rgba(166, 166, 166, 0.2);
                border-radius: 8px;
                margin-bottom: 10px;
                display: flex;

                .cardList-left {
                    width: 58px;
                    min-width: 58px;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .cardList-circle {
                        width: 22px;
                        height: 22px;
                        border-radius: 50%;
                        border: 2px solid var(--authpage_card_selects);

                        :deep(svg) {
                            display: none;
                        }
                    }

                    .cardList-cur {
                        border: none;
                        stop-color: linear-gradient(180deg, #e8a841 0%, #e88441 100%) !important;

                        :deep(svg) {
                            width: 100%;
                            height: 100%;
                            display: block;
                            color: var(--authpage_card_selects);

                            circle {
                                fill: var(--authpage_card_selects);
                            }
                        }
                    }
                }

                .cardList-right {
                    height: 100%;
                    overflow: hidden;
                    box-sizing: border-box;
                    padding: 18px 0 10px 0;
                    display: flex;
                    flex-direction: column;

                    .header {
                        // margin-bottom: 10px;
                        display: flex;
                        align-items: center;

                        img {
                            width: 18px;
                            height: 18px;
                            margin-right: 4px;
                            border-radius: 3px;
                        }

                        .cardList-title {
                            margin-right: 10px;
                            font-weight: 600;
                            font-size: 16px;
                            color: var(--authpage_card_title_h1_color);
                            line-height: 22px;
                        }
                    }

                    .company {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 11px;
                        color: #333333;
                        color: var(--authpage_card_title_h2_color);
                        line-height: 17px;
                        white-space: nowrap;
                        margin-top: 4px;
                        margin-bottom: 15px;

                        font-family: PingFangSC, PingFang SC;
                    }

                    .tags {
                        display: flex;

                        .tag {
                            margin-right: 24px;
                            // display: flex;

                            .key {
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 12px;
                                color: var(--authpage_card_tag_color);
                                line-height: 18px;
                                white-space: nowrap;
                            }

                            .value {
                                color: var(--authpage_card_tag_important_color);
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 600;
                                font-size: 16px;
                                color: #3c56e4;
                                line-height: 22px;
                            }
                        }
                    }
                }
            }
        }

        .agreement {
            text-align: initial;
            // transform: translateX(0);
            // text-indent: 6em;
            // height: 87px;
            background: #f3f5f7;
            border-radius: 8px;
            padding: 12px;
            box-sizing: border-box;
            // display: flex;
            .tipsImg {
                width: 63px;
                height: 13px;
                display: inline-block;
            }

            .default {
                color: #999;
                margin-right: 10px;
            }

            .agreement-link {
                color: var(--authpage_important_font_color);
            }

            .agreement-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffe7ba;
                line-height: 17px;
                color: var(--authpage_text_color);
            }

            .circle {
                //position: relative;
                //top: 0px;
                //left: 3px;
                width: 14px;
                height: 14px;
                border-radius: 50%;
                border: 1px solid #999;
                display: inline-block;
                margin-right: 6px;

                :deep(svg) {
                    display: none;
                }
            }

            .empty {
                position: absolute;
                top: -6px;
                left: -8px;
                border-radius: 50%;
                background-color: transparent;
                width: 24px;
                height: 24px;
            }

            .cur {
                border: none;

                :deep(svg) {
                    width: 100%;
                    height: 100%;
                    display: block;
                    color: var(--authpage_important_font_color);

                    circle {
                        fill: var(--authpage_card_selects);
                    }
                }
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100vw;
        background-color: #fff;
        padding: 8px 20px 0 20px;
        box-shadow: 0px -4px 4px 0px rgba(160, 160, 160, 0.1);

        .global-btn {
            height: 50px;
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            line-height: 25px;
            background: var(--authpage_button_background);
            color: var(--authpage_button_font_color);
            margin-bottom: 8px;
            border-radius: 25px;
        }
    }
}
</style>
