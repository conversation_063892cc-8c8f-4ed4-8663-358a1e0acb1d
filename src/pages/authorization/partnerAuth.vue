<template>
    <div class="iframePage">
        <iframe
            :src="decodeURIComponent(url)"
            class="iframe"
            frameborder="0"
            height="100%"
            width="100%"
        ></iframe>
    </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";

const url = ref<any>("");
const route = useRoute();
onMounted(() => {
    console.log(route.query);
    url.value = route.query.target ?? "https://g.hhhtfin.com/h5/index?_c=fdsfs";
});
</script>
<style lang="less" scoped>
.iframePage {
    height: 100vh;

    .iframe {
        /* 设置滚动条的样式 */

        ::-webkit-scrollbar {
            width: 30px;
        }

        /*滚动槽*/

        ::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        /* 滚动条滑块 */

        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
        }
    }
}
</style>
