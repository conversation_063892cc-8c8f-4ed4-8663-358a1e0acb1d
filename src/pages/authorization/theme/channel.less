// 橙色
// 51信狐
.theme_51xinhu {
  // 背景色
  --authpage_background: linear-gradient(180deg, #f8aa85 0%, rgba(255, 150, 49, 0) 60%);

  // 按钮颜色
  --authpage_button_background: #e86e41ff;
  // 按钮字体颜色
  --authpage_button_font_color: #ffffffff;

  // 卡片定制
  ///多个产品
  // 卡片边框颜色
  --authpage_card_border: #ffa381ff;
  // 卡片背景
  --authpage_card_background: none;
  // 卡片多选按钮颜色
  --authpage_card_selects: #eb6b3bff;
  // 卡片一级标题字体颜色
  --authpage_card_title_h1_color: #333333ff;
  // 卡片二级标题字体颜色
  --authpage_card_title_h2_color: #333333ff;
  // 卡片tag字体颜色
  --authpage_card_tag_color: #555555ff;
  // 卡片tag重要字体颜色
  --authpage_card_tag_important_color: #eb6b3bff;

  /// 单个产品
  // 卡片边框颜色
  --authpage_one_card_border: none;
  // 卡片背景
  --authpage_one_card_background: #fff;
  // 卡片一级标题字体颜色
  --authpage_one_card_title_h1_color: #000000ff;
  // 卡片二级标题字体颜色
  --authpage_one_card_title_h2_color: #000000ff;
  // 卡片tag字体颜色
  --authpage_one_card_tag_color: #000000ff;
  // 卡片tag重要字体颜色
  --authpage_one_card_tag_important_color: #000000ff;

  // 一级标题字体颜色
  --authpage_title_h1_color: #ffffffff;
  // 二级标题字体颜色
  --authpage_title_h2_color: #ffffffff;
  // 正文字体颜色
  --authpage_text_color: #666666ff;
  // 重要字体颜色
  --authpage_important_font_color: #eb6b3bff;
}

// 黄色
// 用呗
.theme_yongbei {
  // 背景色
  --authpage_background: linear-gradient(180deg, #FFE103 0%, rgba(255, 225, 3, 0) 60%);

  // 按钮颜色
  --authpage_button_background: #FFE103FF;
  // 按钮字体颜色
  --authpage_button_font_color: #000000FF;

  // 卡片定制
  ///多个产品
  // 卡片边框颜色
  --authpage_card_border: #FEB97C;
  // 卡片背景
  --authpage_card_background: none;
  // 卡片多选按钮颜色
  --authpage_card_selects: #FE8D29FF;
  // 卡片一级标题字体颜色
  --authpage_card_title_h1_color: #333333ff;
  // 卡片二级标题字体颜色
  --authpage_card_title_h2_color: #333333ff;
  // 卡片tag字体颜色
  --authpage_card_tag_color: #555555ff;
  // 卡片tag重要字体颜色
  --authpage_card_tag_important_color: #FE8D29FF;

  /// 单个产品
  // 卡片边框颜色
  --authpage_one_card_border: none;
  // 卡片背景
  --authpage_one_card_background: #fff;
  // 卡片一级标题字体颜色
  --authpage_one_card_title_h1_color: #000000ff;
  // 卡片二级标题字体颜色
  --authpage_one_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_one_card_tag_color: #555555FF;
  // 卡片tag重要字体颜色
  --authpage_one_card_tag_important_color: #FE6F28FF;

  // 一级标题字体颜色
  --authpage_title_h1_color: #333333ff;
  // 二级标题字体颜色
  --authpage_title_h2_color: #333333ff;
  // 正文字体颜色
  --authpage_text_color: #666666ff;
  // 重要字体颜色
  --authpage_important_font_color: #FE8D29FF;
}

// 深蓝色
.theme_text {
  // 背景色
  --authpage_background: linear-gradient(180deg, #0131A3FF 0%, #0131A300 50%);

  // 按钮颜色
  --authpage_button_background: #00339EFF;
  // 按钮字体颜色
  --authpage_button_font_color: #FFFFFFFF;

  // 卡片定制
  ///多个产品
  // 卡片边框颜色
  --authpage_card_border: #748AFF;
  // 卡片背景
  --authpage_card_background: none;
  // 卡片多选按钮颜色
  --authpage_card_selects: #00339EFF;
  // 卡片一级标题字体颜色
  --authpage_card_title_h1_color: #333333FF;
  // 卡片二级标题字体颜色
  --authpage_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_card_tag_color: #555555ff;
  // 卡片tag重要字体颜色
  --authpage_card_tag_important_color: #00339EFF;

  /// 单个产品
  // 卡片边框颜色
  --authpage_one_card_border: none;
  // 卡片背景
  --authpage_one_card_background: #fff;
  // 卡片一级标题字体颜色
  --authpage_one_card_title_h1_color: #000000ff;
  // 卡片二级标题字体颜色
  --authpage_one_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_one_card_tag_color: #555555FF;
  // 卡片tag重要字体颜色
  --authpage_one_card_tag_important_color: #00339EFF;

  // 一级标题字体颜色
  --authpage_title_h1_color: #FFFFFFFF;
  // 二级标题字体颜色
  --authpage_title_h2_color: #FFFFFFFF;
  // 正文字体颜色
  --authpage_text_color: #666666ff;
  // 重要字体颜色
  --authpage_important_font_color: #00339EFF;
}

// 浅蓝色
// 小安钱包
.theme_xiaoanqianbao {
  // 背景色
  --authpage_background: linear-gradient(180deg, #2974F5 0%, rgba(49, 114, 255, 0) 60%);

  // 按钮颜色
  --authpage_button_background: #2974F5FF;
  // 按钮字体颜色
  --authpage_button_font_color: #FFFFFFFF;

  // 卡片定制
  ///多个产品
  // 卡片边框颜色
  --authpage_card_border: #81A4FF;
  // 卡片背景
  --authpage_card_background: none;
  // 卡片多选按钮颜色
  --authpage_card_selects: #2974F5FF;
  // 卡片一级标题字体颜色
  --authpage_card_title_h1_color: #333333FF;
  // 卡片二级标题字体颜色
  --authpage_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_card_tag_color: #555555ff;
  // 卡片tag重要字体颜色
  --authpage_card_tag_important_color: #2974F5FF;

  /// 单个产品
  // 卡片边框颜色
  --authpage_one_card_border: none;
  // 卡片背景
  --authpage_one_card_background: #fff;
  // 卡片一级标题字体颜色
  --authpage_one_card_title_h1_color: #000000ff;
  // 卡片二级标题字体颜色
  --authpage_one_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_one_card_tag_color: #555555FF;
  // 卡片tag重要字体颜色
  --authpage_one_card_tag_important_color: #2974F5FF;

  // 一级标题字体颜色
  --authpage_title_h1_color: #FFFFFFFF;
  // 二级标题字体颜色
  --authpage_title_h2_color: #FFFFFFFF;
  // 正文字体颜色
  --authpage_text_color: #666666ff;
  // 重要字体颜色
  --authpage_important_font_color: #2974F5FF;
}

// 浅蓝色
// 炎晓2
.theme_yanxiao2 {
  // 背景色
  --authpage_background: linear-gradient(180deg, #3688FF 0%, rgba(54, 136, 255, 0) 60%);

  // 按钮颜色
  --authpage_button_background: #3688FFFF;
  // 按钮字体颜色
  --authpage_button_font_color: #FFFFFFFF;

  // 卡片定制
  ///多个产品
  // 卡片边框颜色
  --authpage_card_border: #7DB2FF;
  // 卡片背景
  --authpage_card_background: none;
  // 卡片多选按钮颜色
  --authpage_card_selects: #3688FFFF;
  // 卡片一级标题字体颜色
  --authpage_card_title_h1_color: #333333FF;
  // 卡片二级标题字体颜色
  --authpage_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_card_tag_color: #555555ff;
  // 卡片tag重要字体颜色
  --authpage_card_tag_important_color: #3688FFFF;

  /// 单个产品
  // 卡片边框颜色
  --authpage_one_card_border: none;
  // 卡片背景
  --authpage_one_card_background: #fff;
  // 卡片一级标题字体颜色
  --authpage_one_card_title_h1_color: #000000ff;
  // 卡片二级标题字体颜色
  --authpage_one_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_one_card_tag_color: #555555FF;
  // 卡片tag重要字体颜色
  --authpage_one_card_tag_important_color: #3688FFFF;

  // 一级标题字体颜色
  --authpage_title_h1_color: #FFFFFFFF;
  // 二级标题字体颜色
  --authpage_title_h2_color: #FFFFFFFF;
  // 正文字体颜色
  --authpage_text_color: #666666ff;
  // 重要字体颜色
  --authpage_important_font_color: #2974F5FF;
}

// 橙色
// 橙意通
.theme_chengyitong {
  // 背景色
  --authpage_background: linear-gradient(180deg, #FE6F28 0%, rgba(254, 111, 40, 0) 60%);

  // 按钮颜色
  --authpage_button_background: #FE6F28FF;
  // 按钮字体颜色
  --authpage_button_font_color: #FFFFFFFF;

  // 卡片定制
  ///多个产品
  // 卡片边框颜色
  --authpage_card_border: #FF8B51;
  // 卡片背景
  --authpage_card_background: none;
  // 卡片多选按钮颜色
  --authpage_card_selects: #FE6F28FF;
  // 卡片一级标题字体颜色
  --authpage_card_title_h1_color: #333333FF;
  // 卡片二级标题字体颜色
  --authpage_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_card_tag_color: #555555ff;
  // 卡片tag重要字体颜色
  --authpage_card_tag_important_color: #FE6F28FF;

  /// 单个产品
  // 卡片边框颜色
  --authpage_one_card_border: none;
  // 卡片背景
  --authpage_one_card_background: #fff;
  // 卡片一级标题字体颜色
  --authpage_one_card_title_h1_color: #000000ff;
  // 卡片二级标题字体颜色
  --authpage_one_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_one_card_tag_color: #555555FF;
  // 卡片tag重要字体颜色
  --authpage_one_card_tag_important_color: #FE6F28FF;

  // 一级标题字体颜色
  --authpage_title_h1_color: #FFFFFFFF;
  // 二级标题字体颜色
  --authpage_title_h2_color: #FFFFFFFF;
  // 正文字体颜色
  --authpage_text_color: #666666ff;
  // 重要字体颜色
  --authpage_important_font_color: #FE6F28FF;
}

// 蓝
// 盈小钱,炎晓
.theme_yingxiaoqian,
.theme_yanxiao,
.theme_yanxiao3 {
  // 背景色
  --authpage_background: linear-gradient(180deg, #4661F8 0%, rgba(70, 97, 248, 0) 60%);

  // 按钮颜色
  --authpage_button_background: #4462F8FF;
  // 按钮字体颜色
  --authpage_button_font_color: #FFFFFFFF;

  // 卡片定制
  ///多个产品
  // 卡片边框颜色
  --authpage_card_border: #748AFF;
  // 卡片背景
  --authpage_card_background: none;
  // 卡片多选按钮颜色
  --authpage_card_selects: #4462F8FF;
  // 卡片一级标题字体颜色
  --authpage_card_title_h1_color: #333333FF;
  // 卡片二级标题字体颜色
  --authpage_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_card_tag_color: #555555ff;
  // 卡片tag重要字体颜色
  --authpage_card_tag_important_color: #4462F8FF;

  /// 单个产品
  // 卡片边框颜色
  --authpage_one_card_border: none;
  // 卡片背景
  --authpage_one_card_background: #fff;
  // 卡片一级标题字体颜色
  --authpage_one_card_title_h1_color: #000000ff;
  // 卡片二级标题字体颜色
  --authpage_one_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_one_card_tag_color: #555555FF;
  // 卡片tag重要字体颜色
  --authpage_one_card_tag_important_color: #4462F8FF;

  // 一级标题字体颜色
  --authpage_title_h1_color: #FFFFFFFF;
  // 二级标题字体颜色
  --authpage_title_h2_color: #FFFFFFFF;
  // 正文字体颜色
  --authpage_text_color: #666666ff;
  // 重要字体颜色
  --authpage_important_font_color: #4462F8FF;
}

// 橙色
// 闪钱
.theme_sudai {
  // 背景色
  --authpage_background: linear-gradient(180deg, #FFB611 0%, rgba(251, 175, 20, 0) 60%);

  // 按钮颜色
  --authpage_button_background: #FE8D29FF;
  // 按钮字体颜色
  --authpage_button_font_color: #FFFFFFFF;

  // 卡片定制
  ///多个产品
  // 卡片边框颜色
  --authpage_card_border: #FEB97C;
  // 卡片背景
  --authpage_card_background: none;
  // 卡片多选按钮颜色
  --authpage_card_selects: #FE8D29FF;
  // 卡片一级标题字体颜色
  --authpage_card_title_h1_color: #333333FF;
  // 卡片二级标题字体颜色
  --authpage_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_card_tag_color: #555555ff;
  // 卡片tag重要字体颜色
  --authpage_card_tag_important_color: #FE8D29FF;

  /// 单个产品
  // 卡片边框颜色
  --authpage_one_card_border: none;
  // 卡片背景
  --authpage_one_card_background: #fff;
  // 卡片一级标题字体颜色
  --authpage_one_card_title_h1_color: #000000ff;
  // 卡片二级标题字体颜色
  --authpage_one_card_title_h2_color: #333333FF;
  // 卡片tag字体颜色
  --authpage_one_card_tag_color: #555555FF;
  // 卡片tag重要字体颜色
  --authpage_one_card_tag_important_color: #FE6F28FF;

  // 一级标题字体颜色
  --authpage_title_h1_color: #FFFFFFFF;
  // 二级标题字体颜色
  --authpage_title_h2_color: #FFFFFFFF;
  // 正文字体颜色
  --authpage_text_color: #666666ff;
  // 重要字体颜色
  --authpage_important_font_color: #FE8D29FF;
}
