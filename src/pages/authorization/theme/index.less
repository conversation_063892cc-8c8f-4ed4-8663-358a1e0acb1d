:root {
  // 背景色
  --authpage_background: linear-gradient( 180deg, #3C56E4 0%, #4860E6 9%, #FFFFFF 57%, #FFFFFF 100%);
  // --authpage_background:#fff;
  // 按钮颜色
  --authpage_button_background: #3C56E4FF;
  // 按钮字体颜色
  --authpage_button_font_color: #ffffffff;

  // 卡片定制
  ///多个产品
  // 卡片边框颜色
  --authpage_card_border: #6786E5FF;
  // 卡片背景
  --authpage_card_background: linear-gradient(90deg, #fc8d62 0%, #fc8155 100%);
  // 卡片多选按钮颜色
  --authpage_card_selects: #354bdb;
  // 卡片一级标题字体颜色
  --authpage_card_title_h1_color: #333333ff;
  // 卡片二级标题字体颜色
  --authpage_card_title_h2_color: #333333ff;
  // 卡片tag字体颜色
  --authpage_card_tag_color: #555555ff;
  // 卡片tag重要字体颜色
  --authpage_card_tag_important_color: #3C56E4FF;

  /// 单个产品
  // 卡片边框颜色
  --authpage_one_card_border: none;
  // 卡片背景
  --authpage_one_card_background: linear-gradient(90deg, #3C56E4 0%, #4250C7 100%);
  // 卡片一级标题字体颜色
  --authpage_one_card_title_h1_color: #ffffffff;
  // 卡片二级标题字体颜色
  --authpage_one_card_title_h2_color: #ffffffff;
  // 卡片tag字体颜色
  --authpage_one_card_tag_color: #ffffffff;
  // 卡片tag重要字体颜色
  --authpage_one_card_tag_important_color: #ffffffff;

  // 一级标题字体颜色
  --authpage_title_h1_color: #fff;
  // 二级标题字体颜色
  --authpage_title_h2_color: #333333ff;
  // 正文字体颜色
  --authpage_text_color: #666666ff;
  // 重要字体颜色
  --authpage_important_font_color: #3C56E4FF;
}
