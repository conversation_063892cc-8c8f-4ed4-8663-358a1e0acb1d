<template>
    <div class="authPage">
        <!-- 主题 -->
        <div :class="`theme_${route.params.theme}`">
            <WholeProcess
                v-if="data.list?.length == 1 && data?.list[0]?.sub"
                v-bind="{
                    data,
                    judgeStr,
                    onJudge,
                    agreementData,
                    onSubmit,
                    selectItems,
                    nuxtLink: $nuxtLink,
                    switchAgreementData,
                    changeSwitch,
                    dataState,
                    doClickItem,
                    promoteData,
                    interceptVisible,
                    interceptConfirm,
                    interceptCancel,
                    agreementContent,
                    agreementUrl,
                    showPopup,
                    // 融多多专属
                    agreementShow,
                    agreementShowTime,
                    agreementShowList
                    //----
                }"
            />
            <ApiAuthorization
                v-else
                v-bind="{
                    data,
                    judgeStr,
                    onJudge,
                    agreementData,
                    onSubmit,
                    selectItems,
                    nuxtLink: $nuxtLink,
                    switchAgreementData,
                    changeSwitch,
                    dataState,
                    doClickItem,
                    promoteData,
                    interceptVisible,
                    interceptConfirm,
                    interceptCancel,
                    agreementContent,
                    agreementUrl,
                    showPopup,
                    // 融多多专属
                    agreementShow,
                    agreementShowTime,
                    agreementShowList
                    //----
                }"
            />
            <VanDialog v-model:show="interceptVisible" :showConfirmButton="false">
                <div class="interceptContent">您确定不继续申请贷款吗？</div>
                <template #footer>
                    <div class="interceptBtn">
                        <AppButton type="primary" @click="interceptCancel"> 确认</AppButton>
                        <AppButton type="primary" @click="interceptConfirm"> 再看看</AppButton>
                    </div>
                </template>
            </VanDialog>

            <!-- 融多多协议弹窗 -->
            <VanDialog v-model:show="agreementShow" show-cancel-button title="用户须知">
                <div class="notice">
                    <!-- 融多多专属协议 -->
                    <template v-for="item in agreementShowList" :key="item.value">
                        <div style="padding: 10px 0; background: #fff">
                            <AgreementView
                                :data="{ url: item?.value }"
                                :path="true"
                            ></AgreementView>
                        </div>
                    </template>
                </div>
                <template #footer>
                    <div class="notice-btnBox">
                        <button class="notice-btn">{{ agreementShowTime }}秒后跳转</button>
                    </div>
                </template>
            </VanDialog>
            <!-- 提示勾选协议弹窗 -->
            <VanDialog
                v-model:show="confirmAgreementPopup"
                :show-cancel-button="true"
                title="用户须知"
            >
                <template #title>
                    <div class="popupTitle">
                        用户须知
                        <div class="titleIcon" @click="confirmAgreementPopup = false">
                            <van-icon name="cross" color="#666" />
                        </div>
                    </div>
                </template>
                <div class="notice">
                    <!-- 融多多专属协议 -->
                    <!--                    <template v-for="item in agreementShowList" :key="item.value">-->
                    <div style="padding: 10px 0; background: #fff">
                        <AgreementView
                            height="300px"
                            :data="{ url: agreementUrl }"
                            :path="true"
                        ></AgreementView>
                    </div>
                    <!--                    </template>-->
                </div>
                <template #footer>
                    <div class="notice-btnBox">
                        <button class="notice-btn" @click="onConfig">同意并立即申请</button>
                    </div>
                </template>
            </VanDialog>
        </div>
    </div>
</template>
<script lang="ts" setup>
import AppButton from "@/components/App/Button.vue";
import { useRoute } from "vue-router";
import { Dialog } from "vant";
import AgreementView from "@/gadget/agreementView/index.vue";
import ApiAuthorization from "@/pages/authorization/apiAuthorization.vue";
import WholeProcess from "@/pages/authorization/wholeProcess.vue";
import { useAuthorization } from "@/hook/useAuthorization/useAuthorization";

const VanDialog = Dialog.Component;
const route = useRoute();

const {
    data,
    judgeStr,
    onJudge,
    agreementData,
    onSubmit,
    selectItems,
    $nuxtLink,
    switchAgreementData,
    changeSwitch,
    dataState,
    doClickItem,
    promoteData,
    interceptVisible,
    interceptConfirm,
    interceptCancel,
    agreementContent,
    agreementUrl,
    confirmAgreementPopup,
    // 融多多专属
    agreementShow,
    agreementShowTime,
    agreementShowList
    //----
} = useAuthorization();

const showPopup = () => {
    confirmAgreementPopup.value = true;
};
const onConfig = () => {
    switchAgreementData.value = true;
    onSubmit();
};
</script>

<style lang="less">
// 默认颜色
@import url("./theme/index.less");
</style>
<style lang="less" scoped>
@import url("./theme/channel.less");

.authPage {
    height: 100%;
}
</style>
<style lang="less" scoped>
.interceptContent {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 106px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 25px;
}

.interceptBtn {
    display: flex;
    border-top: 1px solid #eeeeee;

    > div {
        flex: 1;
        height: 54px;
        background-color: #fff;
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #999999;
        line-height: 25px;

        &:nth-child(2) {
            background-color: var(--authpage_button_background);
            color: var(--authpage_button_font_color);
        }
    }
}

.notice {
    padding: 0 20px;
    min-height: 200px;

    p {
        padding: 5px 5px;
    }

    .title-box {
        margin: 16px 0 18px 0;

        .notice-title {
            padding: 0 5px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 24px;
        }
    }

    .notice-h2 {
        color: #e44c2a;
        padding: 0 5px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        line-height: 24px;
    }

    .content-box {
        padding: 21px 12px;
        background: #f9f9f9;
        border-radius: 4px;

        .notice-content {
            height: 206px;
            overflow: auto;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 18px;
            padding: 0 5px;
            text-align: justify;

            &::-webkit-scrollbar-thumb {
                width: 1px;
                background-color: #99999980;
            }

            &::-webkit-scrollbar {
                width: 1px;
            }
        }
    }
}

.notice-btnBox {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;

    .notice-btn {
        border: none;
        width: 80%;
        margin: 0 auto;
        height: 40px;
        border-radius: 20px;
        color: #fff;
        background-color: var(--authpage_button_background);
        //opacity: 0.5;
    }
}

.popupTitle {
    position: relative;

    .titleIcon {
        position: absolute;
        top: -10px;
        right: 20px;
    }
}
</style>
