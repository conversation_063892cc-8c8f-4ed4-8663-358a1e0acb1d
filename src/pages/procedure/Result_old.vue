<template>
    <LayoutPage class="resultPage">
        <div class="hr"></div>
        <div v-if="$route.query.status === 'failure'" class="panel">
            <img alt="" src="@/assets/images/result/headerImg.png" />
            <p class="title">很遗憾，无匹配的产品</p>
            <p class="subTitle">提高期望额度和增加资产项可匹配更多贷款产品</p>
            <template v-if="store.channel === 'xiaoanfenqi'">
                <div class="downBtn" @click="down">下载app,查看进度</div>
            </template>
        </div>
        <header class="header clearfix" v-else>
            <div class="resultBar clearfix">
                <img class="resultBar-img" src="@/assets/images/result/headerImg.png" alt="" />
                <div class="resultBar-text">请注意接听商务经理来电</div>
            </div>
            <section class="productResult">
                <div class="productResult-title">您申请的产品</div>
                <div
                    :class="item.result == 'success' ? 'productResult-on' : 'productResult-off'"
                    v-for="item in productList"
                    v-show="productList.length"
                    :key="item.name"
                >
                    <div class="productResult-uname">
                        {{ item?.name }}
                    </div>
                    <div v-if="item.result == 'success'" class="productResult-result">成功</div>
                    <div v-else-if="item.result == 'failed'" class="productResult-result">失败</div>
                    <van-loading v-else color="#C5CFE0FF" size="12px" type="spinner" />
                </div>
                <div v-show="!productList.length" class="dialogBox-productLoading">
                    <van-loading v-if="!loadingStatus" color="#3C56E4FF" size="20px" type="spinner"
                        >加载中...
                    </van-loading>
                    <span v-else style="color: #3c56e4ff"> 订单异常 </span>
                </div>
            </section>
        </header>

        <!-- 代抄 -->
        <section v-if="data?.length" class="externalLinks">
            <header class="externalLinks-title">想借更多</header>
            <template v-for="item in data ?? []" :key="item">
                <a
                    :href="
                        creatUrl(item?.partner_api ?? 'https://g.hhhtfin.com/h5/index?_c=fdsfs', {
                            _c: store.channel
                        })
                    "
                >
                    <section class="externalLinks-card">
                        <header class="externalLinks-header">
                            <img :src="item?.logo" class="externalLinks-logo" />
                            <div class="externalLinks-productName">{{ item.name }}</div>
                        </header>
                        <div class="externalLinks-agreement">
                            <span>协议</span>
                            <img alt="" src="@/assets/images/result/agreement.png" />
                        </div>
                        <div class="externalLinks-content">
                            <div>
                                <!-- <div class="externalLinks-key">最高额度</div>
                                <div class="externalLinks-value">200,000.00 <span>元</span></div> -->
                                <div class="externalLinks-key">
                                    {{ item?.detail ? (item?.detail[0] ?? {}).key : "异常" }}
                                </div>
                                <div class="externalLinks-value">
                                    {{ item?.detail ? (item?.detail[0] ?? {}).value : "异常" }}
                                    <span>元</span>
                                </div>
                            </div>
                            <div class="externalLinks-examine">立即查看</div>
                        </div>
                        <div class="externalLinks-tags">
                            <div v-for="j in item.detail?.slice(1)" :key="j" class="tags-item">
                                {{ j.key + j.value }}
                            </div>
                        </div>
                    </section>
                </a>
            </template>
        </section>

        <div class="tips">
            <p class="title">特别提醒</p>
            <div class="tipWrap">
                <p class="tip">
                    *平台承诺不会向用户收取<span class="color">任何费用</span
                    >，以官方名义收款行为都是诈骗。
                </p>
                <p class="tip">
                    *贷款到账前以卡号错误、流水不足等理由收取"验资费"、"解冻费用"等行为都是<span
                        class="color"
                        >诈骗</span
                    >，请<span class="color">拒绝付费</span>，谨防被骗。
                </p>
                <p class="tip">*致电人员均属平台合作的第三方金融机构。</p>
            </div>
            <template v-if="store.channel === 'xiaoanfenqi' && $route.query.status !== 'failure'">
                <div class="downBtn" @click="down">下载app,查看进度</div>
            </template>
        </div>

        <!-- 更多产品推荐 -->
        <div
            v-if="countDown >= 0"
            style="
                width: 227px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                background-color: #1959ff;
                color: #fff;
                border-radius: 20px;
                margin: 0 auto;
                margin-top: 20px;
                margin-bottom: 50px;
                cursor: pointer;
            "
            @click="openNewPage"
        >
            更多产品推荐（{{ countDown }}ｓ）
        </div>

        <!-- 借款申请须知 -->
        <VanDialog v-model:show="statement" show-cancel-button title="借款申请须知">
            <div class="notice">
                <div class="title-box">
                    <section class="notice-h2">您正在申请借贷请您充分阅读此内容:</section>
                </div>
                <div class="content-box">
                    <section class="notice-content">
                        <p>
                            1.正规放贷机构不会以解冻资金、交保证金、做银行流水或者资质审核为由先行收取费用！以"验资费"、"保证金"等理由收款的行为都是诈骗，如遇到请勿支付。
                        </p>
                        <p>
                            2.正规放贷机构不会向您索要银行卡密码和短信验证码，请勿将此提供给陌生人。
                        </p>
                        <p>
                            3.正规放贷机构不会单独向用户收费或要求线下还款、对私打款或银行转账等，或冒充公检法等机关要求您提供相关个人信息，如遇到以上情况请拒绝。
                        </p>
                        <p>
                            4.本平台作为移动金融智选平台，本身不从事资金放贷，向您展示的贷款产品由第三方机构提供，您在选择贷款产品前应仔细阅读相关产品信息及注意风险。
                        </p>
                        <p>
                            5.基于协助您申请产品之目的，您授权本平台将您提供的产品申请所需信息（包括但不限于姓名、手机号码、身份证号码及您申请时所填列的相关个人信息等）将提供给为您办理贷款服务的第三方机构，请您注意接听相关来电。
                        </p>
                        <p>6.网络贷款有风险，请仔细分辨和识别，谨防诈骗！</p>
                        <p>
                            7.如您遇到疑似诈骗时，要及时拨打反诈专线 96110
                            进行咨询；如您不慎被骗，请立即拨打110报警，并保存聊天记录、转账记录等相关证据，切勿通过网上去寻找维权机构，避免二次上当受骗！
                        </p>
                    </section>
                </div>
            </div>
            <template #footer>
                <div class="notice-btnBox">
                    <button class="notice-btn" @click="statement = false">我已悉知</button>
                </div>
            </template>
        </VanDialog>
    </LayoutPage>
</template>
<script lang="ts" setup>
import { Dialog } from "vant";
import { onBeforeMount, onMounted, onBeforeUnmount, ref } from "vue";
import { getCommitResult, getPromote } from "@/apis/index";
import { creatUrl, getUrlData } from "@/utils/index";
import { useGlobalStore } from "@/stores/global";
import { useRoute } from "vue-router";

const route = useRoute();
const store = useGlobalStore();
const VanDialog = Dialog.Component;
const statement = ref(true);
const data = ref<any>({});
const productList = ref<any>([]);
const loadingStatus = ref<boolean>(false);
const countDown = ref<number>(-1);
let timer: number | null = null;

const urlData = getUrlData();
onMounted(() => {
    setTimeout(() => {
        getProductDetails();
    }, 1000);
    getPromote().then((res) => {
        console.log(res);

        if (res?.data?.length > 0) {
            data.value = res.data;
        } else {
            startCountDown();
        }
    });
});

// 开始倒计时
const startCountDown = () => {
    // 清除可能存在的定时器
    if (timer !== null) {
        clearInterval(timer);
    }

    // 设置倒计时初始值
    countDown.value = 3;

    // 创建定时器，每秒减1
    timer = window.setInterval(() => {
        if (countDown.value > 0) {
            countDown.value -= 1;
        }

        // 倒计时结束，自动打开新标签页
        if (countDown.value === 0) {
            openNewPage();
            clearInterval(timer as number);
            timer = null;
        }
    }, 1000);
};

// 打开新标签页
const openNewPage = () => {
    // 如果倒计时未结束，不执行操作
    if (countDown.value > 0) return;

    // 打开新标签页
    window.location.href = "https://m.yikaionline.com/pages/promotion/index";
};

const getProductDetails = () => {
    if (urlData.orderId)
        getCommitResult({ order: urlData.orderId }).then((res) => {
            // console.log(res);
            if (res.data.code == -1) {
                setTimeout(() => {
                    getProductDetails();
                }, 500);
            } else {
                setTimeout(() => {
                    loadingStatus.value = true;
                }, 10000);
            }
            productList.value = res.data.list ?? [];
        });
};
// 下载app
const down = () => {
    // const ios = "https://apps.apple.com/cn/app/%E4%BA%BF%E4%BF%A1%E7%AE%A1%E5%AE%B6/id1630912831";
    // const android = "https://ajresources.oss-cn-hangzhou.aliyuncs.com/yx/yixin.apk";
    // // const android =
    // //     "https://a.app.qq.com/o/simple.jsp?pkgname=com.haohan666.yixin&g_f=1178424&fromcase=70048&scenevia=YDGW#";
    // const url = /(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent) ? ios : android;
    // window.open(url, "_blank");
};

onBeforeMount(() => {
    if (route.query.status === "failure") {
        statement.value = false;
    }
});

onBeforeUnmount(() => {
    // 清除定时器
    if (timer !== null) {
        clearInterval(timer);
        timer = null;
    }
});
</script>
<style lang="less" scoped>
.resultPage {
    min-height: 100vh;
    background-color: #fff;
    .hr {
        height: 10px;
        // background-color: #f8f8f8;
    }

    .header {
        min-height: 184px;
        // background-color: #f8f8f8;
        .resultBar {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 31px 0 16px 0;
            .resultBar-img {
                width: 138px;
                height: 138px;
            }
            .resultBar-text {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #3c56e4;
                line-height: 20px;
                margin-top: 4px;
            }
        }
        .productResult {
            padding: 20px;
            background: #fff;
            padding-bottom: 0;
            .productResult-title {
                &::before {
                    position: relative;
                    top: 1px;
                    left: 0;
                    content: "";
                    display: inline-block;
                    width: 4px;
                    height: 14px;
                    background-color: var(--color-primary-bg);
                    border-radius: 2px;
                    margin-right: 4px;
                }
                height: 23px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                line-height: 22px;
                margin-bottom: 10px;
            }
            .dialogBox-productLoading {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 60px;
            }
            .productResult-on {
                height: 50px;
                border-radius: 8px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 30px 0 17px;
                box-shadow: 0px 2px 8px 0px #d6daf1;
                margin-bottom: 20px;
                background: url("@/assets/images/result/res-on.png"),
                    linear-gradient(135deg, #373edb 0%, #4496c6 100%);
                background-position: center right;
                background-size: contain;
                background-repeat: no-repeat;
                background-blend-mode: normal;
                .productResult-uname {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 16px;
                    color: #ffffff;
                    line-height: 22px;
                }
                .productResult-result {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 18px;
                    color: #ffffff;
                    line-height: 26px;
                }
            }
            .productResult-off {
                height: 50px;
                // background: linear-gradient(90deg, #dae3f0 0%, #c5cfe0 100%);
                border-radius: 8px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 30px 0 17px;
                box-shadow: 0px 2px 8px 0px #d6daf1;
                margin-bottom: 20px;
                background: url("@/assets/images/result/res-off.png"),
                    linear-gradient(90deg, #dae3f0 0%, #c5cfe0 100%);
                background-position: center right;
                background-size: contain;
                background-repeat: no-repeat;
                background-blend-mode: normal;

                .productResult-uname {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 16px;
                    color: #8190a4;
                    line-height: 22px;
                }
                .productResult-result {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 18px;
                    color: #ffffff;
                    line-height: 25px;
                }
            }
        }
    }

    .externalLinks {
        background-color: #f9f9f9;

        .externalLinks-title {
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 22px;
            padding: 0 20px;
            margin-bottom: 13px;
            margin-top: 13px;

            &::before {
                content: "";
                display: inline-block;
                width: 4px;
                height: 14px;
                background: #ee5533;
                border-radius: 2px;
                position: relative;
                top: 1px;
                margin-right: 3px;
            }
        }

        .externalLinks-card {
            background: url("@/assets/images/result/background.png") no-repeat;
            background-size: cover;
            min-height: 140px;
            margin-bottom: 16px;
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
            padding: 19px 20px 13px;
            position: relative;
            box-sizing: border-box;

            .externalLinks-header {
                display: flex;
                align-items: center;

                .externalLinks-logo {
                    width: 18px;
                    height: 21px;
                    display: inline-block;
                    margin-right: 6px;
                    object-fit: contain;
                }

                .externalLinks-productName {
                    display: inline-block;
                    font-size: 14px;
                    font-family: PingFangSC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #333333;
                    line-height: 20px;
                }
            }

            .externalLinks-agreement {
                position: absolute;
                top: 21px;
                right: 20px;
                display: flex;
                align-items: center;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 18px;

                img {
                    width: 13px;
                    height: 13px;
                    margin-left: 2px;
                }
            }

            .externalLinks-content {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .externalLinks-key {
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #edba52;
                    line-height: 18px;
                    margin-top: 10px;
                    margin-bottom: 4px;
                }

                .externalLinks-value {
                    font-size: 28px;
                    font-family: DINAlternate-Bold, DINAlternate;
                    font-weight: bold;
                    color: #ee5533;
                    line-height: 18px;

                    span {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #edba52;
                        line-height: 18px;
                    }
                }

                .externalLinks-examine {
                    width: 83px;
                    height: 30px;
                    background: linear-gradient(360deg, #ff804c 0%, #fa6549 100%);
                    border-radius: 15px;
                    font-size: 12px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #ffffff;
                    line-height: 30px;
                    text-align: center;
                }
            }

            .externalLinks-tags {
                margin-top: 10px;
                display: flex;

                .tags-item {
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #999999;
                    line-height: 18px;
                    margin-right: 10px;
                }
            }
        }
    }

    .dialog {
        :deep(.van-dialog) {
            width: 300px;
            height: 254px;
            background-color: #fff;
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
            border-radius: 8px;
        }

        .dialog-bgc {
            background: linear-gradient(
                360deg,
                rgba(238, 85, 51, 0.2) 0%,
                rgba(255, 255, 255, 0) 21%,
                rgba(255, 255, 255, 0) 100%
            );
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
            border-radius: 8px;
            width: 300px;
            height: 254px;
        }

        .statement {
            display: flex;
            flex-direction: column;
            align-items: center;

            .theme_img_procedure {
                width: 88px;
                height: 88px;
                border-radius: 50%;
                background-color: #f5f5f5;
                margin-top: 24px;
                margin-bottom: 21px;
                display: flex;
                justify-content: center;
                align-items: center;

                .icon {
                    font-size: 60px;
                    color: #fa6549;
                }
            }

            img {
                margin-top: 13px;
                width: 100px;
                height: 100px;
                margin-bottom: 12px;
            }

            div {
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 20px;
            }
        }
    }

    .tips {
        padding: 16px;
        background: #fff;

        .title {
            &::before {
                position: relative;
                top: 1px;
                left: 0;
                content: "";
                display: inline-block;
                width: 4px;
                height: 14px;
                background-color: var(--color-primary-bg);
                border-radius: 2px;
                margin-right: 4px;
            }
            height: 23px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 22px;
            margin-bottom: 10px;
        }

        .tip {
            font-size: 13px;

            font-weight: 400;

            color: #666666;

            line-height: 19px;
            margin-bottom: 15px;

            .color {
                color: #f64729;
            }
        }
    }

    .panel {
        min-height: 210px;
        padding-bottom: 16px;
        // display: flex;
        // flex-direction: column;
        // align-items: center;
        // justify-content: center;
        background: #fff;
        text-align: center;

        img {
            width: 140px;
            height: 100px;
            margin: 0 auto;
        }

        .title {
            margin-top: 5px;

            font-size: 18px;

            font-weight: 500;

            color: #222222;

            line-height: 25px;
        }

        .subTitle {
            margin-top: 6px;

            font-size: 14px;

            font-weight: 400;
            color: #999999;
            line-height: 20px;
        }
    }

    .notice {
        padding: 0 20px;

        p {
            padding: 5px 5px;
        }

        .title-box {
            margin: 16px 0 18px 0;

            .notice-title {
                padding: 0 5px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 24px;
            }
        }

        .notice-h2 {
            color: var(--color-primary-bg);
            padding: 0 5px;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            line-height: 24px;
        }

        .content-box {
            padding: 21px 12px;
            background: #f9f9f9;
            border-radius: 4px;

            .notice-content {
                height: 206px;
                overflow: auto;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 18px;
                padding: 0 5px;
                text-align: justify;

                &::-webkit-scrollbar-thumb {
                    width: 1px;
                    background-color: #99999980;
                }

                &::-webkit-scrollbar {
                    width: 1px;
                }
            }
        }
    }

    .notice-btnBox {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80px;

        .notice-btn {
            border: none;
            width: 80%;
            margin: 0 auto;
            height: 40px;
            border-radius: 20px;
            color: #fff;
            background-color: var(--color-primary-bg);
        }
    }
}

.downBtn {
    text-align: center;
    margin: 20px 16px;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 44px;
    height: 44px;
    background: #ee5533;
    box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.08);
    border-radius: 25px;
}
</style>
