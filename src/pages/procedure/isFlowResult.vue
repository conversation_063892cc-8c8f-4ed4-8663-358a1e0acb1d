<template>
    <div>
        <!-- 匹配成功弹窗 -->
        <van-dialog v-model:show="matchDialog" @confirm="dialogConfirm">
            <template #title>
                <div class="dialog-title">提交成功</div>
            </template>

            <template #default>
                <span class="circle cur" v-html="CheckCircleFill"> </span>
                <div class="dialog-main">
                    <div>您的信息已提交成功，稍后将会通过</div>
                    <div>短信告知审核结果，请注意查收!</div>
                </div>
            </template>
        </van-dialog>
    </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { CheckCircleFill } from "@/assets/svg/index";
import { Dialog } from "vant";

const VanDialog = Dialog.Component;

const matchDialog = ref<any>(true);
const dialogConfirm = () => {
    window.history.back();
};
</script>
<style lang="less" scoped>
:deep(.van-button__content) {
    background-color: #de7146;
    color: #fff;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

:deep(.van-dialog__footer) {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

:deep(.van-dialog) {
    overflow: visible;
}

.dialog-title {
    font-weight: 600;
    font-size: 18px;
    margin-top: 30px;
}

.circle {
    position: absolute;
    top: -40px;
    left: 50%;
    margin-left: -40px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 1px solid rgb(238, 85, 51);
    z-index: 9999;

    :deep(svg) {
        display: none;
    }

    overflow: hidden;

    &.cur {
        border: none;

        :deep(svg) {
            width: 80px;
            height: 80px;
            display: block;
            color: #81d490;
            background-color: #fff;
        }
    }
}

.dialog-main {
    text-align: center;
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 30px;
    margin-top: 20px;
}
</style>
