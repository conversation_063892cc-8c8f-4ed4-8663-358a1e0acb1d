<template>
    <div class="recommend-page" v-if="data">
        <div class="recommend-main">
            <div class="header">
                <div class="tip">恭喜，已为您在第三方平台匹配到以下机构</div>
                <div class="title">
                    <img alt="" :src="data?.product_logo" srcset="" />
                    <span>{{ data?.product_name }}</span>
                </div>
            </div>
            <!-- 单个产品 -->

            <div
                class="wholeProcess"
                @click="
                    $nuxtLink('/procedure/product', {
                        query: {
                            id: route.query.product_id,
                            order_id: route.query.order_id,
                            _s: 1
                        }
                    })
                "
            >
                <div class="wholeProcessCard">
                    <div class="wholeProcessCard-tab">优质产品</div>
                    <div class="wholeProcessCard-main">
                        <img
                            :src="data?.logo"
                            alt=""
                            srcset=""
                            class="wholeProcessCard-main-logo"
                        />
                        <div>{{ data?.name }}</div>
                    </div>
                    <div class="wholeProcessCard-describe">
                        {{ data?.partner_name }}
                    </div>
                    <div class="wholeProcessCard-text">最高可借金额(元)</div>
                    <div class="wholeProcessCard-amount">
                        {{ data?.loan_max?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                    </div>
                    <div class="wholeProcessCard-tag">
                        <div
                            v-for="tag in data?.advantages"
                            :key="tag.key"
                            class="wholeProcessCard-tag-item"
                        >
                            <img
                                src="@/assets/images/authPage/wholeProcessIcon.png"
                                style="width: 14px; height: 14px; margin-right: 4px"
                            />
                            {{ tag.value }}{{ tag.key }}
                        </div>
                    </div>
                </div>
                <div class="wholeProcessMain">
                    <img
                        :src="data?.product_logo"
                        alt=""
                        srcset=""
                        class="wholeProcessCard-main-logo"
                    />
                    <span>由“{{ data?.product_name }}”为您推荐上述产品</span>
                </div>
            </div>

            <!-- 协议 -->
            <div class="agreementBox">
                <AgreementView :data="agreementData" background="#fff" v-if="data.name">
                    <template #header="s">
                        <div v-if="data" :class="{ shake: c }" class="agreement">
                            <img
                                class="tipsImg"
                                src="../../../assets/images/authPage/tipsImg.png"
                                alt=""
                                srcset=""
                            />
                            <span class="agreement-text">
                                <span
                                    > 本平台已为您匹配到专业贷款咨询机构({{
                                        data?.partner_name
                                    }})。如需了解授权详情，请查看并同意本平台及机构的</span
                                >
                                <span>
                                    <a :href="s.agreementUrl" class="agreement-link"
                                        >《个人信息授权及产品风险告知》</a
                                    >
                                </span>
                                <span v-for="item in data?.protocols" :key="item.value">
                                    <a :href="item.value" class="agreement-link">{{
                                        item.label
                                    }}</a>
                                </span>
                            </span>
                        </div>
                    </template>
                    <template #footer>
                        <signTips />
                    </template>
                </AgreementView>
            </div>

            <!-- 声明 -->
            <Statement />
        </div>
        <div class="footer safe-area-padding-bottom">
            <AppButton
                :style="!switchAgreementData ? 'opacity:0.2;' : 'opacity:1;'"
                class="global-btn"
                data-collect="authAgreeClick"
                type="primary"
                @click="onSub"
            >
                我已同意上述协议并申请服务
            </AppButton>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useRoute } from "vue-router";
import { useGlobalStore } from "@/stores/global";
import { Toast } from "vant";
import { useWholeProcessGoods } from "@/pages/procedure/wholeProcessGoods/useWholeProcessGoods";
import Statement from "@/gadget/statement/index.vue";
import AgreementView from "@/gadget/agreementView/index.vue";
import SignTips from "@/gadget/signTips.vue";
import AppButton from "@/components/App/Button.vue";

const route = useRoute();
const { data, agreementData, switchAgreementData, onSubmit, $nuxtLink } = useWholeProcessGoods();
const store = useGlobalStore();

// 是否触发颤动动画
const c = ref(false);
let time: any;

const onSub = () => {
    if (!switchAgreementData.value) {
        Toast("请阅读并勾选协议");
        clearTimeout(time);
        // 颤动动画
        c.value = true;
        time = setTimeout(() => {
            c.value = false;
        }, 820);
    } else {
        onSubmit();
    }
};
const onGoBack = () => {
    window.history.back();
};
</script>
<style scoped lang="less">
.recommend-page {
    // 背景色
    --authpage_background: linear-gradient(
        180deg,
        #3c56e4 0%,
        #4860e6 9%,
        #ffffff 57%,
        #ffffff 100%
    );
    // --authpage_background:#fff;
    // 按钮颜色
    --authpage_button_background: #3c56e4ff;
    // 按钮字体颜色
    --authpage_button_font_color: #ffffffff;

    // 卡片定制
    ///多个产品
    // 卡片边框颜色
    --authpage_card_border: #6786e5ff;
    // 卡片背景
    --authpage_card_background: linear-gradient(90deg, #fc8d62 0%, #fc8155 100%);
    // 卡片多选按钮颜色
    --authpage_card_selects: #354bdb;
    // 卡片一级标题字体颜色
    --authpage_card_title_h1_color: #333333ff;
    // 卡片二级标题字体颜色
    --authpage_card_title_h2_color: #333333ff;
    // 卡片tag字体颜色
    --authpage_card_tag_color: #555555ff;
    // 卡片tag重要字体颜色
    --authpage_card_tag_important_color: #3c56e4ff;

    /// 单个产品
    // 卡片边框颜色
    --authpage_one_card_border: none;
    // 卡片背景
    --authpage_one_card_background: linear-gradient(90deg, #3c56e4 0%, #4250c7 100%);
    // 卡片一级标题字体颜色
    --authpage_one_card_title_h1_color: #ffffffff;
    // 卡片二级标题字体颜色
    --authpage_one_card_title_h2_color: #ffffffff;
    // 卡片tag字体颜色
    --authpage_one_card_tag_color: #ffffffff;
    // 卡片tag重要字体颜色
    --authpage_one_card_tag_important_color: #ffffffff;

    // 一级标题字体颜色
    --authpage_title_h1_color: #fff;
    // 二级标题字体颜色
    --authpage_title_h2_color: #333333ff;
    // 正文字体颜色
    --authpage_text_color: #666666ff;
    // 重要字体颜色
    --authpage_important_font_color: #3c56e4ff;
}

.recommend-page {
    height: 100%;
    background: linear-gradient(180deg, #3c56e4 0%, #4860e6 9%, #ffffff 57%, #ffffff 100%);

    .recommend-main {
        padding: 16px;
        padding-bottom: 70px;
        box-sizing: border-box;

        .header {
            text-align: center;

            .tip {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
                line-height: 20px;
            }

            .title {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 8px;
                margin-top: 8px;

                img {
                    width: 30px;
                    height: 30px;
                    margin-right: 8px;
                    border-radius: 6px;
                }

                span {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 16px;
                    color: #ffffff;
                    line-height: 22px;
                }
            }
        }

        .noMatchingProduct {
            margin-top: 80px;

            .noMatchingProduct-product {
                .product {
                    background-color: #fff;
                    height: 114px;
                    position: relative;
                    padding: 20px;
                    margin-bottom: 16px;
                    box-shadow: 0px 2px 6px 0px rgba(49, 91, 255, 0.1);
                    border-radius: 8px;

                    .product-list {
                        display: flex;
                        margin-top: 14px;

                        .product-item {
                            margin-right: 32px;
                            text-align: center;

                            .value {
                                font-size: 18px;
                                font-family: DINAlternate-Bold, DINAlternate;
                                font-weight: bold;
                                color: var(--ThemeColor);
                                line-height: 22px;
                            }

                            .key {
                                font-size: 12px;
                                font-family: PingFangSC-Regular, PingFang SC;
                                font-weight: 400;
                                color: #888888;
                                line-height: 18px;
                            }
                        }
                    }

                    .product-describe {
                        display: flex;
                        // justify-content: center;
                        align-items: center;

                        span {
                            font-size: 14px;
                            font-family: PingFangSC-Semibold, PingFang SC;
                            font-weight: 600;
                            color: #333333;
                            line-height: 20px;
                        }

                        img {
                            width: 16px;
                            height: 16px;
                            margin-right: 5px;
                        }

                        .titleWrap-tag {
                            width: 44px;
                            height: 14px;
                            font-size: 12px;
                            font-family: PingFangSC-Semibold, PingFang SC;
                            font-weight: 600;
                            line-height: 16px;
                            margin-left: 8px;
                            text-align: center;
                        }

                        div:nth-of-type(1) {
                            color: #c09359;
                            background: #f8f4ee;
                        }

                        div:nth-of-type(2) {
                            color: #e98f4d;
                            background: #f6ebe6;
                        }

                        div:nth-of-type(3) {
                            color: #958dda;
                            background: #eeecfe;
                        }
                    }

                    .product-apply {
                        background: var(--ThemeColor);
                        width: 83px;
                        border-radius: 15px;
                        height: 30px;
                        position: absolute;
                        top: 42px;
                        right: 20px;
                        font-size: 12px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #ffffff;
                        line-height: 17px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                }
            }

            .btn-box {
                padding: 0 40px;
                margin-top: 24px;

                .noMatchingProduct-btn {
                    height: 50px;
                    border-radius: 25px;
                    border: 1px solid var(--ThemeColor);
                    background-color: #fff;
                    font-size: 18px;
                    color: var(--ThemeColor);
                }
            }

            .notProduct_procedure {
                width: 156px;
                height: 156px;
                background: url("@/assets/images/xsd5.png") no-repeat;
                margin-bottom: 12px;
            }

            .noMatchingProduct-card {
                height: 265px;
                // background: #ffffff;
                box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
                border-radius: 8px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                img {
                    width: 156px;
                    height: 156px;
                    margin-bottom: 12px;
                }

                .noMatchingProduct-title {
                    margin-bottom: 9px;
                    font-size: 18px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #333333;
                    line-height: 25px;
                }

                .noMatchingProduct-describe {
                    height: 17px;
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #999999;
                    line-height: 17px;
                }
            }

            .noMatchingProduct-tips {
                margin-top: 24px;
                height: 202px;
                // background: #ffffff;
                box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
                border-radius: 8px;
                padding: 12px 16px;

                .title {
                    height: 23px;
                    font-size: 16px;
                    font-weight: 500;
                    color: #222222;
                    line-height: 23px;
                    margin-bottom: 6px;

                    &::before {
                        content: "";
                        display: inline-block;
                        width: 4px;
                        height: 14px;
                        background: var(--authBgc);
                        border-radius: 2px;
                        position: relative;
                        margin-right: 3px;
                        top: 1px;
                    }
                }

                .tip {
                    font-size: 13px;
                    font-weight: 400;
                    color: #666666;
                    line-height: 19px;
                    margin-bottom: 15px;

                    .color {
                        color: #f64729;
                    }
                }
            }
        }

        .wholeProcess {
            .wholeProcessCard {
                height: 195px;
                border-radius: 8px;
                padding: 24px;
                box-sizing: border-box;
                position: relative;
                background: url("@/assets/images/authPage/wholeProcessBg.png") no-repeat, #fff;
                background-size: 100% 195px;
                margin-top: 10px;
                box-sizing: border-box;

                .wholeProcessCard-tab {
                    width: 68px;
                    height: 24px;
                    background: linear-gradient(180deg, #ffc99d 0%, #ffcea8 100%);
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 11px;
                    color: #b15a31;
                    line-height: 24px;
                    text-align: center;
                    border-bottom-left-radius: 8px;
                    border-top-right-radius: 8px;
                    position: absolute;
                    top: 0;
                    right: 0;
                }

                .wholeProcessCard-main {
                    display: flex;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 16px;
                    color: #333333;
                    line-height: 22px;
                    text-align: left;

                    .wholeProcessCard-main-logo {
                        width: 18px;
                        height: 18px;
                        border-radius: 4px;
                        margin-right: 6px;
                    }
                }

                .wholeProcessCard-describe {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 11px;
                    color: #a49484;
                    line-height: 16px;
                    text-align: left;
                }

                .wholeProcessCard-text {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #555555;
                    line-height: 20px;
                    text-align: center;
                    margin-top: 12px;
                    margin-bottom: 5px;
                }

                .wholeProcessCard-amount {
                    font-family: D-DIN, D-DIN;
                    font-weight: bold;
                    font-size: 40px;
                    color: #667eff;
                    line-height: 43px;
                    text-align: left;
                    font-style: normal;
                    height: 43px;
                    text-align: center;
                    background: url("@/assets/images/authPage/wholeProcessFontBg.png") no-repeat;
                    background-size: 100% 43px;
                    margin-bottom: 8px;
                }

                .wholeProcessCard-tag {
                    display: flex;
                    justify-content: space-around;

                    .wholeProcessCard-tag-item {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #555555;
                        line-height: 18px;
                        display: flex;
                        align-items: center;
                    }
                }
            }

            .wholeProcessMain {
                height: 80px;
                background: #667eff;
                border-radius: 0px 0px 16px 16px;
                display: flex;
                margin-top: -21px;
                padding: 32px 24px 14px 24px;
                box-sizing: border-box;
                margin-bottom: 10px;
                align-items: center;

                > img {
                    width: 32px;
                    height: 32px;
                    border-radius: 4px;
                    margin-right: 8px;
                }

                > span {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #ffffff;
                    // line-height: 32px;
                }
            }
        }

        .cardList {
            margin-top: 10px;

            .cardList-card {
                height: 80px;
                background: #ffffff;
                box-shadow: 0px 2px 6px 0px rgba(166, 166, 166, 0.2);
                border-radius: 8px;
                border: 1px solid var(--authpage_card_border);
                margin-bottom: 10px;
                display: flex;

                .cardList-left {
                    width: 58px;
                    min-width: 58px;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .cardList-circle {
                        width: 22px;
                        height: 22px;
                        border-radius: 50%;
                        border: 2px solid var(--authpage_card_selects);

                        :deep(svg) {
                            display: none;
                        }
                    }

                    .cardList-cur {
                        border: none;
                        stop-color: linear-gradient(180deg, #e8a841 0%, #e88441 100%) !important;

                        :deep(svg) {
                            width: 100%;
                            height: 100%;
                            display: block;
                            color: var(--authpage_card_selects);

                            circle {
                                fill: var(--authpage_card_selects);
                            }
                        }
                    }
                }

                .cardList-right {
                    height: 100%;
                    overflow: hidden;
                    box-sizing: border-box;
                    padding: 18px 0 10px 0;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;

                    .header {
                        // margin-bottom: 10px;
                        display: flex;
                        align-items: center;

                        img {
                            width: 18px;
                            height: 18px;
                            margin-right: 4px;
                            border-radius: 3px;
                        }

                        .cardList-title {
                            font-size: 16px;
                            font-family: PingFangSC-Semibold, PingFang SC;
                            font-weight: 600;
                            color: var(--authpage_card_title_h1_color);
                            line-height: 22px;
                            margin-right: 10px;
                            white-space: nowrap;
                        }

                        .company {
                            font-size: 12px;
                            font-family: PingFangSC-Semibold, PingFang SC;
                            font-weight: 600;
                            color: var(--authpage_card_title_h2_color);
                            line-height: 17px;
                            white-space: nowrap;
                        }
                    }

                    .tags {
                        display: flex;

                        .tag {
                            margin-right: 24px;
                            display: flex;

                            .key {
                                font-size: 12px;
                                font-family: PingFangSC-Regular, PingFang SC;
                                font-weight: 400;
                                color: var(--authpage_card_tag_color);
                                line-height: 18px;
                                margin-right: 2px;
                                white-space: nowrap;
                            }

                            .value {
                                font-size: 12px;
                                font-family: PingFangSC-Semibold, PingFang SC;
                                font-weight: 600;
                                color: var(--authpage_card_tag_important_color);
                                line-height: 17px;
                                white-space: nowrap;
                            }
                        }
                    }
                }
            }
        }

        .agreement {
            text-align: initial;
            // transform: translateX(0);
            // text-indent: 6em;
            // height: 87px;
            background: #f3f5f7;
            border-radius: 8px;
            padding: 12px;
            box-sizing: border-box;
            // display: flex;
            .tipsImg {
                width: 63px;
                height: 13px;
                display: inline-block;
            }

            .default {
                color: #999;
                margin-right: 10px;
            }

            .agreement-link {
                color: var(--authpage_important_font_color);
            }

            .agreement-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffe7ba;
                line-height: 17px;
                color: var(--authpage_text_color);
            }

            .circle {
                position: absolute;
                top: 0px;
                left: 3px;
                width: 14px;
                height: 14px;
                border-radius: 50%;
                border: 1px solid #999;
                display: inline-block;

                :deep(svg) {
                    display: none;
                }
            }

            .empty {
                position: absolute;
                top: -6px;
                left: -8px;
                border-radius: 50%;
                background-color: transparent;
                width: 24px;
                height: 24px;
            }

            .cur {
                border: none;

                :deep(svg) {
                    width: 100%;
                    height: 100%;
                    display: block;
                    color: var(--authpage_important_font_color);

                    circle {
                        fill: var(--authpage_card_selects);
                    }
                }
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100vw;
        background-color: #fff;
        padding: 8px 20px 0 20px;
        box-shadow: 0px -4px 4px 0px rgba(160, 160, 160, 0.1);

        .global-btn {
            height: 50px;
            font-size: 18px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            line-height: 25px;
            background: var(--authpage_button_background);
            color: var(--authpage_button_font_color);
            margin-bottom: 8px;
            border-radius: 25px;
        }
    }
}
</style>
