import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useGlobalStore } from "@/stores/global";
import { getProductInfo } from "@/apis/index";
import { getUrlData } from "@/utils/index";
import moment from "moment/moment";
import { submitH5Commit } from "@/gadget/productList/submit";

export const useWholeProcessGoods = () => {
    const urlData = getUrlData();
    const store = useGlobalStore();
    const router = useRouter();
    const route = useRoute();
    const data = ref<any>();
    const localStorageKey = `authData_${store.uid}`;
    const authData = JSON.parse(localStorage.getItem(localStorageKey) ?? "{}");
    const agreementData = ref<any>();
    // 是否默认勾选协议
    const switchAgreementData = ref<any>(true);
    const $nuxtLink = (path: string, options: any) => {
        router.push({
            path,
            ...options
        });
    };
    const changeSwitch = () => {
        switchAgreementData.value = !switchAgreementData.value;
    };

    const onSubmit = async () => {
        const { idCard } = authData;
        await submitH5Commit({
            orderId: route.query.order_id,
            list: [route.query.product_id],
            name: idCard.name,
            idno: idCard.idno
        });
    };
    const init = async () => {
        const res: any = await getProductInfo({
            product_id: route.query.product_id,
            order_id: route.query.order_id
        });
        data.value = res.data ?? {};
        localStorage.setItem("recommendList", JSON.stringify([res.data ?? {}]));
        store.recommendList = [res.data ?? {}];

        console.log(` store.recommendList-->`, store.recommendList);
        getAgreementData();
    };

    const getAgreementData = () => {
        agreementData.value = {
            url: window.location.origin + import.meta.env.VITE_RECOMMEND_AGREEMENT,
            query: {
                name: authData.idCard.name,
                idno: authData.idCard.idno,
                p: data.value?.name,
                uid: store.uid,
                date: moment(new Date()).format("YYYY年MM月DD日")
            }
        };
    };

    onMounted(() => {
        init();
    });

    return {
        data,
        agreementData,
        onSubmit,
        $nuxtLink,
        switchAgreementData,
        changeSwitch
    };
};
