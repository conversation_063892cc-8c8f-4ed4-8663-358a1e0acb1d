<template>
    <LayoutPage class="productPage">
        <template v-if="data">
            <section class="productInfo">
                <header class="headerBgi"></header>
                <section class="infoContent clearfix">
                    <div v-if="store.channel === 'tttr'" class="infoContent-productName"></div>
                    <div v-else class="infoContent-productName">
                        <img :src="data.logo" alt="" class="logo" />
                        <p class="name">{{ data.name }}</p>
                    </div>
                    <div class="infoContent-company">
                        {{ data.partner_name }}
                    </div>
                    <div class="infoContent-tags">
                        <div
                            v-for="(item, index) in data.advantages"
                            :key="item.key"
                            class="tags-item"
                        >
                            <p class="value">{{ item.value }}</p>
                            <p class="key">{{ item.key }}</p>
                        </div>
                        <div class="tags-item">
                            <p class="value">20万元</p>
                            <p class="key">最高可借额度</p>
                        </div>
                    </div>
                    <div class="contentBottom">
                        <span> 办理贷款，平台不会向用户收取任何费用；谨防电话、微信诈骗。 </span>
                    </div>
                </section>
                <div class="descWrap">
                    <!-- 产品说明 -->
                    <p class="title">产品说明</p>
                    <div class="description">
                        <div v-for="(item, index) in data.description" :key="index" class="item">
                            <p class="key">{{ item.key }}</p>
                            <p class="value">{{ item.value }}</p>
                        </div>
                    </div>
                </div>
                <div v-if="showButton" class="submitWrap">
                    <!-- 隐私协议 -->
                    <div class="checkbox">
                        <div
                            :class="{ cur: agreement }"
                            class="adm-checkbox-icon circle"
                            @click="agreement = !agreement"
                            v-html="CheckCircleFill"
                        ></div>
                        <div class="adm-checkbox-content">
                            <span> 我已阅读并同意以下协议，并授权相应机构为我提供咨询服务： </span>
                            <!-- <a
                                :href="item.value"
                                v-for="(item, index) in data.partner_agreement"
                                :key="index"
                                >{{ item.key }}</a
                            > -->
                            <a :href="agreementUrl">《第三方服务及个人信息授权》</a>
                        </div>
                    </div>
                    <signTips />
                    <AppButton
                        class="global-btn"
                        data-collect="productSubmitAgreement"
                        type="primary"
                        @click="onJudge"
                    >
                        立即申请
                    </AppButton>
                </div>
                <div class="processWrap">
                    <p class="title">申请流程</p>
                    <div class="process">
                        <div v-for="(item, index) in data.process" :key="index" class="item">
                            <img :src="item.logo" alt="" class="item-img" />
                            <p class="label">{{ item.key }}</p>
                        </div>
                    </div>
                </div>
                <Statement style="background-color: inherit"></Statement>
            </section>
        </template>
        <template v-else>
            <div class="productOut">
                <div class="productOut-img">
                    <img alt="产品下线" src="@/assets/images/productOut.png" />
                </div>
                <div class="productOut-text">当前产品已下线，请尝试别的业务。</div>
            </div>
        </template>
    </LayoutPage>
    <!-- 弹窗 -->
    <VanDialog
        v-model:show="visible"
        show-cancel-button
        title="温馨提示"
        @cancel="visible = false"
        @confirm="onOkAgreement"
    >
        <div class="detail-agreement pop">
            <span>请阅读并同意</span>
            <a :href="agreementUrl">《第三方服务及个人信息授权》</a>
        </div>
    </VanDialog>
    <!-- 借款申请须知 -->
    <!-- <van-dialog title="借款申请须知" v-model:show="noticeVisible" show-cancel-button>
        <div class="notice">
            <div v-for="item in 1" :key="1" class="notice-title">
                <div>产品：{{ data.name + "(" + data.partner_name + ")" }}</div>
            </div>
            <section class="notice-h2">您正在申请借贷请您充分阅读此内容:</section>
            <section class="notice-content">
                <p>
                    1.正规放贷机构不会以解冻资金、交保证金、做银行流水或者资质审核为由先行收取费用！以“验资费”、“保证金”等理由收款的行为都是诈骗，如遇到请勿支付。
                </p>
                <p>2.正规放贷机构不会向您索要银行卡密码和短信验证码，请勿将此提供给陌生人。</p>
                <p>
                    3.正规放贷机构不会单独向用户收费或要求线下还款、对私打款或银行转账等，或冒充公检法等机关要求您提供相关个人信息，如遇到以上情况请拒绝。
                </p>
                <p>
                    4.本平台作为移动金融智选平台，本身不从事资金放贷，向您展示的贷款产品由第三方机构提供，您在选择贷款产品前应仔细阅读相关产品信息及注意风险。
                </p>
                <p>
                    5.基于协助您申请产品之目的，您授权本平台将您提供的产品申请所需信息（包括但不限于姓名、手机号码、身份证号码及您申请时所填列的相关个人信息等）将提供给为您办理贷款服务的第三方机构，请您注意接听相关来电。
                </p>
                <p>6.网络贷款有风险，请仔细分辨和识别，谨防诈骗！</p>
                <p>
                    7.如您遇到疑似诈骗时，要及时拨打反诈专线 96110
                    进行咨询；如您不慎被骗，请立即拨打110报警，并保存聊天记录、转账记录等相关证据，切勿通过网上去寻找维权机构，避免二次上当受骗！
                </p>
            </section>
        </div>
        <template #footer>
            <div class="notice-btnBox">
                <button data-collect="productSubmitAgreement" class="notice-btn" @click="onSubmit">
                    我已悉知
                </button>
            </div>
        </template>
    </van-dialog> -->
</template>
<script lang="ts" setup>
import { getProductInfo } from "@/apis";
import { setTitle } from "@/utils/index";
import { CheckCircleFill } from "@/assets/svg/index";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import AppButton from "@/components/App/Button.vue";
import { Dialog } from "vant";
import { useGlobalStore } from "@/stores/global";
import Statement from "@/gadget/statement/index.vue";
import moment from "moment";
import useAgreement from "@/hook/useAgreement";
import { submitH5Commit } from "@/gadget//productList/submit";
import signTips from "@/gadget/signTips.vue";

const store = useGlobalStore();
const VanDialog = Dialog.Component;
const route = useRoute();
const product_id = ref(route.query.id);
const data = ref<any>({});
// 是否勾选隐私协议
const agreement = ref(false);
// const localStorageKey = `auth_${$uid.value}`
const localStorageKey = `authData_${store.uid}`;
const authData = JSON.parse(localStorage.getItem(localStorageKey) ?? "{}");
const visible = ref<boolean>(false);
const noticeVisible = ref<boolean>(false);
const agreementUrl = ref<string>("");

onMounted(() => {
    getProductInfo({ product_id: route.query.id, order_id: route.query.order_id })
        .then((res: any) => {
            data.value = res.data;
            setTitle(data.value?.name);
            return res.data;
        })
        .then((res) => {
            const { useAgreementUrl } = useAgreement(
                "https://g.hhhtfin.com/agreement/xyr!WJjyl_f",
                {
                    name: authData.name,
                    idno: "",
                    p: res.name,
                    uid: store.uid,
                    date: moment(new Date()).format("YYYY年MM月DD日")
                }
            );
            agreementUrl.value = useAgreementUrl;
        });
    visible.value = false;
    noticeVisible.value = false;
    if (product_id.value !== route.query.id) {
        product_id.value = route.query.id;
    }
});
const showButton = ref(!route.query._s);

// 确认
const onOkAgreement = () => {
    agreement.value = true;
    onSubmit();
};
// 提交
const onSubmit = async () => {
    const params = JSON.parse(localStorage.getItem(localStorageKey) ?? "");
    const item: any = data.value;

    submitH5Commit({
        orderId: route.query.orderId,
        list: [item.id],
        name: params.name,
        idno: params.idno
    });
};
const onJudge = () => {
    // 如果没有勾选隐私协议，拦截并弹窗
    if (!agreement.value) {
        visible.value = true;
        return;
    }
    onSubmit();
};
</script>
<style lang="less" scoped>
.productOut {
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;

    .productOut-img {
        width: 268px;
        height: 214;
        margin-top: 104px;
        margin-bottom: 24px;

        img {
            width: 268px;
            height: 214;
        }
    }

    .productOut-text {
        width: 210px;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #888888;
        line-height: 20px;
        width: 100vw;
        text-align: center;
    }
}
.productPage {
    background-color: #f6f6f6;
    min-height: 100vh;

    .productInfo {
        .headerBgi {
            background: url("@/assets/images/product/productBgi2.png") no-repeat;
            background-size: 100% 122px;
            width: 100%;
            height: 122px;
        }

        .infoContent {
            margin: -92px 12px 10px 12px;
            height: 183px;
            background: url("@/assets/images/product/productBgi3.png") no-repeat;
            background-size: cover;
            position: relative;

            .infoContent-productName {
                display: flex;
                margin-top: 20px;
                margin-bottom: 10px;
                justify-content: center;
                height: 33px;
                align-items: center;

                .logo {
                    width: 24px;
                    height: 24px;
                    margin-right: 4px;
                }

                .name {
                    font-size: 24px;
                    font-family: PingFangSC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #222222;
                    line-height: 33px;
                }
            }

            .infoContent-company {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #555555;
                line-height: 17px;
                text-align: center;
            }

            .infoContent-tags {
                display: flex;
                justify-content: space-around;
                margin-top: 16px;

                .tags-item {
                    text-align: center;

                    .value {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #3c56e4ff;
                        line-height: 17px;
                    }

                    .key {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #888888;
                        line-height: 17px;
                    }
                }
            }

            .contentBottom {
                position: absolute;
                left: 0;
                bottom: 0;
                background-color: #fff0d9;
                height: 30px;
                line-height: 30px;
                width: 100%;

                span {
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #888888;
                    display: inline-block;
                    transform: scale(0.9);
                    white-space: nowrap;
                }
            }
        }

        .descWrap {
            margin: 0 12px;
            background-color: #fff;
            border-radius: 8px;
            padding: 16px 12px 11px 12px;
            margin-bottom: 20px;

            .title {
                font-size: 14px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #333333;
                line-height: 20px;
                margin-bottom: 8px;
            }

            .item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 3px;

                p {
                    display: inline-block;
                    transform: scale(0.9);
                }

                .key {
                    // flex: 0.3;
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #555555;
                    line-height: 16px;
                }

                .value {
                    flex: 0.8;
                    font-size: 12px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #888888;
                    line-height: 16px;
                    text-align: right;
                }
            }
        }

        .submitWrap {
            margin-bottom: 16px;
            padding: 0 12px;

            .checkbox {
                margin-bottom: 10px;
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #444444;
                line-height: 18px;
                display: flex;

                .adm-checkbox-content {
                    span {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #666;
                        line-height: 17px;
                    }

                    a {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #eb6b3b;
                        line-height: 17px;
                    }
                }

                .adm-checkbox-icon {
                    position: relative;
                    top: 3px;
                    left: 1px;
                    flex: none;
                    border: 1px solid #cccccc;
                    border-radius: 50%;
                    box-sizing: border-box;
                    width: 12px;
                    height: 12px;
                    color: #fff;
                    margin-right: 6px;
                    display: inline-block;
                }

                .circle {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    border: 1px solid #999;

                    :deep(svg) {
                        display: none;
                    }

                    &.cur {
                        border: none;

                        :deep(svg) {
                            width: 100%;
                            height: 100%;
                            display: block;
                            color: rgb(238, 85, 51);
                        }
                    }
                }

                a {
                    color: #ee5533;
                }
            }

            .global-btn {
                color: #ee5533;
                color: #fff;
            }
        }

        .processWrap {
            background-color: #fff;
            margin: 0 12px;
            border-radius: 8px;
            padding-bottom: 16px;
            box-sizing: border-box;

            .title {
                font-size: 14px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #2c2c2c;
                line-height: 20px;
                padding: 17px 0 18px 12px;
            }

            .process {
                display: flex;
                justify-content: space-around;
                padding: 0 10px;

                .item {
                    text-align: center;
                    width: 72px;
                    position: relative;

                    &:not(:last-child)::before {
                        content: "";
                        display: block;
                        position: absolute;
                        right: -12px;
                        top: 21px;
                        width: 12px;
                        height: 12px;
                        background-image: url(https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/icon3.png);
                        background-repeat: no-repeat;
                        background-size: 100% 100%;
                    }

                    .item-img {
                        width: 58px;
                        height: 57px;
                        display: inline-block;
                    }

                    .label {
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #333333;
                        line-height: 17px;
                    }
                }
            }
        }
    }
}

.notice {
    padding: 0 20px;

    // background-image: linear-gradient(to top, #fff 30%, #a8edea 100%);
    p {
        padding: 5px 5px;
    }

    .notice-title {
        margin-top: 5px;
        padding: 0 5px;
        margin-bottom: 5px;
        font-size: 14px;
    }

    .notice-h2 {
        font-size: 13px;
        color: #e44c2a;
        padding: 0 5px;
        margin-bottom: 8px;
    }

    .notice-content {
        // padding: 0 5px;
        height: 200px;
        overflow: auto;
        font-size: 12px;
        text-align: inherit;

        &::-webkit-scrollbar-thumb {
            width: 1px;
            background-color: #99999980;
        }

        &::-webkit-scrollbar {
            width: 1px;
        }
    }
}

.notice-btnBox {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;

    .notice-btn {
        border: none;
        width: 80%;
        margin: 0 auto;
        height: 40px;
        border-radius: 20px;
        color: #fff;
        background-color: #e44c2a;
    }
}

.detail-agreement {
    text-align: center;
}
</style>
