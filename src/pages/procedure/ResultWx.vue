<template>
    <LayoutPage class="loan-result-wx-page">
        <div class="result-content">
            <!-- 标题 -->
            <div class="title">正在前往微信借款...</div>

            <!-- 微信logo -->
            <div class="wechat-logo">
                <img src="@/assets/images/result/wx_logo.png" alt="微信图标" />
            </div>

            <!-- 金额信息 -->
            <div class="amount-info">
                <div class="amount-value">10万</div>
                <div>最高可借（元）</div>
            </div>

            <!-- 专享服务 -->
            <div class="service-section"></div>

            <!-- 底部说明文字 -->
            <div class="disclaimer">
                <span>本人已知晓</span>
                <span class="highlight" @click="openAgreementDialog">《个人信息查询授权协议》</span>
                <span>的所有内容，同意并授权平台推荐产品</span>
            </div>

            <!-- 立即借款按钮 -->
            <div class="submit-btn-wrapper">
                <button class="submit-btn" @click="handleSubmit">
                    立即借款 ({{ countdown }}s)
                </button>
            </div>

            <!-- 底部协议文字 -->
            <div class="agreement-text">
                <img
                    src="@/assets/images/result/wx_icon.png"
                    alt="微信图标"
                    width="16"
                    height="16"
                />
                <div>使用您本人微信验证后才可领取额度</div>
            </div>
        </div>

        <!-- 个人信息授权协议弹窗 -->
        <VanDialog v-model:show="showStatement" show-cancel-button title="个人信息授权">
            <div class="statement-content">
                <div class="statement-body" style="padding: 0 !important; margin-top: 10px">
                    <div class="statement-text scrollable">
                        <AgreementView :data="agreementViewData" :height="'100%'"></AgreementView>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="statement-footer">
                    <button class="confirm-button" @click="confirmStatement">我已阅读并同意</button>
                </div>
            </template>
        </VanDialog>

        <!-- 二维码弹窗 -->
        <div class="qrcode-popup" v-if="showQrcode">
            <div class="qrcode-container">
                <div class="qrcode-title">请使用微信扫描二维码</div>
                <div class="qrcode-content">
                    <img
                        v-if="qrcodeDataUrl"
                        :src="qrcodeDataUrl"
                        class="qrcode-image"
                        alt="微信二维码"
                    />
                    <div v-else class="qrcode-loading">生成二维码中...</div>
                </div>
                <div class="qrcode-tip">请用微信扫描此二维码继续操作</div>
                <button class="close-btn" @click="closeQrcode">关闭</button>
            </div>
        </div>

        <!-- 使用封装的挽留弹窗组件 -->
        <RetainDialog ref="retainDialogRef" @continue="continueSubmit" />
    </LayoutPage>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, computed, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Dialog, Toast } from "vant";
import LayoutPage from "@/components/layout/page.vue";
import { useGlobalStore } from "@/stores/global";
import { creatUrl } from "@/utils";
import AgreementView from "@/gadget/agreementView/index.vue";
import moment from "moment";
import QRCode from "qrcode";
import { resultJump } from "@/apis";

const router = useRouter();
const store = useGlobalStore();
const VanDialog = Dialog.Component;

// 弹窗状态
const showStatement = ref(false);
const agreementViewData = ref<any>({});

// 倒计时相关
const countdown = ref(4);
let timer: number | null = null;

// 微信客服相关
const wxServiceUrl = ref(""); // 微信客服URL
const showQrcode = ref(false); // 控制二维码弹窗显示
const qrcodeDataUrl = ref(""); // 二维码图片的Data URL

// 获取微信客服链接
const fetchWechatUrl = async () => {
    try {
        const route = useRoute();
        const url = decodeURIComponent(route.query.wxUrl as string);
        wxServiceUrl.value = url;
        const newUrl = creatUrl(wxServiceUrl.value, { _c: store.channel });
        console.log("newUrl -->", newUrl);
        if (newUrl) {
            wxServiceUrl.value = newUrl;
        }

        console.log("获取微信链接成功 -->", wxServiceUrl.value);
    } catch (err) {
        console.log("获取微信链接失败 -->", err);
        Toast("获取微信链接失败，请稍后再试");
    }
};

// 生成二维码
const generateQRCode = async () => {
    if (!wxServiceUrl.value) return;

    try {
        // 使用QRCode库生成二维码为dataURL
        const dataUrl = await QRCode.toDataURL(wxServiceUrl.value, {
            width: 200,
            margin: 1,
            color: {
                dark: "#000000",
                light: "#ffffff"
            }
        });

        // 设置二维码图片源
        qrcodeDataUrl.value = dataUrl;
        console.log("二维码生成成功");
    } catch (error) {
        console.error("生成二维码失败:", error);
        Toast("二维码生成失败，请稍后再试");
    }
};

// 显示二维码弹窗
const showQrcodePopup = () => {
    showQrcode.value = true;
    nextTick(() => {
        generateQRCode();
    });
};

// 关闭二维码弹窗
const closeQrcode = () => {
    showQrcode.value = false;
};

// 开始倒计时
const startCountdown = () => {
    timer = window.setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--;
        } else {
            clearInterval(timer as number);
            handleSubmit();
        }
    }, 1000);
};

// 检测设备类型
const getDeviceType = () => {
    const ua = navigator.userAgent;
    if (/iPhone|iPad|iPod/i.test(ua)) {
        return "ios";
    } else if (/Android/i.test(ua)) {
        return "android";
    }
    return "other";
};

// 尝试打开微信客服链接
const tryOpenWeChatService = (serviceUrl: string) => {
    // 如果没有获取到URL，直接显示二维码
    if (!serviceUrl) {
        console.log("没有有效的微信URL，直接显示二维码");
        showQrcodePopup();
        return;
    }

    // 检测是否成功打开
    let hasOpened = false;
    const deviceType = getDeviceType();

    // 根据设备类型尝试不同的方式打开微信
    const tryOpenByDeviceType = () => {
        if (deviceType === "ios") {
            console.log("iOS设备，使用a标签打开");
            // 创建一个a标签模拟点击并新标签打开
            const a = document.createElement("a");
            a.href = serviceUrl;
            a.target = "_blank";
            a.rel = "noopener noreferrer";
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        } else if (deviceType === "android") {
            console.log("Android设备，使用window.open打开");
            window.open(serviceUrl, "_blank");
        }
    };

    // 页面隐藏事件处理
    const visibilityChangeHandler = () => {
        if (document.hidden) {
            hasOpened = true;
            console.log("页面隐藏，可能已成功唤醒微信");
            document.removeEventListener("visibilitychange", visibilityChangeHandler);
            window.removeEventListener("blur", blurHandler);
        }
    };

    // 页面失焦事件处理
    const blurHandler = () => {
        hasOpened = true;
        console.log("页面失焦，可能已成功唤醒微信");
        document.removeEventListener("visibilitychange", visibilityChangeHandler);
        window.removeEventListener("blur", blurHandler);
    };

    // 添加事件监听
    document.addEventListener("visibilitychange", visibilityChangeHandler);
    window.addEventListener("blur", blurHandler);

    // 尝试使用window.open打开
    console.log("尝试使用window.open打开微信客服:", serviceUrl);
    const openWindow = window.open(serviceUrl, "_blank");

    // 检查window.open是否被浏览器阻止
    const isWindowOpenBlocked =
        !openWindow || openWindow.closed || typeof openWindow.closed === "undefined";

    // 如果window.open被阻止，立即尝试设备特定的打开方式
    if (isWindowOpenBlocked) {
        console.log("window.open被浏览器阻止，尝试设备特定的打开方式");
        tryOpenByDeviceType();
    }

    // 设置超时检测
    setTimeout(() => {
        // 移除事件监听
        document.removeEventListener("visibilitychange", visibilityChangeHandler);
        window.removeEventListener("blur", blurHandler);

        // 如果两秒后页面没有失焦，说明可能没有成功打开微信
        if (!hasOpened) {
            console.log("无法打开微信，显示二维码");
            showQrcodePopup();
        }
    }, 1500);
};

// 立即借款按钮点击事件
const handleSubmit = () => {
    if (timer) {
        clearInterval(timer);
    }

    // 如果已经获取到了微信客服链接，尝试打开
    if (wxServiceUrl.value) {
        Dialog.confirm({
            title: "提示",
            message: "即将离开网页跳转至微信，是否继续?",
            confirmButtonText: "确认",
            cancelButtonText: "取消"
        })
            .then(() => {
                // 确认后尝试打开微信客服
                tryOpenWeChatService(wxServiceUrl.value);
            })
            .catch(() => {
                // 用户取消操作
                console.log("用户取消跳转");
            });
    } else {
        // 如果没有获取到微信客服链接，尝试重新获取
        fetchWechatUrl().then(() => {
            if (wxServiceUrl.value) {
                tryOpenWeChatService(wxServiceUrl.value);
            } else {
                // 还是获取不到链接，直接显示二维码
                showQrcodePopup();
            }
        });
    }
};

const continueSubmit = () => {
    handleSubmit();
};

// 打开个人信息授权协议弹窗
const openAgreementDialog = () => {
    // 初始化协议数据
    initAgreementData();
    showStatement.value = true;
};

// 确认协议
const confirmStatement = () => {
    showStatement.value = false;
};

// 初始化协议数据
const initAgreementData = () => {
    const localStorageKey = `authData_${store.uid}`;
    const authData = JSON.parse(localStorage.getItem(localStorageKey) ?? "{}");

    agreementViewData.value = {
        url: window.location.origin + import.meta.env.VITE_RECOMMEND_AGREEMENT,
        query: {
            name: authData.idCard?.name || "",
            idno: "",
            p: "微信贷款",
            uid: store.uid,
            date: moment(new Date()).format("YYYY年MM月DD日")
        }
    };
};

onMounted(() => {
    startCountdown();
    // 初始化协议数据
    initAgreementData();
    // 获取微信客服链接
    fetchWechatUrl();
});

onBeforeUnmount(() => {
    if (timer) {
        clearInterval(timer);
    }
});
</script>

<style lang="less" scoped>
.loan-result-wx-page {
    min-height: 100vh;
    font-family: PingFangSC, PingFang SC, sans-serif;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;

    .result-content {
        width: 100%;
        max-width: 375px;
        padding: 0 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;

        // 顶部标题
        .title {
            font-size: 20px;
            color: #333;
            font-weight: 500;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        // 微信logo
        .wechat-logo {
            width: 60px;
            height: 60px;
            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }

        // 金额信息
        .amount-info {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin: 35px 0;
        }

        // 专享服务区域
        .service-section {
            background: url("@/assets/images/result/wx_tips.png") no-repeat center center/contain;
            border-radius: 12px;
            box-sizing: border-box;
            margin-bottom: 30px;
            width: 297px;
            height: 107px;

            .service-title {
                font-size: 14px;
                color: #333;
                margin-bottom: 15px;
            }

            .service-list {
                display: flex;
                justify-content: space-around;

                .service-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .service-icon {
                        width: 30px;
                        height: 30px;
                        margin-bottom: 5px;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                        }
                    }

                    .service-name {
                        font-size: 12px;
                        color: #666;
                    }
                }
            }
        }

        // 免责声明
        .disclaimer {
            font-size: 12px;
            color: #999;
            margin-bottom: 15px;
            line-height: 1.5;
            padding: 0 10px;

            .highlight {
                color: #4285f4;
            }
        }

        // 提交按钮
        .submit-btn-wrapper {
            width: 100%;
            margin-bottom: 15px;

            .submit-btn {
                width: 100%;
                height: 44px;
                background-color: #4285f4;
                border-radius: 22px;
                color: white;
                font-size: 16px;
                font-weight: 500;
                border: none;
                outline: none;
            }
        }

        // 底部协议文字
        .agreement-text {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 12px;
            color: #999;
            margin-bottom: 20px;
        }
    }
}

// 弹窗样式
.statement-content {
    padding: 0 16px;

    .statement-header h4 {
        margin: 16px 0;
        color: #4285f4;
        font-size: 14px;
        font-weight: 400;
    }

    .statement-body {
        padding: 16px;
        background: #f9f9f9;
        border-radius: 4px;

        .scrollable {
            max-height: 50vh;
            overflow-y: auto;
            font-size: 12px;
            color: #333;
            line-height: 1.6;

            p {
                margin-bottom: 8px;
            }

            &::-webkit-scrollbar {
                width: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 2px;
            }
        }
    }
}

.statement-footer {
    display: flex;
    justify-content: center;
    padding: 16px 0;

    .confirm-button {
        width: 80%;
        height: 40px;
        background-color: #4285f4;
        color: #fff;
        border: none;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;
    }
}

// 二维码弹窗样式
.qrcode-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;

    .qrcode-container {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 40px;
        width: 90%;
        max-width: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .qrcode-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .qrcode-content {
            margin: 10px 0;
            padding: 10px;
            background-color: #fff;
            border: 1px solid #eee;
            width: 200px;
            height: 200px;
            display: flex;
            justify-content: center;
            align-items: center;

            .qrcode-image {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            .qrcode-loading {
                font-size: 14px;
                color: #666;
            }
        }

        .qrcode-tip {
            font-size: 14px;
            color: #666;
            margin: 15px 0;
            text-align: center;
        }

        .close-btn {
            margin-top: 15px;
            background-color: #4285f4;
            color: #ffffff;
            font-size: 14px;
            border-radius: 15px;
            height: 30px;
            line-height: 30px;
            width: 80px;
            border: none;
            cursor: pointer;
        }
    }
}
</style>
