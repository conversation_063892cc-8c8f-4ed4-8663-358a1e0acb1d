<template>
    <LayoutPage class="productPage">
        <template v-if="data">
            <div class="info">
                <div class="header">
                    <div class="card">
                        <div v-if="store.channel === 'tttr'" class="titleWrap"></div>
                        <div v-else class="titleWrap">
                            <img :src="data.logo" alt="" class="logo" />
                            <p class="title">{{ data.name }}</p>
                        </div>
                        <p class="amount">
                            {{
                                data.loan_max &&
                                String(data.loan_max).replace(/(\d)(?=(?:\d{3})+$)/g, "$1,")
                            }}
                        </p>
                        <div class="advantages">
                            <div v-for="(item, index) in data.advantages" :key="index" class="item">
                                <p class="value">{{ item.value }}</p>
                                <p class="key">{{ item.key }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="describe">
                    <span class="notices">
                        办理贷款，平台不会向用户收取任何费用；谨防电话、微信诈骗 实际由{{
                            data.partner_name
                        }}提供信息服务；
                    </span>
                </div>
                <div class="productDescribe">
                    <div class="descWrap">
                        <!-- 产品说明 -->
                        <p class="title">产品说明</p>
                        <div class="description">
                            <div
                                v-for="(item, index) in data.description"
                                :key="index"
                                class="item"
                            >
                                <p class="key">{{ item.key }}</p>
                                <p class="value">{{ item.value }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="processWrapBox">
                    <div class="processWrap">
                        <p class="title">申请流程</p>
                        <div class="process">
                            <div v-for="(item, index) in data.process" :key="index" class="item">
                                <div class="imgWrap">
                                    <img :src="item.logo" alt="" class="logo" />
                                </div>
                                <p class="label">{{ item.key }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <Statement style="background-color: inherit"></Statement>
            </div>
            <div v-if="showButton" class="submitWrap">
                <!-- 隐私协议 -->
                <div v-if="data.partner_agreement?.length" class="checkbox">
                    <div
                        :class="{ cur: agreement }"
                        class="adm-checkbox-icon circle"
                        @click="agreement = !agreement"
                        v-html="CheckCircleFill"
                    ></div>
                    <div class="adm-checkbox-content">
                        阅读并同意
                        <a
                            v-for="(item, index) in data.partner_agreement"
                            :key="index"
                            :href="item.value"
                            >{{ item.key }}</a
                        >
                    </div>
                </div>
                <AppButton
                    class="global-btn"
                    data-collect="productSubmitAgreement"
                    type="primary"
                    @click="onJudge"
                >
                    立即提交
                </AppButton>
            </div>
        </template>
        <template v-else>
            <div class="productOut">
                <div class="productOut-img">
                    <img alt="产品下线" src="@/assets/images/productOut.png" />
                </div>
                <div class="productOut-text">当前产品已下线，请尝试别的业务。</div>
            </div>
        </template>

        <!-- <Head>
            <Title>{{ data?.name ?? " " }}</Title>
        </Head> -->
    </LayoutPage>
    <!-- 弹窗 -->
    <!-- 弹窗 -->
    <van-dialog
        v-model:show="visible"
        show-cancel-button
        title="温馨提示"
        @cancel="visible = false"
        @confirm="onOkAgreement"
    >
        <div class="detail-agreement pop">
            <span>请阅读并同意</span>
            <a v-for="(item, index) in data.partner_agreement" :key="index" :href="item.value">{{
                item.key
            }}</a>
        </div>
    </van-dialog>
    <van-dialog v-model:show="noticeVisible" show-cancel-button title="借款申请须知">
        <div class="notice">
            <div v-for="item in 1" :key="1" class="notice-title">
                <div>产品：{{ data.name + "(" + data.partner_name + ")" }}</div>
            </div>
            <section class="notice-h2">您正在申请借贷请您充分阅读此内容:</section>
            <section class="notice-content">
                <p>
                    1.正规放贷机构不会以解冻资金、交保证金、做银行流水或者资质审核为由先行收取费用！以“验资费”、“保证金”等理由收款的行为都是诈骗，如遇到请勿支付。
                </p>
                <p>2.正规放贷机构不会向您索要银行卡密码和短信验证码，请勿将此提供给陌生人。</p>
                <p>
                    3.正规放贷机构不会单独向用户收费或要求线下还款、对私打款或银行转账等，或冒充公检法等机关要求您提供相关个人信息，如遇到以上情况请拒绝。
                </p>
                <p>
                    4.本平台作为移动金融智选平台，本身不从事资金放贷，向您展示的贷款产品由第三方机构提供，您在选择贷款产品前应仔细阅读相关产品信息及注意风险。
                </p>
                <p>
                    5.基于协助您申请产品之目的，您授权本平台将您提供的产品申请所需信息（包括但不限于姓名、手机号码、身份证号码及您申请时所填列的相关个人信息等）将提供给为您办理贷款服务的第三方机构，请您注意接听相关来电。
                </p>
                <p>6.网络贷款有风险，请仔细分辨和识别，谨防诈骗！</p>
                <p>
                    7.如您遇到疑似诈骗时，要及时拨打反诈专线 96110
                    进行咨询；如您不慎被骗，请立即拨打110报警，并保存聊天记录、转账记录等相关证据，切勿通过网上去寻找维权机构，避免二次上当受骗！
                </p>
            </section>
        </div>
        <template #footer>
            <div class="notice-btnBox">
                <button class="notice-btn" data-collect="productSubmitAgreement" @click="onSubmit">
                    我已悉知
                </button>
            </div>
        </template>
    </van-dialog>
</template>
<script lang="ts" setup>
import { commitProducts, getProductInfo } from "@/apis";
import { setTitle } from "@/utils/index";
import { CheckCircleFill } from "@/assets/svg/index";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Dialog, Toast } from "vant";
import AppButton from "@/components/App/Button.vue";
import { useGlobalStore } from "@/stores/global";
import { reportConversion } from "@/actions/conversion";
import Statement from "@/gadget/statement/index.vue";

const store = useGlobalStore();
const VanDialog = Dialog.Component;
const route = useRoute();
const router = useRouter();
const product_id = ref(route.query.id);
const data = ref<any>({});
const loading = ref<boolean>(false);
// 是否勾选隐私协议
const agreement = ref(!(data.value?.partner_agreement?.length > 0));
// const localStorageKey = `auth_${$uid.value}`
const localStorageKey = `authData_${store.uid}`;
const visible = ref<boolean>(false);
const noticeVisible = ref<boolean>(false);

const $nuxtLink = (path: string, options: any) => {
    router.push({
        path,
        ...options
    });
};

onMounted(() => {
    getProductInfo({ product_id: route.query.id }).then((res: any) => {
        console.log(res.data);

        data.value = res.data;
        setTitle(data.value?.name);
    });
    visible.value = false;
    noticeVisible.value = false;
    if (product_id.value !== route.query.id) {
        product_id.value = route.query.id;
    }
});
const showButton = ref(!route.query._s);

// 确认
const onOkAgreement = () => {
    agreement.value = true;
};
// 提交
const onSubmit = async () => {
    const submitToast = Toast.loading({
        duration: 0,
        message: "提交信息"
    });
    const params = JSON.parse(localStorage.getItem(localStorageKey) ?? "");
    const item: any = data.value;
    const commitInfoResult: any = await commitProducts({
        orderId: route.query.orderId,
        list: [item.id],
        name: params.name,
        idno: params.idno
    });
    // console.log(commitInfoResult);
    if (commitInfoResult.data?.url) {
        // console.log('成功');
        // $nuxtLink("/authorization/partnerAuth", {
        //     query: { target: encodeURIComponent(commitInfoResult.data?.url) },
        //     replace: true,
        // });
        window.location.href = decodeURIComponent(commitInfoResult.data?.url);
    } else if (commitInfoResult?.data) {
        $nuxtLink("/procedure/result", { query: { orderId: route.query.orderId }, replace: true });
        reportConversion("formSuccess");
    } else {
        Toast.fail(commitInfoResult?.message);
    }

    submitToast.clear();
    loading.value = false;
};
const onJudge = () => {
    // 如果没有勾选隐私协议，拦截并弹窗
    if (!agreement.value) {
        visible.value = true;
        return;
    }
    noticeVisible.value = true;
};
</script>
<style lang="less" scoped>
.detail-agreement {
    text-align: center;
}

.productPage {
    .productOut {
        width: 100vw;
        height: 100vw;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;

        .productOut-img {
            width: 268px;
            height: 214;
            margin-top: 104px;
            margin-bottom: 24px;

            img {
                width: 268px;
                height: 214;
            }
        }

        .productOut-text {
            width: 210px;
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #888888;
            line-height: 20px;
            width: 100vw;
            text-align: center;
        }
    }

    .info {
        padding-bottom: 100px;
    }

    .header {
        background-size: 100%;
        background-repeat: no-repeat;
        padding: 20px;
        min-height: 286px;
        background-image: url("@/assets/images/bg2.jpg");

        .card {
            margin: 0;
            min-height: 218px;
            border-radius: 8px;
            padding: 20px 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 19px;

            .titleWrap {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                .logo {
                    width: 72px;
                    height: 72px;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }

                font-size: 20px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #fff;
                line-height: 28px;
            }

            .amount {
                margin-top: 14px;
                width: 276px;
                line-height: 56px;
                text-align: center;
                font-size: 56px;
                font-weight: 500;
                font-family: "Din";
                color: #ffffff;
            }

            .advantages {
                margin-top: 23px;
                display: flex;
                align-items: center;
                justify-content: space-around;
                min-width: 250px;

                .item {
                    text-align: center;

                    .value {
                        font-size: 18px;
                        line-height: 25px;
                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #ffffff;
                    }

                    .key {
                        height: 17px;
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #ffffff80;
                        line-height: 17px;
                    }
                }
            }
        }
    }

    .describe {
        display: flex;
        justify-content: center;
        margin-top: -40px;
        margin-bottom: 16px;

        .notices {
            background-color: #fff;
            padding: 12px 16px;
            width: 100%;
            margin: 0 20px;
            box-sizing: border-box;
            box-shadow: 0px 0px 8px 0px rgba(222, 222, 222, 0.5);
            border-radius: 8px;
            font-size: 10px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #888888;
            line-height: 18px;
            text-align: justify;
        }
    }

    .productDescribe {
        display: flex;
        justify-content: center;

        .descWrap {
            margin: 0 20px;
            width: 100%;
            background-color: #fff;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0px 0px 8px 0px rgba(222, 222, 222, 0.5);

            .title {
                height: 25px;
                font-size: 18px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #222222;
                line-height: 25px;
                margin-bottom: 16px;
            }

            .item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;

                .key {
                    width: 80px;
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #555555;
                    line-height: 20px;
                }

                .value {
                    flex: 1;
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    line-height: 20px;
                }
            }
        }
    }

    .processWrapBox {
        display: flex;
        justify-content: center;
        margin: 16px 0 8px 0;

        .processWrap {
            padding: 28px 16px;
            width: 100%;
            margin: 0 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0px 0px 8px 0px rgba(222, 222, 222, 0.5);

            .title {
                height: 25px;
                font-size: 18px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #222222;
                line-height: 25px;
            }

            .process {
                margin-top: 18px;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                overflow: scroll;

                &::-webkit-scrollbar {
                    display: none;
                }

                .item {
                    flex-grow: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    position: relative;
                    margin-right: 39px;
                    text-align: center;

                    &:first-child {
                        &::before {
                            display: none;
                        }
                    }

                    &:last-child {
                        &::after {
                            display: none;
                        }
                    }

                    &::after {
                        content: "";
                        display: block;
                        position: absolute;
                        right: -25px;
                        top: 28%;

                        width: 9px;
                        height: 10px;
                        background-image: url(https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/icon3.png);
                        background-repeat: no-repeat;
                        background-size: 100% 100%;
                    }

                    .imgWrap {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        position: relative;

                        .right {
                            position: absolute;

                            left: -14px;
                            top: 50%;
                            width: 9px;
                            transform: translateY(-50%);
                            height: 16px;
                            margin-right: 14px;
                        }
                    }

                    .logo {
                        width: 58px;
                        height: 57px;
                        margin-bottom: 6px;
                    }

                    .label {
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #444444;
                        line-height: 20px;
                        white-space: nowrap;
                        text-align: center;
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .tips {
        margin: 0 16px 30px;
        padding: 8px;
        height: 66px;
        border-radius: 3px;

        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 16px;
    }

    .submitWrap {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fefffe;
        box-shadow: 0px -6px 10px 0px rgba(0, 0, 0, 0.02);
        border-radius: 20px 20px 0px 0px;
        padding: 10px 19px 16px;
        padding-bottom: calc(10px + var(---safe-area-bottom));

        .checkbox {
            margin-bottom: 10px;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #444444;
            line-height: 18px;
            position: relative;
            padding-left: 28px;

            .adm-checkbox-icon {
                position: absolute;
                left: 0;
                top: -2px;
            }

            a {
                color: #ee5533;
            }
        }

        .submit {
            line-height: 50px;
            background: #ee5533;
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.06);
            border-radius: 25px;
            text-align: center;
            font-size: 18px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #fefffe;
        }
    }
}

.adm-checkbox {
    display: flex;
    flex-direction: row;
}

.adm-checkbox-icon {
    flex: none;
    border: 1px solid #cccccc;
    border-radius: 50%;
    box-sizing: border-box;
    width: 18px;
    height: 18px;
    color: #fff;
    margin-right: 6px;
}

.circle {
    width: 21px;
    height: 21px;
    border-radius: 50%;
    border: 1px solid #999;

    :deep(svg) {
        display: none;
    }

    &.cur {
        border: none;

        :deep(svg) {
            width: 100%;
            height: 100%;
            display: block;
            color: rgb(238, 85, 51);
        }
    }
}

.notice {
    padding: 0 20px;

    // background-image: linear-gradient(to top, #fff 30%, #a8edea 100%);
    p {
        padding: 5px 5px;
    }

    .notice-title {
        margin-top: 5px;
        padding: 0 5px;
        margin-bottom: 5px;
        font-size: 14px;
    }

    .notice-h2 {
        font-size: 13px;
        color: #e44c2a;
        padding: 0 5px;
        margin-bottom: 8px;
    }

    .notice-content {
        // padding: 0 5px;
        height: 200px;
        overflow: auto;
        font-size: 12px;
        text-align: inherit;

        &::-webkit-scrollbar-thumb {
            width: 1px;
            background-color: #99999980;
        }

        &::-webkit-scrollbar {
            width: 1px;
        }
    }
}

.notice-btnBox {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;

    .notice-btn {
        border: none;
        width: 80%;
        margin: 0 auto;
        height: 40px;
        border-radius: 20px;
        color: #fff;
        background-color: #e44c2a;
    }
}
</style>
