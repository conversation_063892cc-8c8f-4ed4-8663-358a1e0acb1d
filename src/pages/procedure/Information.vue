<template>
    <div class="information-container">
        <div v-for="item in informationData" :key="item.key" class="information-card">
            <div class="title">{{ item.label }}</div>
            <div class="content">{{ item.value }}</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { onBeforeMount, ref } from "vue";
import { getInformation } from "@/apis/serve/informationServe";
import { getCertificationConfig } from "@/apis/index";
import { analysisData } from "@/utils/procedure";

const informationData = ref<any>([]);

onBeforeMount(() => {
    handle();
});

const handle = async () => {
    const { data = [] } = await getCertificationConfig("auth,complete,will");
    const userInfo = (await getInformation()) ?? { data: {} };
    const certificationConfig = analysisData(
        data
            .filter((item: any) => item.key !== "certification")
            .sort((a: any, b: any) => (b.sort ?? 0) - (a.sort ?? 0))
            .map((item: any, index: number) => {
                return {
                    ...item,
                    value: undefined,
                    index
                };
            })
    );
    informationData.value = certificationConfig.map((element) => {
        element.value = userInfo.data[element.key];
        return element;
    });
};
</script>
<style lang="less" scoped>
.information-container {
    background-color: #fff;
    padding: 0 16px;

    .information-card {
        height: 52px;
        border-bottom: 1px solid #fff3f3f3;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        .title {
            font-weight: 600;
            color: #333333;
            line-height: 20px;
        }

        .content {
            color: #555555;
            font-weight: 400;
            line-height: 20px;
            font-family: PingFangSC-Regular, PingFang SC;
        }
    }
}
</style>
