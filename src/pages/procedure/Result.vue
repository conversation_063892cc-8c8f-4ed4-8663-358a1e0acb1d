<template>
    <LayoutPage class="loan-result-page" v-show="!isLoading">
        <!-- 顶部状态栏 -->
        <div
            v-if="!isSuccess"
            class="status-card"
            :class="{ success: isSuccess, failure: !isSuccess }"
        >
            <div class="status-icon">
                <img
                    v-if="isSuccess"
                    src="@/assets/images/result/status_success.png"
                    alt="成功图标"
                />
                <img
                    v-else
                    src="@/assets/images/result/status_error.png"
                    alt="失败图标"
                    width="100%"
                />
            </div>
            <div class="status-content">
                <div class="status-title">
                    {{ isSuccess ? "机构申请成功" : "抱歉您的授信分不符" }}
                </div>
                <div class="status-subtitle">
                    {{ isSuccess ? "已为您匹配最优产品" : "以下为您推荐更多产品" }}
                </div>
            </div>
        </div>

        <div v-else class="success-products-section">
            <van-collapse v-model="activeNames">
                <van-collapse-item
                    v-for="(item, index) in successProductsList"
                    :key="index"
                    :title="item.name"
                    :name="index"
                >
                    <template #icon>
                        <img :src="item?.logo" class="product-icon" alt="产品图标" />
                    </template>
                    <div class="product-details">
                        <div class="product-metrics">
                            <div
                                class="metric"
                                v-for="(detail, i) in item.detail.slice(0, 3)"
                                :key="i"
                            >
                                <div class="metric-value">
                                    {{ detail.value }}
                                </div>
                                <div class="metric-label">{{ detail.key }}</div>
                            </div>
                        </div>
                        <div class="product-tips">
                            <div class="product-tips-title">恭喜！您的申请已通过</div>
                            <div class="product-tips-subtitle">
                                工作人员将稍后致电您，请保持手机畅通
                            </div>
                        </div>
                    </div>
                </van-collapse-item>
            </van-collapse>
        </div>

        <div class="agreement-box" v-if="isSuccess">
            <!-- 提示区域 -->
            <div class="warning-tips">
                <div class="warning-tips-title">温馨提示:</div>
                <div class="warning-tips-content">
                    任何要求您提前缴纳保证金、利息、服务等，或要求提供银行卡密码皆为诈骗请勿相信，并向平台举报投诉，平台将及时为您解决
                </div>
            </div>

            <!-- 协议 -->
            <van-checkbox icon-size="16px" v-model="isAgreement" style="margin-bottom: 10px">
                <span style="color: #5c5c5c">已阅读并同意</span>
                <span
                    @click.stop="(showStatement = true), (showAgreement = true)"
                    style="color: #2e6bf1"
                >
                    《个人信息授权》
                </span></van-checkbox
            >

            <!-- 确认按钮 -->
            <div class="apply-btn">
                <van-button type="primary" block round @click="confirmProducts"
                    >确认额度</van-button
                >
            </div>
        </div>

        <!-- 今日推荐区域 -->
        <div class="recommend-section">
            <div class="recommend-hot" v-if="primaryProduct?.name && !isSuccess">
                <div class="section-header">
                    <span>今日推荐</span>
                    <div class="section-tag">低门槛通道</div>
                </div>

                <!-- 主推荐产品卡片 -->
                <div class="primary-product-card">
                    <div class="product-info">
                        <div class="product-logo">
                            <img
                                :src="primaryProduct?.logo || '@/assets/images/home/<USER>'"
                                alt="产品图标"
                            />
                        </div>
                        <div class="product-name">{{ primaryProduct?.name }}</div>
                        <div class="product-desc">大额低息 放款迅速</div>
                    </div>

                    <div class="product-metrics">
                        <div
                            v-for="(item, index) in primaryProduct?.detail"
                            :key="index"
                            class="metric"
                        >
                            <div
                                class="metric-value"
                                :style="{ color: index === 1 ? 'var(--amount-color)' : '' }"
                            >
                                {{ item.value }}
                            </div>
                            <div class="metric-label">{{ item.key }}</div>
                        </div>
                    </div>

                    <div class="apply-button" @click="applyProduct(primaryProduct)">立即申请</div>
                </div>
            </div>

            <!-- 通过率提示 -->
            <div class="pass-rate-tips">
                <span>高通过率</span>
                <span class="rate-detail">申请3-5个产品下款率最高可达99%</span>
            </div>

            <!-- 产品推荐列表 -->
            <div class="product-list" v-if="recommendProducts?.length > 0">
                <div
                    v-for="(product, index) in recommendProducts"
                    :key="index"
                    class="product-item"
                >
                    <div class="product-basic-info">
                        <div class="product-logo">
                            <img
                                :src="product?.logo || '@/assets/images/home/<USER>'"
                                alt="产品图标"
                            />
                        </div>
                        <div class="product-name-box">
                            <div class="product-name">{{ product?.name }}</div>
                            <div class="product-tag">快速下款</div>
                        </div>
                    </div>

                    <div class="product-details">
                        <div
                            class="product-count"
                            v-for="(detail, i) in product.detail.slice(0, 3)"
                            :key="i + detail?.key"
                        >
                            <div class="count-value">
                                {{ detail.value }}
                            </div>
                            <div class="count-label">
                                {{ detail.key }}
                            </div>
                        </div>
                        <div class="apply-now-button" @click="applyProduct(product)">立即申请</div>
                    </div>
                </div>
            </div>

            <!-- 空列表状态 -->
            <div class="empty-product-list" v-else>
                <div class="empty-icon">
                    <img src="@/assets/images/noProduct.png" alt="没有更多产品" />
                </div>
                <div class="empty-text">暂无更多推荐产品</div>
            </div>
        </div>

        <!-- 借款申请须知弹窗 -->
        <VanDialog
            v-model:show="showStatement"
            show-cancel-button
            :title="!showAgreement ? '借款申请须知' : '个人信息授权'"
        >
            <div class="statement-content">
                <div class="statement-header">
                    <h4 v-if="!showAgreement">您正在申请借贷请您充分阅读此内容:</h4>
                </div>
                <div
                    class="statement-body"
                    :style="
                        showAgreement
                            ? 'padding: 0 !important;margin-top:10px;'
                            : 'padding: 0 15px;'
                    "
                >
                    <div class="statement-text scrollable" v-if="!showAgreement">
                        <p>
                            1.正规放贷机构不会以解冻资金、交保证金、做银行流水或者资质审核为由先行收取费用！以"验资费"、"保证金"等理由收款的行为都是诈骗，如遇到请勿支付。
                        </p>
                        <p>
                            2.正规放贷机构不会向您索要银行卡密码和短信验证码，请勿将此提供给陌生人。
                        </p>
                        <p>
                            3.正规放贷机构不会单独向用户收费或要求线下还款、对私打款或银行转账等，或冒充公检法等机关要求您提供相关个人信息，如遇到以上情况请拒绝。
                        </p>
                        <p>
                            4.本平台作为移动金融智选平台，本身不从事资金放贷，向您展示的贷款产品由第三方机构提供，您在选择贷款产品前应仔细阅读相关产品信息及注意风险。
                        </p>
                        <p>
                            5.基于协助您申请产品之目的，您授权本平台将您提供的产品申请所需信息（包括但不限于姓名、手机号码、身份证号码及您申请时所填列的相关个人信息等）将提供给为您办理贷款服务的第三方机构，请您注意接听相关来电。
                        </p>
                        <p>6.网络贷款有风险，请仔细分辨和识别，谨防诈骗！</p>
                        <p>
                            7.如您遇到疑似诈骗时，要及时拨打反诈专线 96110
                            进行咨询；如您不慎被骗，请立即拨打110报警，并保存聊天记录、转账记录等相关证据，切勿通过网上去寻找维权机构，避免二次上当受骗！
                        </p>
                    </div>

                    <div v-else class="statement-text scrollable">
                        <AgreementView :data="agreementViewData" :height="'100%'"> </AgreementView>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="statement-footer">
                    <button class="confirm-button" @click="confirmStatement">
                        {{ !showAgreement ? "我已悉知" : "我已阅读并同意" }}
                    </button>
                </div>
            </template>
        </VanDialog>

        <!-- 使用封装的挽留弹窗组件 -->
        <RetainDialog
            ref="retainDialogRef"
            @continue="continueSubmit"
            @cancel="closeRetainDialog"
        />
    </LayoutPage>
</template>

<script lang="ts" setup>
import { Dialog, Toast } from "vant";
import { onBeforeMount, onMounted, ref, computed, onBeforeUnmount, nextTick } from "vue";
import { getCommitResult, getPromote, confirmProductsAmount, resultJump } from "@/apis/index";
import { useRoute, useRouter } from "vue-router";

import { creatUrl } from "@/utils";
import { useGlobalStore } from "@/stores/global";
import moment from "moment";
import AgreementView from "@/gadget/agreementView/index.vue";
import RetainDialog from "@/components/common/RetainDialog.vue";

const router = useRouter();

const store = useGlobalStore();
const retainDialogRef = ref();

// 协议弹窗显示
const showAgreement = ref(false);

// 是否同意协议
const isAgreement = ref(true);

const activeNames = ref<number[]>([0]);

// 定义API返回的产品类型接口
interface ApiProduct {
    id: number;
    product_id: number;
    name: string;
    logo: string;
    price: string;
    partner_api: string;
    partner_url: string;
    detail: { key: string; value: string }[];
    // 其他字段可选
    channel_gid?: number;
    status?: number;
    sort?: number;
    tags?: any[];
}

const route = useRoute();
const VanDialog = Dialog.Component;
const isLoading = ref(true);
const hasError = ref(false);

// 状态管理
const showStatement = ref(true);
const productData = ref<ApiProduct[]>([]);

const status = ref("loading");

// 计算属性
const isSuccess = computed(() => !(status.value == "failure" || route.query.status == "failure"));

// 主推荐产品数据 - 取产品列表的第一个
const primaryProduct = computed<ApiProduct | null>(() => {
    return productData.value.length > 0 ? productData.value[0] : null;
});

// 推荐产品列表 - 除去第一个产品的列表
const recommendProducts = computed<ApiProduct[]>(() => {
    if (isSuccess.value) {
        return productData.value || [];
    } else {
        return productData.value.length > 1 ? productData.value.slice(1) : [];
    }
});

const agreementViewData = ref<any>({});

const jump_status = ref(false);
// 获取开关状态
async function getStatus() {
    let res = await resultJump();
    jump_status.value = res?.data?.jump;
    return jump_status.value;
}
// 生命周期钩子
onMounted(async () => {
    await getStatus();
    // 先获取机构结果列表
    await getProductDetails();

    // 如果没有匹配到成功的产品，则获取推广产品
    if (!productData.value || productData.value.length === 0) {
        await getPromoteProducts();
    }
    const localStorageKey = `authData_${store.uid}`;
    const authData = JSON.parse(localStorage.getItem(localStorageKey) ?? "{}");
    let p = (successProductsList.value ?? [])
        .reduce((pre: string, element: any) => {
            pre += `,${element.name}`;
            return pre;
        }, "")
        .replace(",", "");

    agreementViewData.value = {
        url: window.location.origin + import.meta.env.VITE_RECOMMEND_AGREEMENT,
        query: {
            name: authData.idCard.name,
            idno: "",
            p: p,
            uid: store.uid,
            date: moment(new Date()).format("YYYY年MM月DD日")
        }
    };
});

onBeforeUnmount(() => {
    // 确保页面退出时恢复滚动
    document.body.style.overflow = "";
    document.body.style.position = "";
    document.body.style.width = "";
});

const urlData = ref({
    orderId: route.query.orderId
});

// 申请成功的机构产品列表
const successProductsList = ref<any[]>([]);

// 获取申请机构结果列表
const getProductDetails = async () => {
    isLoading.value = true;

    if (!urlData.value.orderId) {
        isLoading.value = true;
        // 开关开启 匹配机构失败 强跳
        console.log(`直接失败 -->`, jump_status.value);
        if (jump_status.value) {
            console.log(`1 -->`, 1);
            // 强跳企微或h5
            jumpWx();
        }
        return Promise.resolve();
    }

    await getCommitResult({ order: urlData.value.orderId })
        .then((res) => {
            // 判断列表中是否有result等于success的item
            const hasSuccessItem = res.data?.list?.some((item: any) => item.result === "success");
            status.value = hasSuccessItem ? "success" : "failure";

            // 处理推荐列表数据
            if (store.recommendList && store.recommendList.length > 0 && res.data?.list) {
                // 找出成功的产品ID
                const successProducts = res.data.list.filter(
                    (item: any) => item.result === "success"
                );

                console.log(`successProducts -->`, successProducts);
                console.log(`store.recommendList -->`, store.recommendList);

                if (successProducts.length > 0) {
                    // 根据成功产品ID匹配store中的产品数据
                    successProductsList.value = store.recommendList.filter((product: any) =>
                        successProducts.some(
                            (successItem: any) =>
                                successItem.id == product.product_id || successItem.id == product.id
                        )
                    );

                    console.log(`successProductsList -->`, successProductsList.value);
                } else {
                    console.log(`五匹配成功产品 -->`, jump_status.value);

                    // 开关开启 匹配机构失败 强跳
                    if (jump_status.value) {
                        // 强跳企微或h5
                        console.log(`五匹配成功产品 -->`);
                        jumpWx();
                    }
                }
            }
        })
        .catch(() => {
            // 请求出错，设置为失败状态
            status.value = "failure";
            if (jump_status.value) {
                // 强跳企微或h5
                console.log(`五匹配成功产品 -->`);
                jumpWx();
            }
            isLoading.value = false;
        })
        .finally(() => {
            setTimeout(() => {
                // isLoading.value = false;
            }, 300);
        });
};

async function jumpWx() {
    resultJump().then((res) => {
        console.log(`res -->`, res);
        if (res?.data?.is_qiwei) {
            router.push({
                path: "/procedure/ResultWx",
                query: { wxUrl: encodeURIComponent(res?.data?.partner_api) }
            });
        } else {
            router.push({
                path: "/procedure/ResultH5",
                query: {
                    info: encodeURIComponent(
                        JSON.stringify({
                            ...res?.data?.user_info,
                            partner_api: res?.data?.partner_api
                        })
                    )
                }
            });
        }
    });
}

onBeforeMount(() => {
    // 如果URL参数中已有status=failure，则不显示声明
    if (route.query.status === "failure") {
        showStatement.value = false;
    }
});

// 获取推广产品
const getPromoteProducts = async () => {
    isLoading.value = true;
    hasError.value = false;

    await getPromote()
        .then((res) => {
            // isLoading.value = false;

            // 代超列表为空
            if (!res?.data || !Array.isArray(res.data) || res.data.length === 0) {
                if (successProductsList.value && successProductsList.value?.length > 0) {
                    return;
                }

                isLoading.value = true;
                window.location.href = "https://m.yikaionline.com/pages/promotion/index";
                return;
            }

            try {
                productData.value = res.data;
            } catch (error) {
                console.error("处理产品数据时出错:", error);
                hasError.value = true;
                Toast("数据处理出错，请稍后再试");
            }
        })
        .catch((err) => {
            isLoading.value = false;
            hasError.value = true;
            console.error("获取推广产品失败:", err);
            Toast("获取产品失败，请稍后再试");
        });
    setTimeout(() => {
        isLoading.value = false;
    }, 300);
};

// 通用的申请产品函数
const applyProduct = (product: ApiProduct | null = null) => {
    console.log(`product -->`, product);

    store.interceptTips = false;
    closeRetainDialog();
    nextTick(() => {
        store.interceptTips = false;
        closeRetainDialog();
    });
    if (isSuccess.value) {
        let url = "";
        // 主推荐产品的情况
        url = creatUrl(product?.partner_api ?? "https://g.hhhtfin.com/h5/index?_c=fdsfs", {
            _c: store.channel
        });
        // 执行跳转或提示错误
        if (url) {
            window.location.href = url;
        } else {
            Toast("申请链接无效，请稍后再试");
        }
    } else {
        // 进件失败
        resultJump().then((res) => {
            console.log(`res -->`, res);
            // 开关开启
            if (res?.data?.jump) {
                // 没有 partner_api
                if (!res?.data?.partner_api) {
                    // 跳转花枝
                    window.location.href = "https://m.yikaionline.com/pages/promotion/index";
                    return;
                }
                // 判断企微
                if (res?.data?.is_qiwei) {
                    router.push({
                        path: "/procedure/ResultWx",
                        query: { wxUrl: encodeURIComponent(res?.data?.partner_api) }
                    });
                } else {
                    window.location.href = creatUrl(
                        res?.data?.partner_api ?? "https://g.hhhtfin.com/h5/index?_c=fdsfs",
                        {
                            _c: store.channel
                        }
                    );

                    // router.push({
                    //     path: "/procedure/ResultH5",
                    //     query: {
                    //         info: encodeURIComponent(
                    //             JSON.stringify({
                    //                 ...res?.data?.user_info,
                    //                 partner_api: res?.data?.partner_api
                    //             })
                    //         )
                    //     }
                    // });
                }
            } else {
                window.location.href = creatUrl(
                    product?.partner_api ?? "https://g.hhhtfin.com/h5/index?_c=fdsfs",
                    {
                        _c: store.channel
                    }
                );
            }
        });
    }
};

// 确认额度按钮点击
const confirmProducts = async () => {
    if (!isAgreement.value) {
        showAgreement.value = true;
        showStatement.value = true;
        return;
    }
    // 有贷超正常跳贷超，没贷超跳花支
    let res = await confirmProductsAmount();

    if (res?.data?.url) {
        let url = creatUrl(res?.data?.url ?? "https://g.hhhtfin.com/h5/index?_c=fdsfs", {
            _c: store.channel
        });

        window.location.href = url;
    } else {
        // 跳转花枝
        window.location.href = "https://m.yikaionline.com/pages/promotion/index";
    }
};

// 确认借款申请须知
const confirmStatement = () => {
    showStatement.value = false;
    if (showAgreement.value) {
        setTimeout(() => {
            showAgreement.value = true;
        }, 300);
        isAgreement.value = true;
    }
};

// 挽留弹窗相关函数
const closeRetainDialog = () => {
    if (retainDialogRef.value) {
        retainDialogRef.value.hideDialog();
    }
};

// 继续完成申请 - 跳转到产品列表的第一个URL
const continueSubmit = () => {
    // 跳转第一位代超/企微产品/花枝
    // 如果第一位代超的地址不存在 跳转企微
    // 如果不存在代超产品列表 跳转花枝
    if (primaryProduct.value) {
        if (!primaryProduct.value.partner_api) {
            jumpWx();
            return;
        }
        const url = creatUrl(
            primaryProduct.value.partner_api ?? "https://g.hhhtfin.com/h5/index?_c=fdsfs",
            {
                _c: store.channel
            }
        );
        window.location.href = url;
    } else {
        // 如果没有产品数据，跳转到默认促销页
        window.location.href = "https://m.yikaionline.com/pages/promotion/index";
    }
    // 如果有产品数据，跳转到第一个产品的URL
    // if (primaryProduct.value) {
    //     const url = creatUrl(
    //         primaryProduct.value.partner_api ?? "https://g.hhhtfin.com/h5/index?_c=fdsfs",
    //         {
    //             _c: store.channel
    //         }
    //     );
    //     window.location.href = url;
    // } else {
    //     // 如果没有产品数据，跳转到默认促销页
    //     window.location.href = "https://m.yikaionline.com/pages/promotion/index";
    // }
};
</script>

<style lang="less" scoped>
/* CSS变量定义 */
.loan-result-page {
    --primary-color: #3c56e4;
    --primary-light: #4496c6;
    --secondary-color: #1959ff;
    --success-color: #373edb;
    --error-color: #ee5533;
    --tag-color: #ff9645;
    --amount-color: #ff2420;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --background-light: #f9f9f9;
    --card-shadow: 0px 2px 8px 0px rgba(214, 218, 241, 0.8);
}

// 页面主体样式
.loan-result-page {
    min-height: 100vh;
    font-family: PingFangSC, PingFang SC, sans-serif;
    background: url("@/assets/images/result/header_bg.png") no-repeat top center/contain;
    background-color: #f8f8f8;
    padding: 0 15px;

    // 状态卡片
    .status-card {
        display: flex;
        align-items: center;
        margin: 10px 0;
        padding: 16px 20px;
        height: 82px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .status-icon {
            width: 70px;
            height: 70px;
            margin-right: 16px;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .status-content {
            .status-title {
                font-size: 15px;
                font-weight: 500;
                margin-bottom: 4px;
                color: var(--text-primary);
            }

            .status-subtitle {
                font-size: 10px;
                color: var(--tag-color);
            }
        }
    }

    // 推荐区域
    .recommend-section {
        border-radius: 8px;

        .recommend-hot {
            position: relative;
            background: #fff;
            border-radius: 8px;
            padding: 16px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);

            .section-tag {
                position: absolute;
                top: 0;
                right: 0;
                font-size: 12px;
                padding: 0 10px;
                height: 27px;
                line-height: 27px;
                background: linear-gradient(90deg, #dba74d 0%, #f7d79f 100%);
                color: #fff;
                border-radius: 0 8px 0 8px;
            }
        }

        // 主推荐产品卡片
        .primary-product-card {
            padding: 0 10px;
            .product-info {
                display: flex;
                align-items: center;
                margin-bottom: 16px;

                .product-logo {
                    width: 29px;
                    height: 29px;
                    border-radius: 4px;
                    background-color: #f0f0f0;
                    margin-right: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .product-name {
                    font-size: 13px;
                    font-weight: 500;
                    color: var(--text-primary);
                    margin-bottom: 4px;
                    padding-right: 13px;
                    position: relative;
                    &::after {
                        content: "";
                        position: absolute;
                        right: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 1px;
                        height: 10px;
                        background-color: var(--text-light);
                    }
                }

                .product-desc {
                    margin-bottom: 3px;
                    padding-left: 13px;
                    font-size: 12px;
                    color: var(--text-light);
                }
            }

            .product-metrics {
                display: flex;
                justify-content: space-between;
                margin-bottom: 16px;

                .metric {
                    text-align: center;
                    flex: 1;

                    .metric-value {
                        font-size: 14px;
                        font-weight: 500;
                        color: var(--text-primary);
                        margin-bottom: 4px;
                    }

                    .metric-label {
                        font-size: 10px;
                        color: var(--text-light);
                    }
                }
            }

            .apply-button {
                height: 44px;
                line-height: 44px;
                text-align: center;
                background-color: var(--secondary-color);
                color: #fff;
                border-radius: 22px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
            }
        }

        // 通过率提示
        .pass-rate-tips {
            display: flex;
            align-items: center;
            margin: 12px 0;

            span:first-child {
                font-size: 16px;
                font-weight: 500;
                color: var(--text-primary);
                margin-right: 8px;
            }

            .rate-detail {
                color: var(--text-light);
                font-size: 12px;
            }
        }

        // 产品列表
        .product-list {
            text-align: center;
            .product-item {
                background-color: #fff;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 12px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

                .product-basic-info {
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;

                    .product-name-box {
                        display: flex;
                        align-items: baseline;
                        gap: 10px;
                    }

                    .product-logo {
                        width: 20px;
                        height: 20px;
                        border-radius: 4px;
                        margin-right: 10px;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .product-name {
                        font-size: 12px;
                        font-weight: 500;
                        color: var(--text-primary);
                        margin-bottom: 4px;
                    }

                    .product-tag {
                        display: inline-block;
                        font-size: 10px;
                        padding: 1px 4px;
                        color: var(--tag-color);
                        border-radius: 2px;
                        border: 1px solid var(--tag-color);
                        opacity: 0.8;
                    }
                }

                .product-details {
                    display: flex;
                    align-items: center;

                    .product-amount,
                    .product-rate,
                    .product-count {
                        flex: 1;

                        .amount-value,
                        .rate-value,
                        .count-value {
                            font-size: 14px;
                            font-weight: 500;
                            color: var(--text-primary);
                            margin-bottom: 2px;
                        }

                        .amount-label,
                        .rate-label,
                        .count-label {
                            font-size: 10px;
                            color: var(--text-light);
                        }
                    }

                    .apply-now-button {
                        width: 80px;
                        height: 27px;
                        line-height: 27px;
                        text-align: center;
                        background-color: var(--secondary-color);
                        color: #fff;
                        border-radius: 16px;
                        font-size: 12px;
                        cursor: pointer;
                    }
                }
            }
        }
    }

    // 特别提醒区域
    .notice-section {
        margin: 10px;
        padding: 16px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .notice-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 12px;

            &::before {
                content: "";
                display: inline-block;
                width: 4px;
                height: 16px;
                background-color: var(--primary-color);
                border-radius: 2px;
                margin-right: 8px;
                vertical-align: middle;
            }
        }

        .notice-content {
            .notice-item {
                font-size: 12px;
                color: var(--text-secondary);
                line-height: 1.6;
                margin-bottom: 8px;

                .highlight {
                    color: var(--error-color);
                }
            }
        }
    }

    // 弹窗样式
    .statement-content {
        padding: 0 16px;

        .statement-header h4 {
            margin: 16px 0;
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 400;
        }

        .statement-body {
            padding: 16px;
            background: var(--background-light);
            border-radius: 4px;

            .scrollable {
                max-height: 200px;
                overflow-y: auto;
                font-size: 12px;
                color: var(--text-primary);
                line-height: 1.6;

                p {
                    margin-bottom: 8px;
                }

                &::-webkit-scrollbar {
                    width: 4px;
                }

                &::-webkit-scrollbar-thumb {
                    background-color: rgba(0, 0, 0, 0.2);
                    border-radius: 2px;
                }
            }
        }
    }

    .statement-footer {
        display: flex;
        justify-content: center;
        padding: 16px 0;

        .confirm-button {
            width: 80%;
            height: 40px;
            background-color: var(--primary-color);
            color: #fff;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
        }
    }
}

/* 添加空列表状态样式 */
.empty-product-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    background-color: #fff;
    border-radius: 8px;
    margin-top: 15px;

    .empty-icon {
        width: 120px;
        height: 120px;
        margin-bottom: 15px;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    .empty-text {
        font-size: 14px;
        color: var(--text-light);
    }
}

:deep(.success-products-section) {
    position: relative;
    margin-right: -6px;
    margin-top: 15px;
    max-height: 400px;
    overflow-y: scroll;
    border-radius: 10px;
    margin-bottom: 15px;
    padding-right: 6px; /* 为滚动条腾出空间 */

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
        width: 13px; /* 滚动条宽度 */
    }

    &::-webkit-scrollbar-track {
        background: #fcfcfc; /* 轨道背景色 - 固定 */
        border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
        background: #7a7a7a; /* 滑块颜色 - 灰色 */
        border-radius: 10px;
        border: 3px solid #fcfcfc;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: #7a7a7a; /* 悬停时更深的颜色 */
    }

    .van-collapse-item {
        overflow: hidden;
        border-radius: 10px;
        margin-bottom: 10px;

        .van-cell {
            align-items: center;
        }
    }

    .van-collapse-item:last-of-type {
        margin-bottom: 0;
    }

    .product-icon {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        margin-right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    // 产品详情样式
    .product-details {
        background-color: #fff;

        .product-metrics {
            display: flex;
            justify-content: space-between;

            .metric {
                flex: 1;
                text-align: center;

                .metric-value {
                    font-size: 15px;
                    font-weight: bold;
                    color: #2e6bf1;

                    &.highlight {
                        color: #ff2420;
                    }
                }

                .metric-label {
                    font-size: 12px;
                    color: #999;
                }
            }
        }
    }

    .product-tips {
        margin-top: 15px;
        margin-left: 22px;
        &-title {
            font-size: 12px;
            font-weight: 500;
            color: #000;
            position: relative;
            padding-left: 22px;
            margin-bottom: 5px;

            &::before {
                content: "✓";
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 16px;
                height: 16px;
                line-height: 16px;
                text-align: center;
                background-color: #2e6bf1;
                border-radius: 50%;
                color: #fff;
                font-size: 10px;
            }
        }

        &-subtitle {
            font-size: 10px;
            color: #9e9e9e;
        }
    }
}

:deep(.agreementContent) {
    margin-top: 0 !important;
}

.agreement-box {
    margin-bottom: 30px;
    .warning-tips {
        &-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        &-content {
            margin-top: 5px;
            margin-bottom: 15px;
            font-size: 11px;
            color: #5c5c5c;
        }
    }
}
</style>
