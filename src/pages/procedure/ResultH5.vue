<template>
    <LayoutPage class="loan-result-h5-page">
        <!-- 额度卡片 -->
        <div class="amount-card">
            <div class="amount-title">恭喜你，可提额度</div>
            <div class="amount-value">¥ 100,000</div>
        </div>

        <!-- 申请信息卡片 -->
        <div class="info-card">
            <div class="info-title">申请信息</div>
            <div class="info-list">
                <div class="info-item">
                    <div class="item-label">姓名</div>
                    <div class="item-value">{{ info?.name || local_info?.name }}</div>
                </div>
                <div class="info-item">
                    <div class="item-label">电话</div>
                    <div class="item-value">{{ info?.phone || local_info?.phone }}</div>
                </div>
                <div class="info-item">
                    <div class="item-label">城市</div>
                    <div class="item-value">{{ info?.city || local_info?.city }}</div>
                </div>
                <div class="info-item">
                    <div class="item-label">芝麻分</div>
                    <div class="item-value">{{ info?.zhima || local_info?.zhima }}</div>
                </div>
            </div>

            <!-- 立即借款按钮 -->
            <div class="submit-btn-wrapper">
                <van-button round block type="primary" class="submit-btn" @click="handleSubmit">
                    立即借款 ({{ countdown }}s)
                </van-button>
            </div>
        </div>

        <!-- 使用封装的挽留弹窗组件 -->
        <RetainDialog ref="retainDialogRef" @continue="continueSubmit" />
    </LayoutPage>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import LayoutPage from "@/components/layout/page.vue";
import { useGlobalStore } from "@/stores/global";
import { creatUrl } from "@/utils";
import { resultJump } from "@/apis";

const retainDialogRef = ref();
const router = useRouter();
const store = useGlobalStore();

const info = ref<any>({});
const jumpUrl = ref<string>("");

const local_info = computed(() => {
    const localStorageKey = `authData_${store.uid}`;
    let info = JSON.parse(localStorage.getItem(localStorageKey) || "{}");
    return { ...info?.isWellFormed, ...info?.idCard };
});

// resultJump().then((res) => {
//     console.log(`res -->`, res);
//     info.value = res?.data?.user_info;
//     jumpUrl.value = res?.data?.partner_api;
// });

// 倒计时相关
const countdown = ref(4);
let timer: number | null = null;

// 开始倒计时
const startCountdown = () => {
    timer = window.setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--;
        } else {
            clearInterval(timer as number);
            handleSubmit();
        }
    }, 1000);
};

// 立即借款按钮点击事件
const handleSubmit = () => {
    if (timer) {
        clearInterval(timer);
    }

    // 默认跳转地址，实际场景可能需要从API获取
    const url = creatUrl(info.value?.partner_api, {
        _c: store.channel
    });
    window.location.href = url;
};

const continueSubmit = () => {
    handleSubmit();
};

onMounted(() => {
    startCountdown();
    try {
        const route = useRoute();

        if (route.query.info) {
            const decodedInfo = JSON.parse(decodeURIComponent(route.query.info as string));
            console.log(`decodedInfo -->`, decodedInfo, local_info.value);
            info.value = decodedInfo || local_info.value;
            // 如果partner_api也在传递的对象中
            jumpUrl.value = decodedInfo.partner_api || "";
        } else {
            // 如果没有通过URL传递，则调用API获取
            resultJump().then((res) => {
                info.value = res?.data?.user_info;
                jumpUrl.value = res?.data?.partner_api;
            });
        }
    } catch (error) {
        console.log(`error -->`, error);
    }
});

onBeforeUnmount(() => {
    if (timer) {
        clearInterval(timer);
    }
});
</script>

<style lang="less" scoped>
:deep(.page_scroll) {
    width: 100%;
}

.loan-result-h5-page {
    min-height: 100vh;
    font-family: PingFangSC, PingFang SC, sans-serif;
    background: url("@/assets/images/result/header_bg.png") no-repeat top center/contain;
    background-color: #f8f8f8;
    padding: 0 15px;
    display: flex;
    flex-direction: column;
    align-items: center;

    // 卡片通用样式
    .amount-card,
    .info-card {
        width: 100%;
        background-color: #fff;
        border-radius: 12px;
        margin-bottom: 15px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    // 额度卡片样式
    .amount-card {
        margin-top: 20px;
        padding: 20px;
        text-align: center;

        .amount-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
        }

        .amount-value {
            font-size: 32px;
            font-weight: bold;
            color: #1976d2;
        }
    }

    // 申请信息卡片样式
    .info-card {
        padding: 15px;

        .info-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 15px;
        }

        .info-list {
            .info-item {
                display: flex;
                justify-content: space-between;
                padding: 8px 0;

                .item-label {
                    color: #666;
                    font-size: 13px;
                }

                .item-value {
                    color: #333;
                    font-size: 13px;
                    font-weight: 500;
                }
            }
        }
    }

    // 按钮样式
    .submit-btn-wrapper {
        width: 100%;
        margin-top: 50px;
        margin-bottom: 30px;

        .submit-btn {
            height: 44px;
            font-size: 16px;
            font-weight: 500;
            background-color: #2e6bf1;
            border: none;
        }
    }
}
</style>
