<template>
    <LayoutPage class="recommend">
        <template v-if="data?.length > 0">
            <div class="recommendPage">
                <div class="recommendPage-tip">根据您的资信情况，为您匹配以下贷款产品：</div>
                <div class="cardList">
                    <div v-for="item in data" class="item" :key="item">
                        <div class="left" @click.stop="doClickItem(item.product_id)">
                            <span
                                :class="{ cur: selectItems.includes(item.product_id) }"
                                class="circle"
                                v-html="CheckCircleFill"
                            >
                            </span>
                        </div>
                        <div
                            class="right"
                            @click="
                                $nuxtLink('/procedure/product', {
                                    query: {
                                        id: item.product_id,
                                        _s: 1,
                                        order_id: route.query.orderId
                                    }
                                })
                            "
                        >
                            <div class="titleWrap">
                                <img :src="item.logo" alt="" class="logo" />
                                <p class="name">{{ item.name }}</p>
                                <span class="company">{{ item.partner_name }} </span>
                            </div>
                            <div class="tags">
                                <div v-for="tag in item.detail" :key="tag.key" class="tag">
                                    <p class="value">{{ tag.value }}</p>
                                    <p class="label">{{ tag.key }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <AgreementView :data="agreementViewData" :height="agreementViewHeight">
                    <template #header="$scoped">
                        <div class="recommendPage-agreement">
                            <span
                                :class="agreementSatus ? 'cur' : ''"
                                class="circle"
                                @click="agreementSatus = !agreementSatus"
                                v-html="CheckCircleFill"
                            >
                            </span>
                            <span class="agreement-text">
                                我已阅读并同意以下协议，并授权相应机构为我提供咨询服务：
                            </span>
                            <span>
                                <a
                                    :class="`theme_color_procedure`"
                                    :href="$scoped.agreementUrl"
                                    class="agreement-link"
                                    >《个人信息授权及产品风险告知》</a
                                >
                                <a
                                    :class="`theme_color_procedure`"
                                    :href="'https://pro.nahezihz.com/agreement/xyYIkeQLZ6J'"
                                    class="agreement-link"
                                    >《数字证书服务协议》</a
                                >
                            </span>
                        </div>
                    </template>
                </AgreementView>
                <signTips />
            </div>
        </template>
        <template #pageFooter>
            <div class="footer">
                <div class="left" @click="selectAll">
                    <span
                        :class="{ cur: data?.length === selectItems?.length }"
                        class="circle"
                        v-html="CheckCircleFill"
                    ></span>
                    <p>全选</p>
                </div>
                <div>
                    <AppButton
                        class="global-btn"
                        data-collect="listSubmit"
                        type="primary"
                        @click="onJudge"
                        >立即申请
                    </AppButton>
                </div>
            </div>
        </template>
    </LayoutPage>
    <van-popup v-model:visible="visible">
        <div className="backModal">
            <img alt="" src="https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/1649924250899.png" />
            <div className="btns">
                <div className="btn" @click="onClose">坚持退出</div>
                <div className="btn continue" @click="visible = false">继续申请</div>
            </div>
        </div>
    </van-popup>

    <!-- 提示勾选协议弹窗 -->
    <VanDialog v-model:show="agreementTips" show-cancel-button title="温馨提示">
        <div class="agreementTips">请阅读并勾选协议</div>
        <template #footer>
            <div class="notice-btnBox">
                <button class="notice-btn" @click="agreementTips = false">我已悉知</button>
            </div>
        </template>
    </VanDialog>

    <VanDialog v-model:show="noticeVisible" show-cancel-button title="借款申请须知">
        <div class="notice">
            <div v-for="(item, index) in selectItems" :key="index" class="notice-title">
                <div>产品 {{ index + 1 }}：{{ judgeStr(item) }}</div>
            </div>
            <section class="notice-h2">您正在申请借贷请您充分阅读此内容:</section>
            <section class="notice-content">
                <p>
                    1.正规放贷机构不会以解冻资金、交保证金、做银行流水或者资质审核为由先行收取费用！以“验资费”、“保证金”等理由收款的行为都是诈骗，如遇到请勿支付。
                </p>
                <p>2.正规放贷机构不会向您索要银行卡密码和短信验证码，请勿将此提供给陌生人。</p>
                <p>
                    3.正规放贷机构不会单独向用户收费或要求线下还款、对私打款或银行转账等，或冒充公检法等机关要求您提供相关个人信息，如遇到以上情况请拒绝。
                </p>
                <p>
                    4.本平台作为移动金融智选平台，本身不从事资金放贷，向您展示的贷款产品由第三方机构提供，您在选择贷款产品前应仔细阅读相关产品信息及注意风险。
                </p>
                <p>
                    5.基于协助您申请产品之目的，您授权本平台将您提供的产品申请所需信息（包括但不限于姓名、手机号码、身份证号码及您申请时所填列的相关个人信息等）将提供给为您办理贷款服务的第三方机构，请您注意接听相关来电。
                </p>
                <p>6.网络贷款有风险，请仔细分辨和识别，谨防诈骗！</p>
                <p>
                    7.如您遇到疑似诈骗时，要及时拨打反诈专线 96110
                    进行咨询；如您不慎被骗，请立即拨打110报警，并保存聊天记录、转账记录等相关证据，切勿通过网上去寻找维权机构，避免二次上当受骗！
                </p>
            </section>
        </div>
        <template #footer>
            <div class="notice-btnBox">
                <button class="notice-btn" data-collect="productSubmitAgreement" @click="onSubmit">
                    我已悉知
                </button>
            </div>
        </template>
    </VanDialog>
</template>
<script lang="ts" setup>
import { getRecommend } from "@/apis";
import { CheckCircleFill } from "@/assets/svg/index";
import { onMounted, ref, nextTick, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Dialog, Toast } from "vant";
import AppButton from "@/components/App/Button.vue";
import { useGlobalStore } from "@/stores/global";
import moment from "moment";
import { submitH5Commit } from "@/gadget//productList/submit";
import signTips from "@/gadget/signTips.vue";

import AgreementView from "@/gadget/agreementView/index.vue";

const VanDialog = Dialog.Component;
const route = useRoute();
const router = useRouter();
const orderId = ref(route.query.orderId);
const store = useGlobalStore();
const data = ref<any>({});
const visible = ref<boolean>(false);
const selectItems = ref<any>([]);
const localStorageKey = `authData_${store.uid}`;
const authData = JSON.parse(localStorage.getItem(localStorageKey) ?? "{}");
const noticeVisible = ref<boolean>(false);
const agreementSatus = ref<boolean>(false);
const agreementTips = ref<any>(false);

const agreementViewData = ref<any>({
    url: "",
    query: {}
});
const $nuxtLink = (path: string, options: any) => {
    router.push({
        path,
        ...options
    });
};

// 单选框点击事件
const doClickItem = (id: any) => {
    if (selectItems.value.includes(id)) {
        selectItems.value = selectItems.value.filter((kid: any) => kid !== id);
    } else {
        selectItems.value = [...selectItems.value, id];
    }
};

const selectAll = () => {
    if (data.value.length === selectItems.value.length) {
        selectItems.value = [];
    } else {
        selectItems.value = data.value.map((item: any) => item.product_id);
        agreementSatus.value = true;
    }
};
const onClose = () => {
    visible.value = false;
    window.history.back();
};

const agreementViewHeight = ref<string>("calc(100vh - 350px)");

// 在onMounted中添加窗口大小变化的监听，动态调整高度
onMounted(() => {
    // 设置初始高度
    setAgreementHeight();

    // 监听窗口大小变化
    window.addEventListener("resize", setAgreementHeight);

    getRecommend(route.query.orderId)
        .then((res) => {
            data.value = res.data;
            return res.data;
        })
        .then((res) => {
            let p = (res ?? [])
                .reduce((pre: string, element: any) => {
                    pre += `,${element.name}`;
                    return pre;
                }, "")
                .replace(",", "");
            agreementViewData.value = {
                url: window.location.origin + import.meta.env.VITE_RECOMMEND_AGREEMENT,
                query: {
                    name: authData.idCard.name,
                    idno: "",
                    p: p,
                    uid: store.uid,
                    date: moment(new Date()).format("YYYY年MM月DD日")
                }
            };
        });
    if (orderId.value !== route.query.orderId) {
        orderId.value = route.query.orderId;
    }
});

// 组件卸载时移除事件监听
onUnmounted(() => {
    window.removeEventListener("resize", setAgreementHeight);
});

// 设置协议区域高度
const setAgreementHeight = () => {
    // 延迟执行确保DOM已完成渲染
    nextTick(() => {
        // 估算其他元素的总高度：头部提示 + 产品列表 + 底部按钮 + 一些边距
        const otherElementsHeight = 350; // 根据实际情况调整这个值
        agreementViewHeight.value = `calc(100vh - ${otherElementsHeight}px)`;
    });
};

// 提交确认
const onSubmit = async () => {
    if (selectItems.value.length) {
        const { idCard } = authData;
        submitH5Commit({
            orderId: route.query.orderId,
            list: data.value
                .filter((item: any) => selectItems.value.includes(item.product_id))
                .map((item: any) => item.product_id),
            name: idCard.name,
            idno: idCard.idno
        });
    }
};

const onJudge = () => {
    if (!agreementSatus.value) {
        Toast("请阅读并勾选协议");
    } else if (!selectItems.value.length) {
        Toast("请选择产品");
    } else {
        onSubmit();
    }
};

const judgeStr = (item: any) => {
    let str1 = data.value.reduce((pre: any, element: any) => {
        if (element.product_id == item) {
            pre += element.name;
        }
        return pre;
    }, "");
    let str2 = data.value.reduce((pre: any, element: any) => {
        if (element.product_id == item) {
            pre += element.partner_name;
        }
        return pre;
    }, "");
    return str1 + "(" + str2 + ")";
};
</script>
<style lang="less" scoped>
.recommend {
    .recommendPage {
        min-height: 100vh;
        // background: linear-gradient(180deg, #ff7b5a 0%, rgba(255, 150, 49, 0) 50%);
        // background: linear-gradient(180deg, #3c56e4 0%, #6f85ff 12%, #ffffff 50%);
        // background: linear-gradient(180deg, #3c56e4 0%, #ffffff 30%);
        background: linear-gradient(180deg, #3c56e4 0%, #ffffff 42%, #ffffff 100%);
        padding: 0 16px 60px;

        .recommendPage-tip {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 33px;
            height: 33px;
        }

        .cardList {
            .item {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                position: relative;
                height: 112px;
                background: #ffffff;
                // box-shadow: 0px 2px 6px 0px rgba(255, 101, 49, 0.2);
                box-shadow: 0px 2px 6px 0px #cad2ff;
                border-radius: 8px;
                padding: 13px 42px 16px 0;
                position: relative;

                .left {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0 18px 0 12px;

                    .circle {
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                        border: 1px solid var(--color-primary-bg);

                        :deep(svg) {
                            display: none;
                        }

                        &.cur {
                            border: none;

                            :deep(svg) {
                                width: 100%;
                                height: 100%;
                                display: block;
                                color: var(--color-primary-bg);
                            }
                        }
                    }
                }

                .right {
                    display: flex;
                    flex-grow: 1;
                    flex-direction: column;

                    .titleWrap {
                        display: flex;
                        align-items: center;
                        font-size: 16px;
                        font-weight: 500;
                        color: #1a1a1a;
                        line-height: 22px;

                        .logo {
                            width: 18px;
                            height: 18px;
                            margin-right: 3px;
                        }

                        .name {
                            font-size: 16px;
                            font-family: PingFangSC-Semibold, PingFang SC;
                            font-weight: 600;
                            color: #333333;
                            line-height: 22px;
                            margin-right: 10px;
                            white-space: nowrap;
                        }

                        .company {
                            font-size: 12px;
                            font-family: PingFangSC-Medium, PingFang SC;
                            font-weight: 500;
                            color: #888888;
                            line-height: 17px;
                        }
                    }

                    .tags {
                        display: flex;
                        margin-top: 21px;
                        align-items: center;
                        justify-content: space-between;

                        .tag {
                            display: flex;
                            flex-direction: column;

                            .value {
                                font-size: 18px;
                                font-family: DINAlternate-Bold, DINAlternate;
                                font-weight: bold;
                                color: var(--color-primary-bg);
                                line-height: 22px;
                            }

                            .label {
                                font-size: 12px;
                                font-family: PingFangSC-Regular, PingFang SC;
                                font-weight: 400;
                                color: #888888;
                                line-height: 18px;
                            }
                        }
                    }

                    .agreement {
                        margin-top: 12px;
                        text-align: right;
                        border-top: 1px solid #eeeeee;
                        padding-top: 10px;
                        color: #666;
                        display: flex;
                        align-items: center;
                        justify-content: right;
                        font-size: 13px;

                        .default {
                            color: #999;
                            margin-right: 10px;
                        }
                    }
                }

                .arrow {
                    position: absolute;
                    top: 50%;
                    right: 8px;
                    transform: translateY(-50%);

                    svg {
                        font-size: 20px;
                        width: 1em;
                        height: 1em;

                        path {
                            fill: #eb6b3b;
                        }
                    }
                }
            }
        }

        .recommendPage-agreement {
            .circle {
                width: 12px;
                height: 12px;
                position: relative;
                top: 1px;
                border-radius: 50%;
                display: inline-block;
                border: 1px solid #999;
                margin-right: 6px;

                :deep(svg) {
                    display: none;
                }

                &.cur {
                    border: none;

                    :deep(svg) {
                        width: 12px;
                        height: 12px;
                        display: block;
                        color: var(--color-primary-bg);
                    }
                }
            }

            .agreement-text {
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #555555ff;
                line-height: 17px;
            }

            .agreement-link {
                .agreement-text();
                color: var(--color-primary-bg);
            }
        }
    }

    .footer {
        background: #fff;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 0 16px;
        line-height: 70px;
        min-height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);
        box-shadow: 0px 2px 6px 0px #d9dbe3;

        .left {
            display: flex;
            align-items: center;
            justify-content: center;

            .circle {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                border: 1px solid #e6e6e6;

                :deep(svg) {
                    display: none;
                }

                &.cur {
                    border: none;

                    :deep(svg) {
                        width: 100%;
                        height: 100%;
                        display: block;
                        color: var(--color-primary-bg);
                    }
                }
            }

            p {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 20px;
                margin-left: 4px;
            }
        }

        .global-btn {
            height: 44px;
            background: var(--color-primary-bg);
            border-radius: 22px;
            width: 174px;
        }
    }
}

.backModal {
    padding-bottom: 30px;

    img {
        width: 335px;
    }

    .btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 335px;
        padding: 0 34px 37px 34px;
        margin-top: -85px;

        .btn {
            width: 124px;
            height: 48px;
            line-height: 48px;
            text-align: center;
            background: #fefefe;
            border-radius: 23px;
            border: 1px solid #a4a4a4;

            &.continue {
                width: 124px;
                height: 48px;
                background: linear-gradient(212deg, #ee5533 0%, #ee7233 100%);
                box-shadow: 0px 1px 5px 0px rgba(238, 93, 51, 0.5);
                border: 0px solid #a4a4a4;
                border-radius: 24px;
                color: #fff;
            }
        }
    }
}

.notice {
    padding: 0 20px;

    p {
        padding: 5px 5px;
    }

    .notice-title {
        margin-top: 5px;
        padding: 0 5px;
        margin-bottom: 5px;
        font-size: 14px;
    }

    .notice-h2 {
        font-size: 13px;
        color: #e44c2a;
        padding: 0 5px;
        margin-bottom: 8px;
    }

    .notice-content {
        height: 200px;
        overflow: auto;
        font-size: 12px;
        text-align: inherit;

        &::-webkit-scrollbar-thumb {
            width: 1px;
            background-color: #99999980;
        }

        &::-webkit-scrollbar {
            width: 1px;
        }
    }
}

.notice-btnBox {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;

    .notice-btn {
        border: none;
        width: 80%;
        margin: 0 auto;
        height: 40px;
        border-radius: 20px;
        color: #fff;
        background-color: #e44c2a;
    }
}

.agreementTips {
    // text-align: center;
    margin-left: 20px;
    font-size: 12px;
}
</style>
