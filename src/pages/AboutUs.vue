<template>
    <LayoutPage class="aboutPage">
        <div class="header">
            <img
                alt=""
                class="logo"
                src="https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/1650441927382.png"
            />
            <p class="desc">专注为白领客户打造的金融综合服务平台</p>
        </div>
        <div class="list">
            <a v-for="item in data?.agreement ?? []" :key="item.key" :href="item.value">
                <div class="item" data-collect="aboutAgreement">
                    <p class="label">{{ item.key }}</p>
                    <svg
                        class="antd-mobile-icon"
                        height="1em"
                        style="vertical-align: -0.125em"
                        viewBox="0 0 48 48"
                        width="1em"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                    >
                        <g
                            id="RightOutline-RightOutline"
                            fill="none"
                            fill-rule="evenodd"
                            stroke="none"
                            stroke-width="1"
                        >
                            <g id="RightOutline-RightOutlined">
                                <rect
                                    id="RightOutline-矩形"
                                    fill="#FFFFFF"
                                    height="48"
                                    opacity="0"
                                    width="48"
                                    x="0"
                                    y="0"
                                ></rect>
                                <path
                                    id="RightOutline-right"
                                    d="M17.3947957,5.11219264 L35.5767382,22.6612572 L35.5767382,22.6612572 C36.1304785,23.2125856 36.1630514,24.0863155 35.6744571,24.6755735 L35.5767382,24.7825775 L17.3956061,42.8834676 C17.320643,42.9580998 17.2191697,43 17.1133896,43 L13.9866673,43 C13.7657534,43 13.5866673,42.8209139 13.5866673,42.6 C13.5866673,42.4936115 13.6290496,42.391606 13.7044413,42.316542 L32.3201933,23.7816937 L32.3201933,23.7816937 L13.7237117,5.6866816 C13.5653818,5.53262122 13.5619207,5.27937888 13.7159811,5.121049 C13.7912854,5.04365775 13.8946805,5 14.0026627,5 L17.1170064,5 C17.2206403,5 17.3202292,5.04022164 17.3947957,5.11219264 Z"
                                    fill="currentColor"
                                    fill-rule="nonzero"
                                ></path>
                            </g>
                        </g>
                    </svg>
                </div>
            </a>
        </div>
    </LayoutPage>
</template>
<script lang="ts" setup>
import { onBeforeMount, ref } from "vue";
import { getHomeDetail } from "@/apis";
import { useGlobalStore } from "@/stores/global";

const store = useGlobalStore();
const data = ref<any>({});

onBeforeMount(async () => {
    const result = await getHomeDetail(store.channel);
    data.value = result.data;
});
</script>
<style lang="less">
.aboutPage {
    background: #f9f9f9;

    .header {
        background: #fff;
        min-height: 215px;
        margin-bottom: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .logo {
            width: 68px;
            height: 68px;
        }

        .desc {
            margin-top: 20px;
            height: 22px;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #444444;
            line-height: 22px;
            text-align: center;
        }
    }

    .list {
        .item {
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #fff;
            font-size: 16px;
            font-weight: 400;
            color: #444444;
            line-height: 54px;
            margin-bottom: 10px;
        }
    }
}
</style>
