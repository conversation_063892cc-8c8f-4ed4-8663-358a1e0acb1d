<template>
    <LayoutPage class="recordPage">
        <div class="list">
            <template v-if="data?.data?.length > 0">
                <div class="notice">办理贷款，平台不会向用户收取任何费用。谨防电话、微信诈骗</div>

                <div v-for="item in data.data" :key="item.id" class="listItem">
                    <div class="left">
                        <img :src="item.product_logo" class="icon" />
                        <div class="info">
                            <p class="name">{{ item.product_name }}</p>
                            <p class="status">
                                {{ item.state === "success" ? "提交成功" : "提交失败" }}
                            </p>
                            <p class="time">{{ item.state_time }}</p>
                        </div>
                    </div>
                    <a
                        v-if="item.product_phone"
                        :href="`tel:${item.product_phone}`"
                        class="right"
                        v-html="PhoneFill"
                    >
                    </a>
                </div>
                <div class="more">没有更多了</div>
            </template>
            <template v-else>
                <div class="data-none">
                    <img
                        alt="error block image"
                        src="https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/1650440770748.png"
                    />
                    <p>您还没有进行过贷款哦</p>
                </div>
            </template>
        </div>
    </LayoutPage>
</template>
<script lang="ts" setup>
import { PhoneFill } from "@/assets/svg/index";
import { getRecord } from "@/apis";
import { onMounted, ref } from "vue";

const data = ref<any>({});
onMounted(() => {
    getRecord({ page: 1, size: 1000 }).then((res: any) => {
        data.value = res.data;
    });
});
</script>
<style lang="less" scoped>
.recordPage {
    background-color: #fff;

    .notice {
        height: 36px;
        font-size: 12px;
        color: #fa8c16;
        background-color: #fff7e6;

        line-height: 36px;
        padding: 0 12px;
        display: -webkit-flex;
        display: flex;
        -webkit-align-items: center;
        align-items: center;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        border: 1px solid #fff3e9;
    }

    .adm-notice-bar .adm-notice-bar-content {
        text-align: center;
        justify-content: center;
    }

    .list {
        .listItem {
            padding: 18px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #eeeeee;

            .left {
                display: flex;

                .icon {
                    width: 22px;
                    height: 22px;
                    border-radius: 50%;
                    overflow: hidden;
                    background: #eeeeee;
                    margin-right: 6px;
                }

                .info {
                    .name {
                        font-size: 16px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #222222;
                        line-height: 22px;
                    }

                    .status {
                        margin-top: 6px;
                        font-size: 15px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #999999;
                        line-height: 22px;
                    }

                    .time {
                        margin-top: 6px;
                        font-size: 13px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #999999;
                        line-height: 18px;
                    }
                }
            }

            .right {
                width: 28px;
                height: 28px;
                border-radius: 50%;
                border: 1px solid rgba(238, 85, 51, 0.4);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                color: rgb(238, 85, 51);
            }
        }
    }
}

.recordPopup {
    padding: 20px 16px;

    .title {
        margin-bottom: 20px;
        text-align: center;
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: bold;
        color: #222222;
        line-height: 24px;

        span {
            color: #ee5533;
        }
    }

    .buttonWrap {
        display: flex;
        align-items: center;
        justify-content: center;

        .right {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 6px;
        }
    }
}

.more {
    color: #999;
    font-size: 10px;
    text-align: center;
    padding: 12px 0;
}

.data-none {
    text-align: center;
    padding-top: 20vh;

    img {
        width: 140px;
        margin: 0 auto;
    }

    p {
        margin-top: 14px;
        font-size: 14px;
        line-height: 1.4;
        color: #999;
    }
}
</style>
