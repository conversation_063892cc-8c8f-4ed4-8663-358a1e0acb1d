import { defineStore } from "pinia";
import { getUrlData, sessionStorage } from "@/utils/index";
import { ref } from "vue";

export const useGlobalStore = defineStore("global", {
    state: () => {
        const { _c, yxuid, yxtoken, _u } = getUrlData();

        const pageConfig = {};

        // 修复JSON解析错误
        let recommendList = [];
        try {
            const storedList = localStorage.getItem("recommendList");
            if (storedList) {
                recommendList = JSON.parse(storedList);
            }
        } catch (err) {
            console.error("解析recommendList错误:", err);
        }

        let channel = sessionStorage.getItem("yxchannel");
        let token = sessionStorage.getItem("token");

        let uid = sessionStorage.getItem("uid");

        if (_c) {
            sessionStorage.setItem("yxchannel", _c);
            channel = _c;
        }
        if (yxtoken) {
            sessionStorage.setItem("token", yxtoken);
            token = yxtoken;
        }
        if (yxuid) {
            sessionStorage.setItem("uid", yxuid);
            uid = yxuid;
        }
        if (!token) {
            console.log("未登录");
        }

        return {
            token,
            channel,
            uid,
            _u,
            isFlow: false,
            orderId: "",
            interceptState: true,
            interceptTips: false,
            preRouter: "",
            currentRouter: "",
            recommendList,
            pageConfig
        };
    },
    persist: {
        paths: ["preRouter", "currentRouter"]
    },
    actions: {
        initialInfo(payload: any) {
            if (payload.token) sessionStorage.setItem("token", payload.token);
            if (payload.uid) sessionStorage.setItem("uid", payload.uid);
            Object.assign(this, payload);
        },
        delToken() {
            localStorage.clear();
            sessionStorage.setItem("token", "");
            sessionStorage.setItem("uid", "");
            Object.assign(this, {
                token: "",
                uid: ""
            });
        },
        async setPageConfig(payload: any) {
            console.log(`payload -->`, payload);

            this.pageConfig = payload;
        }
    }
});
