<template>
    <VanDialog v-model:show="store.interceptTips" :close-on-popstate="false">
        <div class="interceptContent">您确定不继续申请贷款吗？</div>
        <template #footer>
            <div class="interceptBtn">
                <AppButton type="primary" @click="onBack"> 确认 </AppButton>
                <AppButton type="primary" @click="store.interceptTips = false"> 再看看 </AppButton>
            </div>
        </template>
    </VanDialog>
</template>
<script setup lang="ts">
import AppButton from "@/components/App/Button.vue";
import { Dialog } from "vant";
import { useGlobalStore } from "@/stores/global";
import { useRouter } from "vue-router";

const router = useRouter();
const VanDialog = Dialog.Component;
const store = useGlobalStore();
/**
 * 确认返回
 */
const onBack = () => {
    store.interceptTips = false;
    store.interceptState = false;
    router.back();
};
</script>
<style scoped lang="less">
.interceptContent {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 106px;
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 25px;
}
.interceptBtn {
    display: flex;
    border-top: 1px solid #eeeeee;

    > div {
        flex: 1;
        height: 54px;
        background-color: #fff;
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #999999;
        line-height: 25px;
        &:nth-child(2) {
            background-color: #3c56e4ff;
            color: #fff;
        }
    }
}
</style>
