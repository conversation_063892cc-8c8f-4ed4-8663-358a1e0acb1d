<template>
    <!-- 挽留页弹窗 -->
    <div class="retain-dialog-container" v-if="visible">
        <div class="retain-dialog-mask"></div>
        <div class="retain-dialog-content">
            <img src="@/assets/images/retain_bg.png" alt="挽留页" class="retain-img" />
            <div class="retain-btn-box">
                <div class="retain-btn" @click="continueHandler">{{ continueText }}</div>
                <div class="retain-btn-text" @click="closeDialog">{{ cancelText }}</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onBeforeUnmount } from "vue";

const props = defineProps({
    // 默认图片地址
    imageSrc: {
        type: String,
        default: "/src/assets/images/retain_bg.png"
    },
    // 继续按钮文本
    continueText: {
        type: String,
        default: "继续完成申请"
    },
    // 取消按钮文本
    cancelText: {
        type: String,
        default: "我再想想"
    },
    // 是否启用返回拦截
    enabled: {
        type: Boolean,
        default: true
    }
});

const emits = defineEmits(["continue", "cancel"]);

// 控制弹窗显示
const visible = ref(false);

// 防抖计时器
let debounceTimer: number | null = null;

// 监听弹窗状态变化，控制页面滚动
watch(visible, (newValue) => {
    if (newValue) {
        // 弹窗显示，禁止页面滚动
        document.body.style.overflow = "hidden";
        document.body.style.position = "fixed";
        document.body.style.width = "100%";
    } else {
        // 弹窗关闭，恢复页面滚动
        document.body.style.overflow = "";
        document.body.style.position = "";
        document.body.style.width = "";
    }
});

// 关闭挽留弹窗
const closeDialog = () => {
    visible.value = false;
    emits("cancel");
    // 重新设置历史状态
    pushHistoryStates();
};

// 继续完成申请
const continueHandler = () => {
    visible.value = false;
    emits("continue");
};

// 推入多个历史状态
const pushHistoryStates = () => {
    // 多次推入状态以确保拦截成功
    for (let i = 0; i < 5; i++) {
        window.history.pushState(
            { noBackExitPage: true, timestamp: Date.now() + i },
            "",
            window.location.href
        );
    }
};

// 处理浏览器返回事件
const handlePopState = (e: PopStateEvent) => {
    // 不拦截正常页面跳转，只拦截返回操作
    if (!props.enabled) return;

    // 防止事件冒泡
    e.stopPropagation();

    // 防抖，避免多次触发
    if (debounceTimer) clearTimeout(debounceTimer);

    debounceTimer = window.setTimeout(() => {
        // 显示挽留弹窗
        visible.value = true;
        // 再次推入历史状态
        pushHistoryStates();
    }, 50);
};

// 初始化拦截器
const initInterceptor = () => {
    if (!props.enabled) return;

    // 移除现有监听器避免重复
    window.removeEventListener("popstate", handlePopState);

    // 添加popstate事件监听
    window.addEventListener("popstate", handlePopState);

    // 初始化历史状态
    pushHistoryStates();

    // 确保在iOS Safari中也能正常工作
    setTimeout(() => {
        pushHistoryStates();
    }, 100);
};

// 公开方法: 手动显示弹窗
const showDialog = () => {
    visible.value = true;
};

// 公开方法: 手动隐藏弹窗
const hideDialog = () => {
    visible.value = false;
};

// 对外暴露的方法
defineExpose({
    showDialog,
    hideDialog
});

onMounted(() => {
    // 延迟初始化挽留弹窗拦截
    setTimeout(() => {
        initInterceptor();
    }, 300);
});

onBeforeUnmount(() => {
    // 清理资源
    window.removeEventListener("popstate", handlePopState);
    if (debounceTimer) clearTimeout(debounceTimer);

    // 确保页面退出时恢复滚动
    document.body.style.overflow = "";
    document.body.style.position = "";
    document.body.style.width = "";
});
</script>

<style lang="less" scoped>
/* 挽留弹窗样式 */
.retain-dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    touch-action: none;
}

.retain-dialog-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.retain-dialog-content {
    position: relative;
    width: 70%;
    max-width: 464px;
    z-index: 2001;
    overflow: hidden;
}

.retain-img {
    width: 100%;
    display: block;
}

.retain-btn-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 97%;
    max-width: 464px;
    padding: 20px 0;
    height: 125px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(90deg, rgba(255, 242, 228, 1) 0%, rgba(250, 218, 185, 1) 100%);
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

.retain-btn {
    position: relative;
    color: #ffffff;
    font-size: 14px;
    line-height: 40px;
    text-align: center;
    margin: 10px 0;
    font-weight: 500;
    width: 70%;
    height: 80px;
    border-radius: 47px;
    background: linear-gradient(90deg, rgba(233, 86, 59, 1) 0%, rgba(231, 52, 52, 1) 100%);
    &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: calc(100% + 17px);
        height: calc(100% + 17px);
        background: linear-gradient(90deg, rgba(233, 86, 59, 1) 0%, rgba(231, 52, 52, 1) 100%);
        border-radius: 134.01px;
        opacity: 0.1;
        z-index: -1;
    }
}

.retain-btn-text {
    color: #858585;
    font-size: 12px;
    line-height: 25px;
    text-align: center;
    margin: 10px 0;
    font-weight: 500;
    width: 80%;
}
</style>
