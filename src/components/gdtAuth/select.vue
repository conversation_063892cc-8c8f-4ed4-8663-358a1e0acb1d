<template>
    <div class="form-select">
        <div v-for="item in props.options" :style="selectStyle" class="select_row">
            <div
                :class="{ active: checkItemActive(item) }"
                class="select_item"
                @click="onSave(item)"
            >
                {{ item.value }}{{ item.isEdit ? "" : props.conf.unit }}
            </div>
        </div>
    </div>

    <div v-if="editData" class="form-select-edit">
        <div class="form-select-edit_content">
            <div class="form-select-edit_wrap">
                <div class="form-select-edit_title">{{ editData.label }}</div>
                <div class="form-select-edit_number">
                    <input
                        v-model="editValue"
                        :placeholder="`${props.conf.placeholder}`"
                        type="number"
                    />
                </div>

                <div class="form-select-edit_unit">{{ editData.unit }}</div>
            </div>
            <AppButton class="global-btn" @click="editSave">确定</AppButton>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import AppButton from "@/components/App/Button.vue";
import { useTheme } from "@/pages/home/<USER>/themeHook/useTheme";

export interface PropsType {
    title: string;
    options: any[];
    conf?: any;
    form: any;
    modelValue: any;
    beforeSave?: any;
}

const props = withDefaults(defineProps<PropsType>(), {
    title: "",
    conf: {
        unit: "",
        placeholder: ""
    }
});
const theme = useTheme();
const emit = defineEmits<{
    (e: "update:modelValue", value: any): void;
    (e: "change", value: any): void;
}>();
const editValue = ref<any>();
const editData = ref<any>();

const checkInEdit = () => {
    return (
        props.modelValue !== undefined &&
        props.options.filter((el: any) => el.value === props.modelValue).length === 0
    );
};

const checkItemActive = ({ value, isEdit }: any) => {
    //更多编辑
    if (editData.value) {
        return !!isEdit;
    }
    if (isEdit && checkInEdit()) {
        return true;
    } else return props.modelValue === value;
};

const onSave = ({ isEdit, ...item }) => {
    const { editConfig } = props.form;
    if (isEdit && editConfig) {
        editData.value = editConfig;
        editValue.value = props.modelValue;
        return;
    }
    editData.value = undefined;
    editValue.value = undefined;
    saveData(item.value);
};

const editSave = async () => {
    let f: boolean = true;
    if (props.beforeSave) {
        f = await props.beforeSave(editData.value, editValue.value);
    }
    if (f) saveData(editValue.value);
};

const saveData = (value: any) => {
    emit("update:modelValue", value);
    emit("change", value);
};

const selectStyle = computed(() => {
    const { col = 2 } = props.form;
    const w = `${Math.floor(100 / col)}%`;
    return {
        width: w,
        maxWidth: w,
        minWidth: w
    };
});

onMounted(() => {
    if (checkInEdit()) {
        const [item] = props.options.filter((el: any) => el.isEdit);
        const { editConfig } = props.form;
        if (item) {
            editData.value = editConfig;
            editValue.value = props.modelValue;
        }
    }
});
</script>
<style lang="less" scoped>
.form_title {
    height: 54px;
    font-size: 16px;
    text-align: center;
    font-weight: 600;
    color: #333333;
    line-height: 22px;
    padding-top: 16px;
    padding-bottom: 16px;
}

.form-select {
    display: flex;
    flex-direction: row;
    padding: 0 12px;
    flex-wrap: wrap;

    .select_row {
        width: 50%;
        max-width: 50%;
        min-width: 50%;
    }

    .select_item {
        height: 44px;
        background: #f5f5f5;
        border-radius: 22px;
        font-size: 12px;
        font-weight: 400;
        color: #666666;
        text-align: center;
        line-height: 44px;
        margin: 0 8px;
        margin-bottom: 16px;

        &.active {
            background-color: v-bind("theme.authBtnBgc") !important;
            color: #fff;
        }
    }
}

.form-select-edit {
    padding-bottom: 16px;
    margin: 0 20px;
}

.form-select-edit_content {
    background: #f5f5f5;
    border-radius: 8px;
    padding: 16px 20px;

    .form-select-edit_wrap {
        margin-bottom: 16px;
        border-bottom: 1px solid #e4e4e4;
        height: 36px;
        line-height: 36px;
        margin-top: 4px;
        display: flex;
        flex-direction: row;
    }

    .form-select-edit_title {
        font-size: 16px;
        font-weight: 400;
        color: #000000;
    }

    .form-select-edit_number {
        flex: 1;
        font-size: 16px;
        padding-left: 28px;

        input {
            height: 100%;
            box-sizing: border-box;
            border: none;
            background-color: transparent;
            border-radius: 0;
            outline: none;
            width: 100%;

            &:focus,
            &:active {
                border: none;
                outline: none;
            }
        }
    }

    .form-select-edit_unit {
        font-size: 16px;
        font-weight: 400;
        color: #333333;
    }
}

.global-btn {
    background: v-bind("theme.authBtnBgc") !important;
}
</style>
