<template>
    <Picker
        ref="myPicker"
        :columns="props.options"
        :default-index="2"
        :swipe-duration="200"
        :threeDimensional="false"
        :title="props.title"
        :visible-item-count="3"
        cancel-button-text=" "
        class="myPicker"
        confirm-button-text=" "
    >
        <template #title>
            <div class="myPicker-box">
                <div class="myPicker-title">
                    {{ props.title }}
                </div>
                <div class="myPicker-describe">请上下滑动选择</div>
            </div>
        </template>
        <!-- <template #confirm>
            <div class="myPicker-img" v-show="!props.modelValue"></div>
            <img
                @click="onPickerClose"
                v-show="props.modelValue"
                class="myPicker-img"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAXlJREFUWEftmDFOQzEMhr8uCE7BAp3ZOAELgnbgnB1AwAAnYGMGFm6BuhQZtdLjKYkdO0/K8Do2sf3lt+PUXdD5Z9E5HzNgNEOzglMreAScAl/ALhpsZC/ZOwO+gW3OdynFAvcGXADPwB3w0wjyGNgA18A7cJmDLAGeAx8DoFdg1QBS4B6Aq4HvJfCZOnwJUNYe96c82EYhU3BPwG2uhLRbfALcj07rhUzBvQDrUlY0QFGuBaQLToJbAKOQbrgaQNmbCqSlOwRXC1gLGYbzAFohm8B5ATVIWR/3OfW2el4S7dHI1aTYDZuwGy6i4AE+BTk8WAiuBWAu3fK9dsO1DP2tW/tgyVlOxS4AtRSHISMKWi9JCNILWOpzqTbjhvQAWpqw51lM1nktoAWu1IKqlawB9KjisfmnpBUwEihia+qDoQB7Odw+NAXdjhMV7/LVw9AkI+2NZ2jqfuzsfnCXMur6rw/Tz6GpN2m3eOr4qv8ZUJVI2TArGFXwF+gDkCnu+gCRAAAAAElFTkSuQmCC"
                alt=""
            />
        </template> -->
        <template #columns-bottom>
            <div class="myPicker-AppButton">
                <AppButton class="global-btn" type="primary" @click="onConfirm">确定</AppButton>
            </div>
        </template>
    </Picker>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import AppButton from "@/components/App/Button.vue";
import { Picker } from "vant";
import "vant/es/picker/style";
import { useTheme } from "@/pages/home/<USER>/themeHook/useTheme";

export interface PropsType {
    title: string;
    options: any[];
    conf?: any;
    form: any;
    modelValue: any;
    beforeSave?: any;
}

const theme = useTheme();
const props = withDefaults(defineProps<PropsType>(), {
    title: ""
});
const emit = defineEmits<{
    (e: "update:modelValue", value: any): void;
    (e: "change", value: any): void;
    (e: "close"): void;
}>();
const myPicker = ref<any>(null);

const onPickerClose = () => {
    emit("close");
};

const onConfirm = () => {
    // 根据索引获取选中的地址,修改currentForm的value
    let addressIndex = myPicker.value.getIndexes();
    let options = props.options;
    // console.log('currentForm.value',currentForm.value.options);
    let e;
    if (addressIndex.length > 1) {
        e = [];
        e.push(options[addressIndex[0]]);
        e.push(options[addressIndex[0]].children[addressIndex[1]]);
        options[addressIndex[0]].children[addressIndex[1]];
    } else {
        e = {};
        e = options[addressIndex[0]];
    }

    if (e instanceof Array) {
        // 传入当前的值
        saveData(e.map((item: any) => item.value).join("/"));
    } else {
        saveData(e.value);
    }
};

const saveData = (value: any) => {
    emit("update:modelValue", value);
    emit("change", value);
};

onMounted(() => {
    let currentData = (props.modelValue ?? props.form?.defaultValue ?? "").split("/");
    myPicker.value.setValues(currentData);
});
</script>
<style lang="less" scoped>
.myPicker {
    height: 278px;

    .van-picker-column__item--selected {
        color: #ee5533;
    }

    .van-picker__cancel {
        width: 58px;
    }

    .van-picker__toolbar {
        height: 60px;
    }

    .myPicker-img {
        width: 20px;
        height: 20px;
    }

    .myPicker-title {
        text-align: center;
        font-size: 16px;
        margin-top: 10px;
    }

    .myPicker-describe {
        text-align: center;
        font-size: 12px;
        color: #00000035;
    }

    .myPicker-AppButton {
        padding: 16px 20px;

        .global-btn {
            background-color: v-bind("theme.authBtnBgc") !important;
            width: 160px;
            height: 46px;
            margin: 0 auto;
        }
    }
}
</style>
