<template>
    <div class="form-zhima">
        <div class="zhimaWrap">
            <div class="titleWrap">
                <p class="title">您的芝麻分是？</p>
                <div class="zhimaBtn" data-collect="zhimaTips" @click="zhimatipsVisible = true">
                    <img alt="" src="https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/zmicon.png" />
                    <p>查询</p>
                    <p class="blue">我的芝麻分</p>
                </div>
            </div>
            <div class="inputWrap">
                <div class="adm-input input" style="--text-align: right">
                    <div class="form-row">
                        <AppInput
                            v-model="editValue"
                            formType="number"
                            placeholder="(范围：350～950)"
                        >
                            <template #prefix>
                                <div class="form-prefix">芝麻分</div>
                            </template>
                        </AppInput>
                    </div>
                </div>
            </div>
            <AppButton class="global-btn" type="primary" @click="editSave">确定</AppButton>
            <p class="noValue" data-collect="noZhima" @click="saveData('无芝麻分')">我暂无芝麻分</p>
        </div>
    </div>

    <Popup
        :show="zhimatipsVisible"
        :z-index="2000"
        closeable
        position="bottom"
        @close="zhimatipsVisible = false"
    >
        <div class="zhimaPopupWrap">
            <div class="clole" style="position: absolute; right: 16px; top: -28px">
                <svg
                    class="antd-mobile-icon"
                    color="#fff"
                    font-size="24"
                    height="1em"
                    style="vertical-align: -0.125em"
                    viewBox="0 0 48 48"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                    <title>BE4E7D81-3958-4F2E-9956-E6071199885F@2x</title>
                    <g
                        id="CloseOutline-CloseOutline"
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                    >
                        <g id="CloseOutline-编组">
                            <rect
                                id="CloseOutline-矩形"
                                fill="#FFFFFF"
                                height="48"
                                opacity="0"
                                width="48"
                                x="0"
                                y="0"
                            ></rect>
                            <path
                                id="CloseOutline-路径"
                                d="M10.6085104,8.11754663 L24.1768397,21.8195031 L24.1768397,21.8195031 L37.7443031,8.1175556 C37.8194278,8.04168616 37.9217669,7.999 38.0285372,7.999 L41.1040268,7.999 C41.3249407,7.999 41.5040268,8.1780861 41.5040268,8.399 C41.5040268,8.50440471 41.4624226,8.60554929 41.3882578,8.68044752 L26.2773302,23.9408235 L26.2773302,23.9408235 L41.5021975,39.3175645 C41.65763,39.4745475 41.6563731,39.7278104 41.4993901,39.8832429 C41.4244929,39.9574004 41.3233534,39.999 41.2179546,39.999 L38.1434012,39.999 C38.0366291,39.999 37.9342885,39.9563124 37.8591634,39.8804408 L24.1768397,26.0621438 L24.1768397,26.0621438 L10.4936501,39.8804497 C10.4185257,39.9563159 10.3161889,39.999 10.2094212,39.999 L7.13584526,39.999 C6.91493136,39.999 6.73584526,39.8199139 6.73584526,39.599 C6.73584526,39.4936017 6.77744443,39.3924627 6.85160121,39.3175656 L22.0763492,23.9408235 L22.0763492,23.9408235 L6.96554081,8.68044639 C6.81010226,8.52346929 6.81134951,8.27020637 6.9683266,8.11476782 C7.04322474,8.04060377 7.14436883,7.999 7.24977299,7.999 L10.3242852,7.999 C10.4310511,7.999 10.5333863,8.04168267 10.6085104,8.11754663 Z"
                                fill="currentColor"
                                fill-rule="nonzero"
                            ></path>
                        </g>
                    </g>
                </svg>
            </div>
            <p class="title" data-collect="zhimaTips">查询“芝麻分”步骤</p>
            <img
                alt=""
                class="tutorial"
                src="https://hxwebpublic.oss-cn-hangzhou.aliyuncs.com/zmbg.png"
            />
        </div>
    </Popup>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import AppButton from "@/components/App/Button.vue";
import AppInput from "@/components/App/Input.vue";
import { Popup } from "vant";
import { useTheme } from "@/pages/home/<USER>/themeHook/useTheme";

export interface PropsType {
    form: any;
    modelValue: any;
    beforeSave?: any;
}

const theme = useTheme();
const props = withDefaults(defineProps<PropsType>(), {});
const emit = defineEmits<{
    (e: "update:modelValue", value: any): void;
    (e: "change", value: any): void;
}>();
const editValue = ref<any>();
const zhimatipsVisible = ref<any>(false);

// const checkInEdit = () => {
//     return props.modelValue !== undefined && props.options.filter((el: any) => el.value === props.modelValue).length === 0;
// }

const editSave = async () => {
    let f: boolean = true;
    if (props.beforeSave) {
        f = await props.beforeSave(props.form, editValue.value);
    }
    if (f) saveData(editValue.value);
};

const saveData = (value: any) => {
    emit("update:modelValue", value);
    emit("change", value);
};

onMounted(() => {
    // if (checkInEdit()) {
    //     const [item] = props.options.filter((el: any) => el.isEdit)
    //     const { editConfig } = props.form;
    //     if (item) {
    //         editData.value = editConfig
    //         editValue.value = props.modelValue
    //     }
    // }
});
</script>
<style lang="less" scoped>
.zhimaWrap {
    padding: 20px 16px;

    .title {
        margin-bottom: 10px;
        height: 24px;
        font-size: 18px;
        font-weight: 500;
        color: #222222;
        line-height: 24px;
    }

    .titleWrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .title {
            margin-bottom: 0;
        }

        .zhimaBtn {
            img {
                width: 13px;
                height: 15.5px;
                margin-right: 3px;
            }

            font-size: 13px;
            color: #222222;
            margin-right: -16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 123px;
            height: 35px;
            background: rgba(0, 116, 255, 0.1);
            border-radius: 40px 0px 0px 40px;

            .blue {
                color: #0075ff;
            }
        }
    }

    .noValue {
        margin-top: 8px;
        text-align: center;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 20px;
        padding-bottom: 32px;
    }
}

.zhimaPopupWrap {
    height: 462px;
    padding: 18px 16px;
    background-color: #fff;

    .title {
        margin-bottom: 19px;
        height: 25px;
        font-size: 18px;
        font-weight: 500;
        color: #222222;
        line-height: 25px;
    }

    img {
        width: 344px;
        height: 348px;
    }
}

.global-btn {
    background-color: v-bind("theme.authBtnBgc") !important;
}
</style>
