<template>
    <div class="radioContainer">
        <div
            class="tab"
            :class="item.value === activeTab ? 'active-tab' : ''"
            v-for="item in props.options"
            :key="item"
            @click="onChange(item.value)"
        >
            {{ item.label }}
        </div>
        <div class="edit" v-if="props.editConfig">
            <van-field
                v-model="customize"
                type="digit"
                :label="props.editConfig.label"
                :placeholder="props.editConfig.placeholder"
                @blur="onEdit"
            >
                <template #extra>
                    <span> {{ props.editConfig.unit }} </span>
                </template>
            </van-field>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, watch } from "vue";

interface Options {
    label: string;
    value: any;
    amount?: any;
}

interface CertificationConfig {
    modelValue: string | number;
    options?: Options[] | any[];
    // 标题
    label: string;
    // 类型
    type?: string;
    // 输入框提示文字
    placeholder?: string;
    // 子项
    children?: {
        keyword: string;
        label: string;
        placeholder: string;
        type: string;
        value: string;
        amount: number;
        rules: any;
    }[];
    // 排序
    sort: number;
    // 输入框配置
    editConfig?: {
        label: "自定义额度";
        unit: "万元";
        valueType: "number";
        placeholder: string;
    };
    // 单位
    unit?: string;
    keyword: string;
    group?: string;
    rules?: any[];

    [property: string]: any;
}
const props = defineProps<CertificationConfig>();
const emits = defineEmits<{
    (e: "update:modelValue", value: any): void;
    (e: "onChange"): void;
}>();
// 选项val
const activeTab = ref<string | number>("");
// 编辑框
const customize = ref<string | number>("");
const onChange = (val: string | number) => {
    customize.value = "";
    activeTab.value = val;
};

const onEdit = () => {
    activeTab.value = "";
};
watch(
    () => props.modelValue,
    (val) => {
        if (props.options?.some((element) => element.value === val)) {
            activeTab.value = val;
        } else {
            customize.value = val.toString().replace(props.unit ?? "", "");
        }
    },
    {
        immediate: true
    }
);
watch([activeTab, customize], ([activeTabWatch, customizeWatch]) => {
    let val = activeTabWatch || customizeWatch;
    emits("update:modelValue", val);
    emits("onChange");
});
</script>

<style scoped lang="less">
.radioContainer {
    display: flex;
    flex-wrap: wrap;
    margin-top: 8px;

    .tab {
        min-width: 55px;
        height: 30px;
        background: #f8f8f8;
        border-radius: 2px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 30px;
        text-align: center;
        margin: 0 8px 8px 0;
        padding: 0 16px;
        border: 1px solid transparent;
    }

    .active-tab {
        .tab();
        background: #e8ecff;
        border: 1px solid #929ee0;
    }

    .edit {
        width: 100%;

        .input-text {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 22px;
        }

        //van-cell van-field
        :deep(.van-cell) {
            //background-color: #f5f5f5ff;
            border-radius: 8px;
            border: 1px solid #eeeeee;
        }

        :deep(.van-field__label) {
            .input-text();
        }
    }
}
</style>
