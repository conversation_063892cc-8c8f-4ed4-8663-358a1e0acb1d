<template>
    <van-popup
        v-model:show="cityShow"
        position="bottom"
        closeable
        @clickOverlay="onClose"
        @clickCloseIcon="onClose"
    >
        <van-picker
            title="标题"
            :columns="props.options"
            :columnsFieldNames="customFieldName"
            class="myPicker"
            ref="myPricker"
        >
            <template #title>
                <div class="myPicker-box">
                    <div class="myPicker-title">
                        {{ props.label }}
                    </div>
                    <div class="myPicker-describe">请上下滑动选择</div>
                </div>
            </template>
            <template #confirm>
                <span></span>
            </template>
            <template #cancel>
                <span></span>
            </template>
            <template #columns-bottom>
                <div class="myPicker-AppButton">
                    <AppButton class="global-btn" type="primary" @click="onConfirm">确定</AppButton>
                </div>
            </template>
        </van-picker>
    </van-popup>
</template>
<script setup lang="ts">
import { ref } from "vue";
import AppButton from "@/components/App/Button.vue";
import { watch } from "vue";
import { nextTick } from "vue";

interface Props {
    modelValue: any;
    options: any;
    cityStatus: boolean;
    // 标题
    label: string;
    // 类型
    type?: string;
    // 输入框提示文字
    placeholder?: string;
    // 子项
    children?: {
        keyword: string;
        label: string;
        placeholder: string;
        type: string;
        value: string;
        amount: number;
        rules: any;
    }[];
    // 排序
    sort: number;
    // 输入框配置
    editConfig?: {
        label: "自定义额度";
        unit: "万元";
        valueType: "number";
        placeholder: string;
    };
    // 单位
    unit?: string;
    keyword: string;
    group?: string;
    rules?: any[];

    [property: string]: any;
}

const props = defineProps<Props>();
const emits = defineEmits<{
    (e: "update:modelValue", value: any): void;
    (e: "update:cityStatus", value: any): void;
    (e: "onChange"): void;
}>();

const cityShow = ref(false);
const myPricker = ref();
const customFieldName = {
    text: "text",
    value: "value",
    children: "children"
};

watch(
    () => props.cityStatus,
    (status) => {
        cityShow.value = status;
        if (status) {
            nextTick(() => {
                myPricker.value.setValues(props.modelValue.split("/"));
            });
        }
    }
);
const onConfirm = () => {
    const content = myPricker.value.getValues();
    emits("update:modelValue", content[0].value + "/" + content[1].value);
    emits("onChange");
    onClose();
};
const onClose = () => {
    emits("update:cityStatus", false);
};
</script>

<style scoped lang="less">
.myPicker {
    :deep(.van-picker-column__item--selected) {
        color: var(--color-primary-bg);
    }

    :deep(.van-picker__cancel) {
        width: 58px;
    }

    :deep(.van-picker__toolbar) {
        height: 60px;
    }

    .myPicker-img {
        width: 20px;
        height: 20px;
    }

    .myPicker-title {
        text-align: center;
        font-size: 16px;
    }

    .myPicker-describe {
        text-align: center;
        font-size: 12px;
        color: #00000035;
    }

    .myPicker-AppButton {
        padding: 16px 20px;
    }
}
</style>
