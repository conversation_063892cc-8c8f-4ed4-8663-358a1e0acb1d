<template>
    <div class="radioContainer">
        <van-field
            v-model="customize"
            :label="props.label"
            :placeholder="props.placeholder"
            @blur="onEdit"
        >
            <template #extra v-if="props.unit">
                <span> {{ props.unit }} </span>
            </template>
        </van-field>
        <div class="not" @click="onChange('无芝麻分')">我暂无芝麻分</div>
    </div>
</template>
<script setup lang="ts">
import { ref, watch } from "vue";

interface Options {
    label: string;
    value: any;
    amount?: any;
}

interface Props {
    modelValue: string;
    beforeSave?: (val: string, rules: any[]) => Promise<boolean>;
    options?: Options[] | any[];
    // 标题
    label: string;
    // 类型
    type?: string;
    // 输入框提示文字
    placeholder?: string;
    // 子项
    children?: {
        keyword: string;
        label: string;
        placeholder: string;
        type: string;
        value: string;
        amount: number;
        rules: any;
    }[];
    // 排序
    sort: number;
    // 输入框配置
    editConfig?: {
        label: "自定义额度";
        unit: "万元";
        valueType: "number";
        placeholder: string;
    };
    // 单位
    unit?: string;
    keyword: string;
    group?: string;
    rules?: any[];

    [property: string]: any;
}

const props = defineProps<Props>();
const emits = defineEmits<{
    (e: "update:modelValue", value: any): void;
    (e: "onChange"): void;
}>();
const customize = ref("");

const onChange = (val: string) => {
    customize.value = val;
    onEdit();
};

const onEdit = () => {
    emits("update:modelValue", customize.value);
    emits("onChange");
};
watch(
    () => props.modelValue,
    (val) => {
        onChange(val);
    },
    {
        immediate: true
    }
);
</script>

<style scoped lang="less">
.radioContainer {
    display: flex;
    flex-wrap: wrap;
    margin-top: 8px;

    .input-text {
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 22px;
    }

    :deep(.van-cell) {
        background-color: #f5f5f5ff;
        border-radius: 8px;
    }

    :deep(.van-field__label) {
        .input-text();
    }

    .not {
        font-size: 12px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #888888;
        line-height: 30px;
        text-align: center;
        height: 30px;
        width: 100%;
    }
}
</style>
