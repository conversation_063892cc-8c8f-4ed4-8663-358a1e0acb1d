<template>
    <!-- <div class="page">
        <div class="page_header">
            <slot name="pageHeader" />
        </div>
        <div class="page_scroll">
            <slot />
        </div>
        <div class="page_footer safe-area-padding-bottom">
            <slot name="pageFooter" />
        </div>
    </div> -->
</template>
<script lang="ts" setup></script>
<style lang="less" scoped>
.page {
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    //.page_scroll {
    //    overflow: hidden;
    //    flex: 1;
    //
    //    .page_footer {
    //    }
    //}
}
</style>
