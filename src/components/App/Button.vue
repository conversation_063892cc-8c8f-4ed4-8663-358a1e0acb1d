     <template>
    <div
        :class="props.type + ' ' + props.className"
        :disabled="disabled ? 'disabled' : undefined"
        class="app-button"
    >
        <slot></slot>
    </div>
</template>
<script lang="ts" setup>
// import { defineProps, withDefaults } from 'vue'
export interface Props {
    type?: "default" | "primary";
    disabled?: boolean;
    className?: string;
}

const props = withDefaults(defineProps<Props>(), {
    type: "default"
});
</script>
<style lang="less">
.app-button {
    text-align: center;
    min-height: 2.4em;
    height: 100%;
    transition: opacity 200ms;
    padding: 0 1em;
    display: flex;
    justify-content: center;
    align-items: center;

    &.default {
        color: #fff;
    }

    &.primary {
        color: var(--color-primary-text);
        background: var(--color-primary-bg);

        &:hover {
            opacity: 0.9;
        }
    }

    &[disabled],
    &.disabled {
        background: #eee !important;
        color: #666;
        pointer-events: none;
        cursor: not-allowed;
    }
}
</style>
