<template>
    <div :class="props.type" :disabled="disabled ? 'disabled' : undefined" class="app-input">
        <div class="app-input-wrap">
            <div class="app-input-wrap-prefix">
                <slot name="prefix"></slot>
            </div>
            <slot name="content">
                <input
                    :placeholder="placeholder"
                    :type="formType"
                    :value="props.modelValue"
                    v-bind="props.props"
                    @input="onChange"
            /></slot>
        </div>
        <div class="app-input-suffix">
            <slot name="suffix"></slot>
        </div>
    </div>
</template>
<script lang="ts" setup>
export interface Props {
    type?: "default" | "primary";
    disabled?: boolean;
    modelValue: any;
    placeholder?: string;
    formType?: "number" | "text";
    props?: any;
}

const props = withDefaults(defineProps<Props>(), {
    type: "default",
    placeholder: "",
    formType: "text",
    props: {}
});
const emit = defineEmits<{
    (e: "update:modelValue", value: any): void;
}>();
const onChange = (e: { target: { value: any } }) => {
    emit("update:modelValue", e.target.value);
};
</script>
<style lang="less">
.app-input {
    text-align: left;
    font-size: 12px;
    display: flex;
    flex-direction: row;

    .app-input-wrap {
        flex: 1;
        display: flex;
        flex-direction: row;

        input {
            flex: 1;
            height: 100%;
            box-sizing: border-box;
            padding: 0 12px;
            border: none;
            background-color: transparent;
            border-radius: 0;
            outline: none;
            width: 100%;

            &:focus,
            &:active {
                border: none;
                outline: none;
            }
        }
    }

    .app-input-wrap-prefix {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &[disabled] {
        background: #dddddd;
        pointer-events: none;
        cursor: not-allowed;
    }
}
</style>
