<template>
    <div>
        <div v-if="toutiaoChannel.includes(store.channel)">
            <Drag
                :boundary="{ top: 0, left: 0, bottom: 0, right: 0 }"
                :style="{ top: '10px', right: '0px' }"
                attract
                direction="y"
            >
                <div class="contact cur2">
                    <div
                        :class="toutiaoChannel.includes(store.channel) ? 'a' : 'b'"
                        class="contact_mark newChannel"
                    >
                        <a></a>
                    </div>
                </div>
            </Drag>
            <!-- 弹窗 -->
            <VanDialog
                v-model:show="showDialog"
                :showConfirmButton="false"
                width="289px"
                @confirm="onOkAgreement"
            >
                <div class="service">
                    <div
                        :class="toutiaoChannel.includes(store.channel) ? 'service-a' : 'service-b'"
                        class="service-top clearfix"
                    >
                        <div class="service-logo">浩瀚</div>
                        <div class="service-content">
                            是否拨打
                            <span> 浩瀚 </span>
                            客服电话?
                        </div>
                        <div class="service-iphone">
                            <span> ************ </span>
                        </div>
                    </div>
                    <div class="service-bottom">
                        <div class="service-dial" @click="onOkAgreement">拨打电话</div>
                        <div class="service-cancel" @click="showDialog = false">取消</div>
                    </div>
                </div>
            </VanDialog>
        </div>
        <div v-else>
            <Drag
                v-show="visible"
                :boundary="{ top: 0, left: 0, bottom: 0, right: 0 }"
                :style="{ top: '100px', right: '0px' }"
                attract
                direction="y"
            >
                <div :class="{ cur: show }" class="contact" @click="show = true">
                    <div class="contact_mark">
                        <a v-show="show" @click="showDialog = true"></a>
                        <!-- <a href="tel:4000272808" v-if="show"></a> -->
                    </div>
                </div>
            </Drag>
            <!-- 弹窗 -->
            <VanDialog
                v-model:show="showDialog"
                show-cancel-button
                title="温馨提示"
                @confirm="onOkAgreement"
            >
                <template #default>
                    <div class="detail-agreement pop">
                        <div>有任何问题请拨打客服电话</div>
                        <div>4000272808</div>
                    </div>
                </template>
                <template #footer>
                    <div class="contactFootter">
                        <van-button
                            class="contactFootter-btn"
                            round
                            type="success"
                            color="#CCCCCCFF"
                            @click="cancel"
                            >取消</van-button
                        >
                        <van-button
                            class="contactFootter-btn"
                            round
                            type="success"
                            color="#3C56E4FF"
                            @click="onOkAgreement"
                            >立即咨询</van-button
                        >
                    </div>
                </template>
            </VanDialog>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { Dialog } from "vant";
import "@nutui/nutui/dist/packages/drag/index.scss";
import { Drag } from "@nutui/nutui";
import { useRoute } from "vue-router";
import { useGlobalStore } from "@/stores/global";
import { useTheme } from "@/pages/home/<USER>/themeHook/useTheme";

const toutiaoChannel = ["toutiao", "bafang1", "bafang", "toutiaolm"];
const theme = useTheme();
const store = useGlobalStore();
const VanDialog = Dialog.Component;
const show = ref(false);
const timer = ref();
const showDialog = ref<boolean>(false);
watch(show, (f) => {
    if (f) {
        window.clearTimeout(timer.value);
        timer.value = window.setTimeout(() => {
            show.value = false;
        }, 2000);
    }
});

const route = useRoute();

const visible = computed(() => {
    const showArry: string[] = [
        "/auth",
        "/procedure/intention",
        "/procedure/product",
        "/procedure/recommend"
    ];
    return showArry.includes(route.path);
});

const onOkAgreement = () => {
    window.location.href = "tel:4000272808";
};

const cancel = () => {
    showDialog.value = false;
};
</script>
<style lang="less" scoped>
.contactFootter {
    display: flex;
    justify-content: space-between;
    padding: 8px 16px 24px 16px;
    .contactFootter-btn {
        width: 48%;
        height: 44px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #ffffff;
        line-height: 18px;
        text-align: right;
        font-style: normal;
    }
}
:deep(.van-dialog__header) {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: #000000;
    line-height: 25px;
    font-style: normal;
    margin-bottom: 21px;
}
.contact {
    width: 32px;

    .contact_mark {
        height: 38px;
        width: 96px;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: cover;
        position: relative;
        transition: all 300ms;

        a {
            display: block;
            width: 100%;
            height: 100%;
        }
    }

    &.cur {
        .contact_mark {
            left: -64px;
        }
    }

    .newChannel {
        width: 60px;
        height: 50px;
    }

    &.cur2 {
        .contact_mark {
            left: -28px;
        }
    }

    .a {
        background-image: url("@/assets/images/service1.png");
    }

    .b {
        background-image: url("@/assets/images/service2.png");
    }
}

.service {
    height: 296px;
    background: #ffffff;
    border-radius: 8px;

    .service-top {
        height: 174px;
        background: url("@/assets/images/serviceBgi.png") no-repeat,
            linear-gradient(180deg, #707cff 0%, #3441ff 100%);
        background-size: 289px 174px;
        border-radius: 8px 8px 0px 0px;
        font-size: 18px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        line-height: 25px;
        text-align: center;

        .service-logo {
            margin-top: 24px;
            margin-bottom: 19px;
        }

        .service-content {
            span {
                color: #ffcc73;
            }
        }

        .service-iphone {
            width: 163px;
            height: 40px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff20 100%);
            border-radius: 4px;
            margin: 0 auto;
            margin-top: 8px;

            span {
                line-height: 40px;
            }
        }
    }

    .service-a {
        background: url("@/assets/images/serviceBgi.png") no-repeat,
            linear-gradient(180deg, #fd8d41 0%, #f66f13 100%) !important;
        background-size: 289px 174px !important;
    }

    .service-b {
        background: url("@/assets/images/serviceBgi.png") no-repeat,
            linear-gradient(180deg, #707cff 0%, #3441ff 100%) !important;
        background-size: 289px 174px !important;
    }

    .service-bottom {
        .service-dial {
            width: 233px;
            height: 44px;
            background: v-bind("theme.serviceBtnColor");
            border-radius: 22px;
            text-align: center;
            line-height: 44px;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #ffffff;
            margin: 0 auto;
            margin-top: 20px;
        }

        .service-cancel {
            width: 233px;
            height: 44px;
            background: #fff;
            border-radius: 22px;
            text-align: center;
            line-height: 44px;
            margin: 0 auto;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #444544;
        }
    }
}

.detail-agreement {
    text-align: center;
    margin: 10px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    font-style: normal;
    & div:nth-child(2) {
        color: #3c56e4ff;
    }
}
</style>
