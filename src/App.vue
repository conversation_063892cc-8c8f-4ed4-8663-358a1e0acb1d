<template>
    <RouterView />
    <Contact />
    <BackTip />
</template>
<script lang="ts" setup>
import { RouterView } from "vue-router";
import { fillEvent } from "@/actions/collect";
import $nekyReport from "@/assets/js/collect";
import { onBeforeMount, onMounted } from "vue";
import { useGlobalStore } from "@/stores/global";
import { loadConversion } from "@/actions/conversion";
import { getAppConfig } from "@/apis/index";
import { getWH } from "@/utils/index";
import BackTip from "@/components/BackTip/BackTip.vue";
const store = useGlobalStore();
const isInIframe = () => {
    let r = false;

    try {
        if (self.frameElement && self.frameElement.tagName == "IFRAME") {
            r = true;
        } else if (window.frames.length != parent.frames.length) {
            r = true;
        } else if (self != top) {
            r = true;
        }
    } catch (error) {}
    return r;
};
onBeforeMount(async () => {
    $nekyReport.init({
        url: `/hx-server/api-server/eventTrack`, //采集地址，采用发送一张1x1的图片带上参数进行数据采集
        // isClick: true, //是否全量点击，监听整个页面所有的点击事件
        uid: store.uid, //采集用户的ID，默认为空，用户数据也可在后续的回调中带上
        channel: store.channel,
        clickAttr: {},
        version: "1.0.1"
    });
    $nekyReport.watchRead(() => {
        $nekyReport.dispatch(
            {
                init: "load",
                extra: navigator.userAgent
            },
            "appload"
        );
    });
    fillEvent($nekyReport);
    // console.log(getWH());
    if (isInIframe()) {
        $nekyReport.dispatch({ eid: "isInIframe", extra: JSON.stringify(getWH()) });
    }
    loadConversion();
    getAppConfig(store.channel).then((res) => {
        store.setPageConfig(res.data);
        store.initialInfo({
            isFlow: res?.data?.is_flow === 1
        });
    });
});
onMounted(() => {
    let apploading: any = document.getElementById("apploading");
    apploading.style.display = "none";
});
</script>
<style lang="less">
@import "@/assets/iconfont/iconfont.css";
// .apploading {
//     // display: none !important;
// }
</style>
