import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import App from "./App.vue";
import LayoutPage from "@/components/layout/page.vue";
import router from "./router";

import "@/assets/common.less";
import "vant/es/dialog/style";
import "vant/es/toast/style";
import "vant/es/notify/style";
import { Loading, Toast } from "vant";

const app = createApp(App);
app.use(createPinia().use(piniaPluginPersistedstate));
app.use(router);
app.use(Loading);
app.use(Toast);
// eslint-disable-next-line vue/component-definition-name-casing
app.component("LayoutPage", LayoutPage);
// app.component("Page", Page);
app.mount("#app");
