import { onMounted, ref } from "vue";
import useCookie from "./useCookie";

export default ({ interval = 60, codeKey = "loginCaptcha" }: any) => {
    const codeStartTime = useCookie(codeKey, 60);
    const codeTime = ref(
        codeStartTime.value
            ? Math.ceil((Number(codeStartTime.value) - new Date().getTime()) / 1000 + interval)
            : -1
    );
    const codeLoading = ref(false);
    const codeTimer = ref();
    //验证码状态
    const codeStatus = ref(true);
    //验证码倒计时
    const runCodeTime = (time: number) => {
        codeTime.value = time;
        codeTimer.value = setInterval(() => {
            codeTime.value -= 1;
            if (codeTime.value < 0) {
                clearInterval(codeTimer.value);
            }
        }, 1000);
    };
    //发送验证码
    const doSend = (fetchCode: any) => {
        if (codeTime.value >= 0) {
            return;
        }
        codeLoading.value = true;
        codeStatus.value = true;
        if (fetchCode) {
            fetchCode()
                .then((status) => {
                    if (status) {
                        codeStartTime.value = String(new Date().getTime());
                        runCodeTime(interval);
                    } else {
                        codeStatus.value = false;
                    }
                })
                .catch((e) => {
                    codeStatus.value = false;
                })
                .finally(() => {
                    codeLoading.value = false;
                });
        }
    };

    onMounted(() => {
        if (codeStartTime.value) {
            runCodeTime(
                Math.ceil((Number(codeStartTime.value) - new Date().getTime()) / 1000 + interval)
            );
        }
    });
    return {
        codeTime,
        codeLoading,
        codeStatus,
        doSend
    };
};
