import { creatUrl } from "@/utils/index";

export default (url: string, params: object, path?: boolean) => {
    const useAgreementUrl = creatUrl(url, params);

    const getAgreementContent = () => {
        return new Promise((res, rej) => {
            const index = useAgreementUrl.indexOf(".com");
            const _url = useAgreementUrl.substring(index).replace(".com", "");
            const xhr = new XMLHttpRequest();
            // 设置 HTTP 请求方法和 URL
            xhr.open("GET", path ? useAgreementUrl : _url, true);
            // 监听 readyState 和 status 的变化
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    // 在这里处理返回的 HTML 数据
                    res(xhr.responseText);
                }
            };
            // 发送请求
            xhr.send();
        });
    };

    return {
        useAgreementUrl,
        getAgreementContent
    };
};
