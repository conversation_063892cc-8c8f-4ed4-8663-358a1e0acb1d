import { onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Toast } from "vant";
import { getAgreement, getProdcutList, submitProduct } from "@/apis/serve/productList";
import { useGlobalStore } from "@/stores/global";
import { reportConversion } from "@/actions/conversion";
import $nekyReport from "@/assets/js/collect";
import { getPromote } from "@/apis/index";
import { getUrlData, sleep } from "@/utils/index";

export const useAuthorization = () => {
    const loadingData = ref<boolean>(false);
    const store = useGlobalStore();
    const router = useRouter();
    const route = useRoute();
    const selectItems = ref<any>([]);
    const data = ref<any>([]);
    const noticeVisible = ref<boolean>(false);
    const agreementData = ref<any>([]);
    // 是否默认勾选协议
    const switchAgreementData = ref<any>(false);
    const $nuxtLink = (path: string, options: any) => {
        router.push({
            path,
            ...options
        });
    };
    const dataState = ref<any>(false);
    const requestNum = ref<any>(0);
    const promoteData = ref<any>([]);
    const interceptVisible = ref<any>(false);
    const agreementContent = ref<any>("");
    const agreementUrl = ref<any>("");
    const changeSwitch = () => {
        switchAgreementData.value = !switchAgreementData.value;
    };
    const urlData = getUrlData();
    const orderID = ref(0);

    // 勾选状态需要排除的渠道
    const excludeChannel = [];
    // 多产品状态，默认勾选状态
    const allDefaultStatus = false;
    // 融多多展示协议状态
    const agreementShow = ref(false);
    // 融多多展示协议列表
    const agreementShowList = ref([]);
    //协议弹窗关闭倒计时
    const agreementShowTime = ref(3);
    // 确认协议弹窗
    const confirmAgreementPopup = ref(false);

    const judgeStr = (item: any, state: any = false) => {
        const list = data.value.list ?? [];
        const str1 = list.reduce((pre: any, element: any) => {
            if (element.id == item) {
                pre += element.name;
            }
            return pre;
        }, "");
        if (!state) {
            const str2 = list.reduce((pre: any, element: any) => {
                if (element.id == item) {
                    pre += element.partner_name;
                }
                return pre;
            }, "");
            return str1 + "(" + str2 + ")";
        }
        return str1;
    };
    // 单选框点击事件
    const doClickItem = (id: any) => {
        if (selectItems.value.includes(id)) {
            selectItems.value = selectItems.value.filter((kid: any) => kid !== id);
        } else {
            selectItems.value = [...selectItems.value, id];
        }
    };

    const onSubmit = async () => {
        if (data.value.code == 400) {
            return;
        }
        if (selectItems.value.length) {
            $nekyReport.dispatch({ eid: "authSubmitApply", extra: route.query._u });

            const submitToast = Toast.loading({
                duration: 0,
                message: "电子合同签署中..."
            });
            const commitInfoResult: any = await submitProduct(data.value.channel_id, {
                uuid: route.query._u,
                list: selectItems.value
            });

            await sleep(1200);
            submitToast.clear();

            if (commitInfoResult?.data) {
                const submitToastSuccess = Toast.success({
                    duration: 1000,
                    message: "签署成功！准备跳转..."
                });
                await sleep(1000);
                submitToastSuccess.clear();
            }

            if (commitInfoResult?.data) {
                if (urlData.return_url) {
                    $nekyReport.dispatch({ eid: "partnerAuth", extra: data.value?.url });
                    location.replace(urlData.return_url);
                    // 跳转兜底h5页面
                } else if (commitInfoResult?.data?.url) {
                    $nekyReport.dispatch({
                        eid: "partnerAuth",
                        extra: commitInfoResult?.data?.url
                    });
                    router.push({
                        path: "/authorization/partnerAuth",
                        query: { target: encodeURIComponent(commitInfoResult?.data?.url) },
                        replace: true
                    });
                    //接口返回回跳地址，则进行回跳
                } else if (commitInfoResult?.data?.redirect_url) {
                    // 有返回协议则展示协议3秒
                    if (commitInfoResult?.data?.protocols?.length) {
                        agreementShow.value = true;
                        agreementShowList.value = commitInfoResult?.data?.protocols;
                        const countdownId = setInterval(function () {
                            if (agreementShowTime.value > 0) {
                                agreementShowTime.value--;
                            } else {
                                clearInterval(countdownId);
                                location.href = commitInfoResult?.data?.redirect_url;
                                reportConversion("formSuccess");
                                $nekyReport.dispatch({
                                    eid: "authSubmitSuccess",
                                    extra: route.query._u
                                });
                            }
                        }, 1000);
                    } else {
                        location.href = commitInfoResult?.data?.redirect_url;
                        reportConversion("formSuccess");
                        $nekyReport.dispatch({
                            eid: "authSubmitSuccess",
                            extra: route.query._u
                        });
                    }
                    //融多多专属 判断接口返回是否有协议，有协议展示协议3秒再跳转
                } else if (commitInfoResult?.data?.protocols?.length) {
                    agreementShow.value = true;
                    agreementShowList.value = commitInfoResult?.data?.protocols;
                    const countdownId = setInterval(function () {
                        if (agreementShowTime.value > 0) {
                            agreementShowTime.value--;
                        } else {
                            clearInterval(countdownId);
                            // 销毁页面
                            $nuxtLink("/procedure/result", {
                                query: {
                                    orderId: orderID.value
                                },
                                replace: true
                            });
                            reportConversion("formSuccess");
                            $nekyReport.dispatch({
                                eid: "authSubmitSuccess",
                                extra: route.query._u
                            });
                        }
                    }, 1000);
                    // 常规流程
                } else {
                    // 销毁页面
                    $nuxtLink("/procedure/result", {
                        query: {
                            orderId: orderID.value,
                            agreement: encodeURIComponent(
                                JSON.stringify(commitInfoResult?.data?.protocols ?? [])
                            )
                        },
                        replace: true
                    });
                    reportConversion("formSuccess");
                    $nekyReport.dispatch({ eid: "authSubmitSuccess", extra: route.query._u });
                }
            } else {
                Toast.fail(commitInfoResult?.message);
                $nekyReport.dispatch({ eid: "authSubmitFiled", extra: route.query._u });
            }
            noticeVisible.value = false;
        }
    };
    const init = async () => {
        const res: any = await getProdcutList({ _u: route.query._u, match: route.query.match });
        requestNum.value++;
        store.channel = res.data.channel_id ?? "fdsfs";
        orderID.value = res.data.order_id ?? 0;
        data.value = res.data ?? {};
        // 超过10次不再请求
        if (requestNum.value == 10) {
            data.value = { list: [] };
            dataState.value = true;
            loadingData.value = true;
            return;
        }
        //接口有数据延迟，循环初始化数据
        if (!data.value.order_id) {
            setTimeout(init, 1000);
            loadingData.value = true;
            return;
        }
        if (data.value.url) {
            router.push({
                path: "/authorization/partnerAuth",
                query: { target: data.value?.url },
                replace: true
            });
            //获取协议
        } else if ((data.value.list ?? []).length) {
            $nekyReport.dispatch({ eid: "authProductMatchSuccess", extra: route.query._u });
            const agreementList = await getAgreement({
                _c: res.data.channel_id,
                order_id: res.data.order_id
            });
            agreementData.value = agreementList.data ?? [];
        } else {
            $nekyReport.dispatch({ eid: "authProductMatchFailed", extra: route.query._u });
        }

        const list = data.value.list ?? [];
        // 只有一个产品时默认勾选所有产品
        if (list.length == 1) {
            list.forEach((element: any) => {
                selectItems.value.push(element.id);
            });
            //     多个产品
        } else {
            if (allDefaultStatus && !excludeChannel.includes(urlData._c)) {
                list.forEach((element: any) => {
                    selectItems.value.push(element.id);
                });
            }
        }

        store.initialInfo({ token: data.value.token });
        //获取代抄
        promote();
        // 获取协议内容
        getAgreementContent();
        // 数据加载完成
        dataState.value = true;
        // 关闭loading
        loadingData.value = true;
    };

    // 监听是否弹窗提示用户订单失效
    watch(
        () => dataState.value,
        (newDate) => {
            if (newDate) {
                if (data.value.code == 400) {
                    Toast({
                        message: data.value.msg,
                        className: "toast"
                    });
                }
            }
        },
        {
            deep: true,
            immediate: true
        }
    );
    onMounted(() => {
        $nekyReport.dispatch({ eid: "authPageLoad", extra: route.query._u });
        init();
        // 如果支持 popstate 一般移动端都支持了
        if (window.history && window.history.pushState) {
            // 往历史记录里面添加一条新的当前页面的url
            history.pushState(null, null, document.URL);
            // 给 popstate 绑定一个方法 监听页面刷新
            window.addEventListener("popstate", interceptBalck, false); //false阻止默认事件
        }
    });
    /**
     * 代抄（获取推荐产品）
     */
    const promote = () => {
        getPromote().then((res) => {
            promoteData.value = res.data;
        });
    };
    const onJudge = () => {
        if (switchAgreementData.value) {
            //同意协议埋点
            $nekyReport.dispatch({ eid: "authAgreement" });
            noticeVisible.value = true;
        }
    };

    const interceptBalck = () => {
        interceptVisible.value = true;
    };
    const interceptConfirm = () => {
        if (window.history && window.history.pushState) {
            // 手机点击了物理返回 然后又选择了继续 这时需要在添加一条记录
            history.pushState(null, null, document.URL);
            interceptVisible.value = false;
        }
    };
    const interceptCancel = () => {
        window.history.back();
        interceptVisible.value = false;
    };

    const getAgreementContent = () => {
        if (!Object.keys(agreementData.value).length) {
            return;
        }
        const content = agreementData.value[agreementData.value.length - 1] ?? {};
        const index = content.value.indexOf(".com");
        const url = content.value.substring(index).replace(".com", "");
        agreementUrl.value = url;
    };

    return {
        data,
        judgeStr,
        onJudge,
        agreementData,
        onSubmit,
        selectItems,
        $nuxtLink,
        switchAgreementData,
        changeSwitch,
        dataState,
        doClickItem,
        promoteData,
        interceptVisible,
        interceptConfirm,
        interceptCancel,
        agreementContent,
        agreementUrl,
        confirmAgreementPopup,
        // 融多多专属
        agreementShow,
        agreementShowTime,
        agreementShowList
        //----
    };
};
