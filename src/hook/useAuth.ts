import { nextTick, ref } from "vue";
import {
    check2rz,
    commitInfo,
    commitProducts,
    getCertificationConfig,
    getUserInfo
} from "@/apis/index";
import { analysisData, getIdCardInfo } from "@/utils/procedure";
import { useGlobalStore } from "@/stores/global";
import { useRouter } from "vue-router";
import { Toast } from "vant";
import SchemaValidator, { type Rules } from "async-validator";
import { reportConversion } from "@/actions/conversion";
import { toutiaoChannel } from "@/common/channel";

const store = useGlobalStore();
const includesChannal = ["gdtxh", "toutiao"];
export const useAuth = () => {
    const router = useRouter();
    // 获取对data数据进行处理得到可以用来初始化的数据（传入data中的certificationConfig）
    const form = ref<any[]>([]);
    const formKey = ref<number>(0);
    // 缓存key名
    const localStorageKey = `authData_${store.uid}`;
    // 指针
    const pointer = ref(-1);
    const idCard = ref({
        idno: "",
        name: "",
        sex: "",
        birthday: ""
    });
    // 动态class底色状态
    let numComplete = ref<boolean>(false);
    const loading = ref<boolean>(false);
    const result = ref<boolean>(false);
    // 初始化状态
    const initState = ref<boolean>(false);

    // 初始化表单数据
    const getFormData = async () => {
        // 挂载前获取浏览器缓存数据
        const { data = [] } = await getCertificationConfig("auth,complete,will");
        let certificationConfig = analysisData(
            data
                .filter((item: any) => item.key !== "certification")
                .sort((a: any, b: any) => (b.sort ?? 0) - (a.sort ?? 0))
                .map((item: any, index: number) => {
                    return {
                        ...item,
                        value: undefined,
                        index
                    };
                })
        );
        if (toutiaoChannel.includes(store.channel)) {
            certificationConfig = certificationConfig.concat(a);
        }

        let d: any = {};
        try {
            d = JSON.parse(localStorage.getItem(localStorageKey) ?? "{}");
        } catch (error) {}
        let n = -1;

        const cityInfo: any = await getUserInfo();
        const cityInfoData = cityInfo.data?.working_city
            ? cityInfo.data?.working_city
            : "天津市/天津市";

        form.value = certificationConfig.map((item: any, index: number) => {
            item.value = d[item.key];
            if (!item.value && n < 0) {
                n = index;
            }
            if (item.key === "working_city") {
                item.defaultValue = cityInfoData;
            }
            return item;
        });
        if (!Object.keys(d)) {
            initState.value = true;
        }

        // 如果浏览器缓存里都有每一条数据都有值，把动态class的状态改为true
        pointer.value = n;
        idCard.value = {
            idno: d.idno,
            name: d.name,
            sex: d.sex,
            birthday: d.birthday
        };
        nextTick(
            () => (numComplete.value = form.value.every((element) => element.value != undefined))
        );

        formKey.value = 1;
    };
    const $nuxtLink = (path: string, options: any) => {
        router.push({
            path,
            ...options
        });
    };
    //  把数据保存到localStorage里面
    const saveData = () => {
        const data: any = {
            ...idCard.value
        };
        // 遍历form数组，重新给对象赋值
        form.value.forEach((item: any) => {
            data[item.key] = item.value;
        });
        // 保存到localStorage中
        localStorage.setItem(localStorageKey, JSON.stringify(data));
    };
    const dataChange = () => {
        saveData();
        showNext();
    };
    // 指针
    const showNext = () => {
        let n = -1;

        form.value.map((item, index) => {
            // 如果没有输入值不关闭页面
            if (!item.value && n < 0) {
                n = index;
            }
        });
        pointer.value = n;
    };

    // 身份信息校验
    const rules_idno: Rules = {
        idno: {
            type: "string",
            required: true,
            pattern:
                /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|X)$/,
            message: "身份证不符合规范"
        }
    };
    const rules: Rules = {
        name: {
            type: "string",
            required: true,
            pattern:
                /^[\u00B7\u3007\u3400-\u4DBF\u4E00-\u9FFF\uE000-\uF8FF\uD840-\uD8C0\uDC00-\uDFFF\uF900-\uFAFF]+$/,
            message: "请输入正确的姓名"
        }
    };
    const rules_age_sex: Rules = {
        sex: { type: "string", required: true, message: "请选择性别" },
        birthday: { type: "string", required: true, message: "请输入正确的生日" }
    };
    const rules_flow: Rules = {
        ...rules,
        ...rules_age_sex
    };

    const rules_full: Rules = {
        ...rules,
        ...rules_idno
    };

    // 校验填写信息
    const checkIdCard = async (): Promise<boolean> => {
        idCard.value.name = (idCard.value.name ?? "").replace(/\s*/g, "");
        idCard.value.idno = (idCard.value.idno ?? "").replace(/\s*/g, "");
        const { idno, name, sex, birthday } = idCard.value;
        const validator = new SchemaValidator(store.isFlow ? rules_flow : rules_full);
        const result: any = await validator
            .validate(store.isFlow ? { name, sex, birthday } : { idno, name })
            .catch(({ errors, fields }) => {
                Toast.fail(errors[0]?.message ?? "校验失败！");
                return false;
            });
        return result;
    };

    // 数据提交
    const submitForm = async () => {
        let age: any = parseInt(idCard.value.birthday);

        if (
            (!/^[1-9]\d{1,2}$/.test(idCard.value.birthday) || age < 20 || age > 100) &&
            store.isFlow
        ) {
            Toast.fail("年龄不合法");
            return;
        }

        if (loading.value) {
            return;
        }
        saveData();
        const IdCardData = await checkIdCard();
        if (!IdCardData) {
            return;
        }
        loading.value = true;
        Toast.allowMultiple();
        const toastIdCard = Toast.loading({
            duration: 0,
            message: "身份信息校验中...."
        });
        const { name, idno } = idCard.value;
        const check2rzResult: any = await check2rz(store.isFlow ? { name } : { name, idno });

        if (check2rzResult.error) {
            toastIdCard.message = "身份信息核验失败!";
            setTimeout(() => {
                toastIdCard.clear();
                loading.value = false;
            }, 500);
            return;
        }
        let other = getIdCardInfo(IdCardData);
        toastIdCard.message = "数据提交信息中";
        let authArr: { key: string; value: string }[] = [];
        form.value.forEach((item: any) => {
            authArr.push({
                key: item.key,
                value: item.unit && item.key !== "zhima" ? `${item.value}${item.unit}` : item.value
            });
        });

        const commitInfoResult: any = await commitInfo({ other, auth: authArr });
        if (commitInfoResult.error) {
            toastIdCard.message = commitInfoResult.message;
        } else {
            toastIdCard.message = "提交成功！";
            const ret: any = commitInfoResult?.data;
            store.orderId = ret.orderId;
            // 指定渠道功能，撞库成功的产品全部推送
            if (store.isFlow) {
                if (ret.list.length !== 0) {
                    const commitInfoResult: any = await commitProducts({
                        orderId: ret.orderId,
                        list: ret.list.slice(0, 1),
                        name: name,
                        idno: idno
                    });
                    // console.log('进件成功');

                    reportConversion("formSuccess");
                    if (commitInfoResult?.data) {
                        console.log(commitInfoResult);

                        // console.log("有data");
                    } else {
                        Toast.fail(commitInfoResult?.message);
                    }
                }
                // 判断是否是当前页面展示的渠道
                if (toutiaoChannel.includes(store.channel)) {
                    result.value = true;
                } else {
                    // 销毁页面
                    $nuxtLink("/procedure/isFlowResult", { replace: true });
                }
            } else if (ret.list.length === 0) {
                // 正常匹配产品逻辑
                $nuxtLink("/procedure/result", { replace: true, query: { status: "failure" } });
            } else if (ret.list.length === 1) {
                $nuxtLink("/procedure/product", {
                    query: { id: ret.list[0], orderId: ret.orderId },
                    replace: true
                });
            } else {
                $nuxtLink("/procedure/recommend", {
                    query: { orderId: ret.orderId },
                    replace: true
                });
            }
        }

        toastIdCard.clear();
        loading.value = false;
    };

    return {
        // 表单数据
        form,
        // 初始化表单数据
        getFormData,
        submitForm,
        idCard,
        result,
        initState
    };
};

const a = [
    {
        icon: "https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/auth1.png",
        label: "姓名",
        innerTitle: "",
        formTitle: "您的工作城市是？",
        required: true,
        type: "input",
        rules: [{ required: true, message: "请先选收入信息" }],
        key: "name",
        sort: 93,
        group: {
            title: "基本信息",
            sort: 2
        },
        placeholder: "请输入姓名"
    },
    {
        icon: "https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/auth1.png",
        label: "年龄",
        innerTitle: "",
        formTitle: "您的工作城市是？",
        required: true,
        type: "input",
        rules: [{ required: true, message: "请先选收入信息" }],
        key: "birthday",
        sort: 93,
        group: {
            title: "基本信息",
            sort: 3
        },
        placeholder: "请输入年龄"
    },
    {
        icon: "https://kxoss.oss-cn-hangzhou.aliyuncs.com/assets/auth1.png",
        label: "性别",
        innerTitle: "",
        formTitle: "您的工作城市是？",
        required: true,
        type: "select",
        rules: [{ required: true, message: "请先选收入信息" }],
        key: "sex",
        sort: 93,
        options: [
            {
                label: "男",
                value: "男"
            },
            {
                label: "女",
                value: "女"
            }
        ],
        value: "",
        group: {
            title: "基本信息",
            sort: 4
        }
    }
];
