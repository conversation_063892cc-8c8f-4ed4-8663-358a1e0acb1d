import { cityOptions } from "./areaData";
import md5 from "js-md5";

// 传入一个数组，对数据进行处理
export const analysisData = (list: any[]): any[] => {
    return list.map((item: any) => {
        if (item.type === "rangePicker" && item.range) {
            const options = [];
            const [min, max] = item.range;
            const ranegStep = Number(item.ranegStep ?? "1");
            for (let i = min; i <= max; i += ranegStep) {
                const text = i + (i === min ? "及以下" : i === max ? "及以上" : "");
                options.push({
                    text,
                    value: text
                });
            }
            item.options = options;
            item.type = "picker";
        } else if (item.type === "city") {
            item.options = cityOptions;
            item.type = "picker";
        }

        return item;
    });
};

export const validate = (value: any, rules: any[]) => {
    return new Promise((resolve, reject) => {
        (rules ?? []).forEach((item: any) => {
            if (item.required) {
                if (typeof value === "undefined") {
                    // throw new error(item.message)
                    reject(item.message);
                    throw Error(item.message);
                }
            } else if (item.pattern) {
                const a = new RegExp(item.pattern);
                if (!a.test(value)) {
                    reject(item.message);
                    throw Error(item.message);
                }
            } else if (item.range?.length === 2) {
                const [min, max] = item.range;
                if (value < min) {
                    reject(item.message[0] ?? "小于最小");
                    throw Error(item.message[0]);
                } else if (value > max) {
                    reject(item.message[1] ?? "大于最大");
                    throw Error(item.message[1]);
                }
            }
        });
        resolve(true);
    });
};

export const getIdCardInfo = ({ name, idno, ...data }: any) => {
    if (idno) {
        const area = idno.substring(0, 6);
        const sex = parseInt(idno.substring(16, 17)) % 2 === 1 ? "男" : "女";
        const birthday =
            idno.substring(6, 10) + "/" + idno.substring(10, 12) + "/" + idno.substring(12, 14);
        const birthDate = new Date(birthday);
        const nowDateTime = new Date();
        let age = nowDateTime.getFullYear() - birthDate.getFullYear();
        if (
            nowDateTime.getMonth() < birthDate.getMonth() ||
            (nowDateTime.getMonth() == birthDate.getMonth() &&
                nowDateTime.getDate() < birthDate.getDate())
        ) {
            age--;
        }

        return {
            name: name.substring(0, 1) + "**",
            name_md5: md5(name),
            idno: md5(idno),
            age: String(age),
            area,
            sex,
            birthday
        };
    }

    // const birthDate = new Date(data.birthday);
    // const nowDateTime = new Date();
    // let age = nowDateTime.getFullYear() - birthDate.getFullYear();
    // if (
    //     nowDateTime.getMonth() < birthDate.getMonth() ||
    //     (nowDateTime.getMonth() == birthDate.getMonth() &&
    //         nowDateTime.getDate() < birthDate.getDate())
    // ) {
    //     age--;
    // }
    return {
        name: name.substring(0, 1) + "**",
        name_md5: md5(name),
        sex: data.sex,
        // birthday: data.birthday,
        age: String(data.birthday)
    };
};

// export const encrypt = (word: string, key: string) => {
//     const encryptor = new JSEncrypt();
//     encryptor.setPublicKey(key);
//     return encryptor.encrypt(word);
// };
