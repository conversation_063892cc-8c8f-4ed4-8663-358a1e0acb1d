// import JSEncrypt from "jsencrypt";

export const sessionStorage = {
    setItem(k: string, value: any, expires = 60 * 60 * 24) {
        const key = this.getKey(k);
        let date = +new Date() + 1000 * expires;
        let params = {
            value,
            date
        };
        window.localStorage.setItem(key, JSON.stringify(params));
    },
    getItem(k: string) {
        const key = this.getKey(k);
        try {
            const v: any = window.localStorage.getItem(key);
            let value: any = JSON.parse(v);
            if (value.date < Date.now()) {
                this.removeItem(key);
                return null;
            }
            return value.value;
        } catch (error) {}
        return null;
    },
    removeItem(key: string) {
        window.localStorage.removeItem(key);
    },
    getKey: (key: string) => `session_${key}`
};

export const getUrlData = function (u?: string) {
    // 分解地址参数
    let url: any = u || window.location.href;
    url = url.split("?");
    if (url.length <= 1) {
        return {};
    }
    const search = url[1].split("#")[0];
    const params: any = {};
    if (search) {
        const tmp = search.split("&");
        for (let i = 0; i < tmp.length; i++) {
            const query = tmp[i].match(/=([^&]+)/);
            const kv = tmp[i].split("=");
            if (query) {
                params[kv[0]] = decodeURIComponent(query[1]);
            }
        }
    }
    return params;
};
const jointUrl = function (p: any) {
    let str = "";
    for (const i in p) {
        if (typeof p[i] !== "undefined") str += `&${i}=${p[i]}`;
    }
    return str;
};

export const creatUrl = function (h: any, p: any) {
    const _h = h.split("#");
    let _hash = "";
    const d = Object.assign(getUrlData(h), p);
    if (_h.length >= 2) {
        _hash = `#${_h[1]}`;
        h = _h[0];
    }
    const u = h.split("?");
    return `${u[0]}?${jointUrl(d)}${_hash}`.replace("?&", "?").replace(/\?*$/g, "");
};
// title
export const setTitle = (t: string): void => {
    const doc: any = document;
    doc.title = t;
    const ifr = doc.createElement("iframe");
    ifr.setAttribute("style", "width:0;height:0;border: none;outline: none;display: none;");
    ifr.src = "about:blank";
    ifr.onload = () => {
        setTimeout(function () {
            doc.body.removeChild(ifr);
        }, 100);
    };
    doc.body.appendChild(ifr);
};
// 加密方法
// export const $encrypt = (word: string, key: string) => {
//     const encryptor = new JSEncrypt();
//     encryptor.setPublicKey(key);
//     return encryptor.encrypt(word);
// };
export const $encrypt = (word: string, key: string) => {
    return word + key;
};
export const sleep = (time: number) => {
    return new Promise((resolve) => setTimeout(resolve, time));
};

interface wh {
    w: number;
    h: number;
}

export const getWH = () => {
    try {
        const box = document.createElement("div");
        box.setAttribute(
            "style",
            "position: fixed;width: 100%;top: 0;bottom: 0;left: 0;z-index: -1;"
        );
        document.body.appendChild(box);
        const a: wh = {
            w: box.clientWidth,
            h: box.clientHeight
        };
        const rwh = box.getBoundingClientRect();

        document.body.removeChild(box);
        return {
            ...a,
            bottom: rwh.bottom,
            height: rwh.height,
            left: rwh.left,
            right: rwh.right,
            top: rwh.top,
            width: rwh.width,
            x: rwh.x,
            y: rwh.y
        };
    } catch (e) {}
    return {
        w: document.body.clientWidth,
        h: document.body.clientHeight
    };
};
