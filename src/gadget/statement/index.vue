<template>
    <template v-if="store.channel === 'quannei'">
        <div class="page-tips">
            <div class="tips-title">提供贷款咨询服务<br />贷款有风险，借款需谨慎;</div>
            <div class="tips-desc">
                借款额度、放款时间以实际审批为准<br />
                公积金相关资料仅作为贷款服务验资证明<br />
                放款路径与公积金账户无关<br />
                综合年化利率6%-24%,具体以审批结果为准<br />
                资金来源:南京市天下捷融互联网科技小额贷款有限公司<br />
            </div>
            <div class="tips-copyright">苏ICP备19068009号-1</div>
        </div>
    </template>
    <template v-else-if="store.channel === 'tttr'">
        <div class="page-tips">
            <div class="tips-title">
                投资有风险，借贷需谨慎。<br />请根据个人能力合理借款、避免逾期。
            </div>
            <div class="tips-copyright">
                重庆两江新区通融小额贷款有限公司 ｜ 渝ICP备17004598号-2
            </div>
            <div class="tips-desc">
                声明：本平台为借款需求发布平台，贷款资金将由重庆两江新区通融小额贷款有限公司及合作银行、持牌放贷机构等提供；具体放款利率、放款金额和利息视个人情况及匹配的产品综合而定。本平台不向在校大学生及未成年提供服务；所有贷款在未成功放款前绝不收取任何费用。
            </div>
        </div>
    </template>
    <template v-else-if="toutiaoChannel.includes(store.channel)">
        <div class="page-tips-toutiao">
            <span>贷款有风险，借贷需谨慎</span><br />
            <span>请根据个人能力合理贷款、理性消费，避免逾期</span><br />
            <span>贷款额度、放款时间以实际审批结果为准</span><br />
            <span>贷款资金将由银行等持牌放贷机构提供</span><br />
            <span>年化利率区间6%-24%</span><br />
            <div>
                <p>湖南浩瀚汇通互联网小额贷款有限公司</p>
                <p>湘ICP备18003428号</p>
            </div>
        </div>
    </template>
    <template v-else-if="zuoXingHuangChannel.includes(store.channel)">
        <div class="page-tips">
            <div class="tips-title">
                投资有风险，借贷需谨慎。<br />请根据个人能力合理借款、避免逾期。<br />
            </div>
            <div class="tips-copyright">
                左新凰(河北)商务服务有限公司温州分公司 ｜ 浙ICP备2024055277号-1
            </div>
            <div class="tips-desc">
                声明：本平台为借款需求发布平台，贷款资金将由合作银行、持牌放贷机构等提供；具体放款利率、放款金额和利息视个人情况及匹配的产品综合而定。本平台不向在校大学生及未成年提供服务；所有贷款在未成功放款前绝不收取任何费用。
            </div>
        </div>
    </template>
    <template v-else-if="store.channel === 'gdtxh'">
        <div class="page-tips-toutiao">
            <span>温馨提示：贷款额度、放款时间等以实际审批为准</span><br />
            <span>贷款有风险，借贷需谨慎</span><br />
            <span>请根据个人能力合理贷款、理性消费，避免逾期</span><br />
            <span>公积金相关资料仅作为贷款服务验资证明</span><br />
            <span>放款路径与公积金账号无关</span><br />
            <span>资金来源：湖南浩瀚汇通互联网小额贷款有限公司</span><br />
            <span>综合年化利率6%-24%（单利）</span><br />
            <span>页面信息及服务由湖南浩瀚汇通互联网小额贷款有限公司提供</span><br />
            <div>
                <p>湖南浩瀚汇通互联网小额贷款有限公司</p>
                <p>湘ICP备18003428号</p>
            </div>
        </div>
    </template>
    <template v-else>
        <div class="page-tips">
            <div class="tips-title">
                投资有风险，借贷需谨慎。<br />请根据个人能力合理借款、避免逾期。<br />
                综合年化利率6%-24%（单利）
            </div>
            <div class="tips-copyright">杭州纳赫兹科技有限公司 ｜ 
            <a href="https://beian.miit.gov.cn" target="_blank">浙ICP备2023042878号-1</a>
            </div>
            <div class="tips-desc">
                声明：本平台为借款需求发布平台，贷款资金将由合作银行、持牌放贷机构等提供；具体放款利率、放款金额和利息视个人情况及匹配的产品综合而定。本平台不向在校大学生及未成年提供服务；所有贷款在未成功放款前绝不收取任何费用。
            </div>
        </div>
    </template>
</template>
<script lang="ts" setup>
import { useGlobalStore } from "@/stores/global";
import { toutiaoChannel, zuoXingHuangChannel } from "@/common/channel";

const store = useGlobalStore();
</script>
<style lang="less" scoped>
.page-tips {
    background-color: inherit;
    padding: 20px;
    text-align: center;
    margin-top: 100px;

    .tips-title {
        padding-top: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #888888;
        line-height: 18px;
    }

    .tips-copyright {
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #888888;
        line-height: 17px;
        margin: 16px 0;
    }

    .tips-desc {
        font-size: 10px;
        font-weight: 400;
        color: #a9a9a9;
        line-height: 16px;
        padding-bottom: 32px;
    }
}

.page-tips-toutiao {
    background-color: #fff;
    text-align: center;
    margin-bottom: 20px;
    font-size: 9px;

    span {
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        // line-height: 18px;
    }

    > span {
        color: #cbad6f;
    }

    div {
        margin-top: 8px;

        p {
            font-size: 11px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 11px;
        }
    }
}
</style>
