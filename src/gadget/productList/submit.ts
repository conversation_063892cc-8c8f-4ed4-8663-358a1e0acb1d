import { commitProducts } from "@/apis";
import { reportConversion } from "@/actions/conversion";
import { Toast } from "vant";
import router from "@/router/index";
import { sleep } from "@/utils/index";

export const submitH5Commit = async (payload: any) => {
    const submitToast = Toast.loading({
        duration: 0,
        message: "申请中..."
    });
    const commitInfoResult: any = await commitProducts(payload);
    await sleep(1200);
    submitToast.clear();

    if (commitInfoResult?.data) {
        const submitToastSuccess = Toast.success({
            duration: 1000,
            message: "申请成功！准备跳转..."
        });
        await sleep(1000);
        submitToastSuccess.clear();
    }

    if (commitInfoResult?.data) {
        console.log(`2 -->`, 2);
        if (commitInfoResult.data?.url) {
            console.log(`2 -->`, 2.2);
            window.location.href = decodeURIComponent(commitInfoResult.data?.url);
        } else {
            console.log(`3 -->`, 3);
            router.push({
                path: "/procedure/result",
                query: { orderId: payload.orderId },
                replace: true
            });
        }
        reportConversion("formSuccess");
    } else {
        Toast.fail(commitInfoResult?.message);
    }
};
