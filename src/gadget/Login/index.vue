<template>
    <template v-if="!state.token">
        <div class="form-row">
            <AppInput v-model="form.phone" placeholder="请输入手机号">
                <template #prefix>
                    <div class="form-icon">
                        <div class="icon-phone"></div>
                    </div>
                </template>
            </AppInput>
        </div>
        <div class="form-row">
            <AppInput v-model="form.code" formType="number" placeholder="请输入验证码">
                <template #prefix>
                    <div class="form-icon">
                        <div class="icon-code"></div>
                    </div>
                </template>
                <template #suffix>
                    <AppButton
                        :data-collect="codeStatus ? 'sendCaptcha' : 'reSend'"
                        font-size="12px"
                        style="width: 100px; font-size: 14px"
                        type="primary"
                        @click="fetchCaptcha"
                    >
                        <template v-if="!codeLoading">
                            <template v-if="codeTime < 0"
                                >{{ codeStatus ? "获取验证码" : "重新获取" }}
                            </template>
                            <template v-else>{{ codeTime }}S</template>
                        </template>
                    </AppButton>
                </template>
            </AppInput>
        </div>
    </template>
    <slot
        :doLogin="doLogin"
        :loginCheck="loginCheck"
        :loginStatus="!!state.token"
        name="login"
    ></slot>
</template>
<script lang="ts" setup>
import { defineProps, ref } from "vue";
import { useRouter } from "vue-router";
import useCaptcha from "@/hook/useCaptcha";
import AppInput from "@/components/App/Input.vue";
import AppButton from "@/components/App/Button.vue";
import { useGlobalStore } from "@/stores/global";
import { reportConversion } from "@/actions/conversion";
import { fetchLogin, getCaptcha } from "@/apis/index";

import { Toast } from "vant";

import $nekyReport from "@/actions/collect";

const props = defineProps<{
    beforeLogin: any;
}>();

const router = useRouter();

const state = useGlobalStore();
const { doSend, codeTime, codeStatus, codeLoading } = useCaptcha({});
const form = ref({
    code: "",
    phone: ""
});
const checkPhone = (): boolean => {
    form.value.phone = form.value.phone.replace(/\s*/g, "");
    const phone = form.value.phone;
    try {
        if (!/^1\d{10}$/.test(phone)) {
            const msg = "手机号格式错误";
            Toast.fail(msg);
            throw { error: msg };
        }
        return true;
    } catch (err: any) {
        return false;
    }
};
const checkCode = (): boolean => {
    form.value.code = form.value.code.replace(/\s*/g, "");
    let code = form.value.code;
    if (code && code.length) {
        form.value = {
            ...form.value,
            code: code.slice(0, 4)
        };
        code = form.value.code;
    }
    try {
        if (!/^\d{4,6}$/.test(code)) {
            const msg = "验证码格式错误";
            Toast.fail(msg);
            throw { error: msg };
        }
        return true;
    } catch (err: any) {
        return false;
    }
};

const loginCheck = () => {
    return checkPhone() && checkCode();
};
//发送验证码点击事件
const fetchCaptcha = async () => {
    if (!checkPhone()) return false;
    //封装的hook，发送送验证码函数
    doSend(async () => {
        const { error, message }: any = await getCaptcha({
            phone: form.value.phone
        });
        if (error) {
            $nekyReport.dispatch({ eid: "sendCaptchaFailed" });
            Toast.fail(message ?? "验证码发送失败！");
            return false;
            // throw (message);
        }
        $nekyReport.dispatch({ eid: "sendCaptchaSucess" });
        Toast.success("验证码已发送，请注意查收！");
        return true;
    });
};

const goNext = () => {
    router.push({ path: "/auth" });
};
//submit
const doLogin = async () => {
    if (state.token) {
        if (props.beforeLogin) {
            const f = await props.beforeLogin().catch(() => false);
            if (!f) return;
        }
        goNext();
        return;
    }
    if (loginCheck()) {
        if (props.beforeLogin) {
            const f = await props.beforeLogin().catch(() => false);
            if (!f) return;
        }
        const loginToast = Toast.loading({ message: "登录中", duration: 0 });
        const { data, error, message }: any = await fetchLogin({
            phone: form.value.phone,
            code: form.value.code,
            channel_id: state.channel
        });
        loginToast.clear();
        if (error) {
            // loginToast.message = message ?? "登录失败"
            Toast.fail(message ?? "登录失败");
            $nekyReport.dispatch({ eid: "loginFailed", extra: navigator.userAgent });
            return false;
        }
        $nekyReport.setData({
            uid: data.user_id,
            extra: navigator.userAgent
        });
        state.initialInfo({ token: data.token, uid: data.user_id });
        //
        if (data.register) {
            reportConversion("register");
        }

        $nekyReport.dispatch({ eid: "loginSucess" });
        reportConversion("loginSucess");

        goNext();
        // store.dispatch('indexStore/initialInfo', { payload: { token: data.token, uid: data.user_id } })
    }
};
</script>
<style lang="less" scoped>
.icon-phone,
.icon-code {
    height: 20px;
    width: 20px;
    background-repeat: no-repeat;
    background-size: cover;
}

.icon-phone {
    background-image: url("@/assets/images/phone.png");
}

.icon-code {
    background-image: url("@/assets/images/code.png");
}
</style>
