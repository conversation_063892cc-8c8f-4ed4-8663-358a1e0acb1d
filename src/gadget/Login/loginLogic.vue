<template>
    <slot
        :doLogin="doLogin"
        :loginCheck="loginCheck"
        :loginStatus="!!state.token"
        name="login"
    ></slot>

    <slot
        name="main"
        v-bind="{
            form,
            // 发送验证码点击事件
            fetchCaptcha,
            //登录事件
            doLogin,
            //验证码倒计时
            codeTime,
            //验证码状态
            codeStatus,
            //验证码是否发送成功
            codeLoading,
            //协议勾选状态
            switchAgreement,
            // 改变协议勾选专状态
            changeSwitch,
            //协议
            agreement,
            //校验手机号和协议是否勾选
            checkPhoneAndAgreement,
            //校验手机号是否合法
            phoneState,
            loginStatus
        }"
    ></slot>
</template>
<script lang="ts" setup>
import { computed, onBeforeMount, ref, watch } from "vue";
import { useRouter } from "vue-router";
import useCaptcha from "@/hook/useCaptcha";
import { useGlobalStore } from "@/stores/global";
import { reportConversion } from "@/actions/conversion";
import { fetchLogin, getCaptcha, getRecommend, oldUserMatchProduct } from "@/apis/index";
import { Toast } from "vant";
import $nekyReport from "@/actions/collect";
import { toutiaoChannel } from "@/common/channel";
import { sessionStorage } from "@/utils/index";
import { submitH5Commit } from "../productList/submit";

const router = useRouter();
const state = useGlobalStore();
const { doSend, codeTime, codeStatus, codeLoading } = useCaptcha({});
const form = ref({
    code: "",
    phone: "",
    name: ""
});
const switchAgreement = ref<any>(true);
// 协议
const agreement = ref<any>({});
const loginStatus = computed(() => {
    return !state.token;
});
const props = withDefaults(
    defineProps<{
        agreementState?: boolean;
    }>(),
    {
        agreementState: true
    }
);

const changeSwitch = () => {
    switchAgreement.value = !switchAgreement.value;
};
const phoneState = computed(() => {
    return form.value.phone.length >= 11;
});
// 检验手机号
const checkPhone = (): boolean => {
    form.value.phone = form.value.phone.replace(/\s*/g, "");
    const phone = form.value.phone;
    try {
        if (!/^1\d{10}$/.test(phone)) {
            const msg = "手机号格式错误";
            Toast.fail(msg);
            throw { error: msg };
        }
        return true;
    } catch (err: any) {
        return false;
    }
};

// 检验姓名
const checkName = (): boolean => {
    // 如果 pageConfig.idcard 不等于 1，则跳过姓名验证
    if (state.pageConfig?.idcard !== 1) {
        return true;
    }

    const name = form.value.name.trim();
    try {
        if (!name) {
            const msg = "请输入姓名";
            Toast.fail(msg);
            throw { error: msg };
        }
        if (name.length < 2) {
            const msg = "姓名至少2个字符";
            Toast.fail(msg);
            throw { error: msg };
        }
        return true;
    } catch (err: any) {
        return false;
    }
};

// 检验手机号、姓名和协议
const checkPhoneAndAgreement = () => {
    if (!(checkPhone() && checkName() && switchAgreement.value)) {
        Toast.fail("请填写完整信息并勾选协议");
    }
    return checkPhone() && checkName() && switchAgreement.value;
};

// 校验验证码
const checkCode = (): boolean => {
    //跳过验证码
    return true;
    form.value.code = form.value.code.replace(/\s*/g, "");
    let code = form.value.code;
    if (code && code.length) {
        form.value = {
            ...form.value,
            code: code.slice(0, 4)
        };
        code = form.value.code;
    }
    try {
        if (!/^\d{4,6}$/.test(code)) {
            const msg = "验证码格式错误";
            Toast.fail(msg);
            throw { error: msg };
        }
        return true;
    } catch (err: any) {
        return false;
    }
};
// 检查是否可以登录
const loginCheck = () => {
    return checkPhone() && checkName() && checkCode();
};
//发送验证码点击事件
const fetchCaptcha = async () => {
    if (!checkPhone()) return false;
    //封装的hook，发送送验证码函数
    doSend(async () => {
        const { error, message }: any = await getCaptcha({
            phone: form.value.phone
        });
        if (error) {
            $nekyReport.dispatch({ eid: "sendCaptchaFailed" });
            Toast.fail(message ?? "验证码发送失败！");
            return false;
            // throw (message);
        }
        $nekyReport.dispatch({ eid: "sendCaptchaSucess" });
        Toast.success("验证码已发送，请注意查收！");
        return true;
    });
};

// 是否是老用户
const isOldUser = ref(false);
const oldUserInfo: any = ref({});

console.log(`1.0 -->`, "1.0");

async function oldUserInfoMatch() {
    let status = await getOldUserStatus();
    let info = localStorage.getItem("oldUserInfo");
    console.log(`info -->`, info);

    if (status) {
        oldUserInfo.value = JSON.parse(info || "{}");
        const localStorageKey = `authData_${state.uid}`;
        localStorage.setItem(
            localStorageKey,
            JSON.stringify({
                idCard: { name: oldUserInfo.value?.name, idno: oldUserInfo.value?.idno }
            })
        );
        const { data } = await getRecommend(oldUserInfo.value?.orderId);
        localStorage.setItem("recommendList", JSON.stringify(data));
        state.recommendList = data;
        await submitH5Commit({
            orderId: oldUserInfo.value?.orderId,
            list: data?.map((item: any) => item.product_id),
            name: oldUserInfo.value?.name,
            idno: oldUserInfo.value?.idno
        });
    }
    return;

    if (!loginStatus.value && (!info || info === "{}")) {
        console.log(`1 -->`, 111);
        let res = await oldUserMatchProduct();
        if (res?.data?.list?.length > 0) {
            oldUserInfo.value = res?.data;
            isOldUser.value = true;
            oldUserInfoMatch();
        } else {
            oldUserInfo.value = {};
            isOldUser.value = false;
        }
        localStorage.setItem("oldUserInfo", JSON.stringify(oldUserInfo.value));
        return;
    }

    if (info && info !== "{}" && info !== "null") {
        oldUserInfo.value = JSON.parse(info || "{}");
        const localStorageKey = `authData_${state.uid}`;
        localStorage.setItem(
            localStorageKey,
            JSON.stringify({
                idCard: { name: oldUserInfo.value?.name, idno: oldUserInfo.value?.idno }
            })
        );
        const { data } = await getRecommend(oldUserInfo.value?.orderId);
        localStorage.setItem("recommendList", JSON.stringify(data));
        state.recommendList = data;
        await submitH5Commit({
            orderId: oldUserInfo.value?.orderId,
            list: data?.map((item: any) => item.product_id),
            name: oldUserInfo.value?.name,
            idno: oldUserInfo.value?.idno
        });
    }
}

oldUserInfoMatch();

// 登录成功跳转
const goNext = async () => {
    savePhone();

    if (isOldUser.value || oldUserInfo.value?.list?.length > 0) {
        console.log(`1.1 -->`, 1.1);
        oldUserInfoMatch();
        return;
    }

    console.log(`1.6 -->`, 1.6);

    if (toutiaoChannel.includes(state.channel)) {
        router.push({ path: "/auth/gdtxh" });
        return;
    }
    router.push({ path: "/auth" });
};
// 缓存手机号和姓名,保存一分钟
const savePhone = () => {
    if (form.value.phone) {
        sessionStorage.setItem("phone", form.value.phone, 60);
    }
    // 只有当 pageConfig.idcard 等于 1 时才保存姓名
    if (state.pageConfig?.idcard === 1 && form.value.name) {
        sessionStorage.setItem("name", form.value.name, 60);
    }
};
// 反显手机号和姓名
const restorePhone = () => {
    const phone = sessionStorage.getItem("phone") ?? "";
    if (phone) {
        form.value.phone = phone;
    }
    // 只有当 pageConfig.idcard 等于 1 时才恢复姓名
    if (state.pageConfig?.idcard === 1) {
        const name = sessionStorage.getItem("name") ?? "";
        if (name) {
            form.value.name = name;
        }
    }
};

//submit
const doLogin = async () => {
    if (!loginStatus.value) {
        goNext();
        return;
    }

    if (loginCheck()) {
        if (!switchAgreement.value) {
            Toast.fail("请勾选协议");
            return;
        }
        const loginToast = Toast.loading({ message: "登录中", duration: 0 });
        const loginParams: any = {
            phone: form.value.phone,
            code: form.value.code,
            channel_id: state.channel
        };

        // 只有当 pageConfig.idcard 等于 1 时才添加 name 字段
        if (state.pageConfig?.idcard === 1) {
            loginParams.name = form.value.name;
        }

        const { data, error, message }: any = await fetchLogin(loginParams);
        loginToast.clear();
        if (error) {
            Toast.fail(message ?? "登录失败");
            $nekyReport.dispatch({ eid: "loginFailed", extra: navigator.userAgent });
            return false;
        }
        $nekyReport.setData({
            uid: data.user_id,
            extra: navigator.userAgent
        });

        state.initialInfo({ token: data.token, uid: data.user_id });

        // 判断老用户
        await getOldUserStatus();

        //
        if (data.register) {
            reportConversion("register");
        }
        $nekyReport.dispatch({ eid: "loginSucess" });
        reportConversion("loginSucess");

        goNext();
    }
};

// 判断是否是老用户
async function getOldUserStatus() {
    let res = await oldUserMatchProduct();
    if (res?.data?.list?.length > 0) {
        oldUserInfo.value = res?.data;
        isOldUser.value = true;
    } else {
        oldUserInfo.value = {};
        isOldUser.value = false;
    }
    localStorage.setItem("oldUserInfo", JSON.stringify(oldUserInfo.value));
    return isOldUser.value;
}

onBeforeMount(() => {
    agreement.value = [
        {
            key: "《隐私政策》",
            value: "https://pro.nahezihz.com/agreement/xynT7TnsSFV"
        },
        {
            key: "《用户服务协议》",
            value: "https://pro.nahezihz.com/agreement/xyu9rxWkqQL"
        },
        {
            key: "《个人信息授权及产品风险告知》",
            value: "https://pro.nahezihz.com/agreement/xyXfeKaqbU5"
        }
    ];
    // });
    switchAgreement.value = props.agreementState;
    restorePhone();
});
</script>

<style lang="less" scoped></style>
