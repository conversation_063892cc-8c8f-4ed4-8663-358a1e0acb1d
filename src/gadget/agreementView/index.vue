<template>
    <div>
        <slot name="header" v-bind="{ agreementUrl }" />
        <div class="agreementContent">
            <van-loading v-if="!agreementContent" :vertical="true" type="spinner" />
            <div class="agreementContentbox" v-html="agreementContent"></div>
        </div>
        <slot name="footer" />
    </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import useAgreement from "@/hook/useAgreement";
import { getUrlData } from "@/utils/index";

const props = defineProps<{
    data: any;
    path?: boolean;
    height?: string;
    background?: string;
}>();

const agreementContent = ref<any>("");
const agreementUrl = ref<any>("");
const theme = {
    height: props.height ?? "146px",
    background: props.background ?? "#f3f3f3ff"
};

const loadAgreement = () => {
    if (props.data?.url) {
        const url = props.data.url;
        const { useAgreementUrl, getAgreementContent } = useAgreement(
            url,
            {
                name: "",
                idno: "",
                uid: "",
                ...getUrlData(url),
                ...(props.data.query ?? {})
            },
            props.path
        );
        agreementUrl.value = useAgreementUrl;
        getAgreementContent().then((value) => {
            agreementContent.value = value;
        });
    }
};

watch(() => props.data, loadAgreement);
onMounted(loadAgreement);
</script>
<style lang="less" scoped>
.agreementContent {
    // height: 129px;
    height: v-bind("theme.height");
    background: v-bind("theme.background");
    // background: linear-gradient(180deg, #e0e0e0 0%, #fff 100%);
    border-radius: 8px;
    padding: 10px 8px;
    margin-top: 12px;
    border: 1px solid #f3f5f7;

    :deep(.van-loading--vertical) {
        justify-content: center;
        height: 129px;
    }

    .agreementContentbox {
        overflow-y: scroll;
        height: 100%;

        :deep(span) {
            background: inherit !important;
            font-size: 12px !important;
            font-family: PingFangSC-Regular, PingFang SC !important;
            font-weight: 400 !important;
            color: #666666 !important;
            line-height: 20px !important;
            overflow-wrap: break-word;
        }

        :deep(.container) {
            background: inherit;
            border: none;
            padding: 0 10px;
            font-size: 12px !important;
            font-family: PingFangSC-Regular, PingFang SC !important;
            font-weight: 400 !important;
            color: #666666 !important;
            line-height: 20px !important;

            span {
                background: inherit !important;
                font-size: 12px !important;
                font-family: PingFangSC-Regular, PingFang SC !important;
                color: #666666 !important;
                line-height: 20px !important;
            }
        }
        // 设置滚动条的宽度
        &::-webkit-scrollbar {
            width: 2px;
            height: 92px;
            // background: #d8d8d8ff;
            // -webkit-border-radius: 5px;
            // -moz-border-radius: 5px;
            border-radius: 5px;
            // background: #d8d8d8;
        }
        // 设置滚动条滑块
        &::-webkit-scrollbar-thumb {
            // background-color: rgba(0, 0, 0, 0.5);
            // background-clip: padding-box;
            // -webkit-border-radius: 5px;
            // -moz-border-radius: 5px;
            // border-radius: 5px;
            // min-height: 28px;
            width: 2px;
            height: 38px;
            background: #adadad;
            border-radius: 1px;
        }
        // 设置滚动条轨道(好像无效设置-webkit-scrollbar就可以了)
        &::-webkit-scrollbar-thumb:hover {
            // background-color: rgba(0, 0, 0, 0.5);
            // -webkit-border-radius: 5px;
            // -moz-border-radius: 5px;
            // border-radius: 5px;
            // width: 2px;
            // height: 92px;
            // background: #d8d8d8;
            // border-radius: 1px;
        }
    }
}
</style>
