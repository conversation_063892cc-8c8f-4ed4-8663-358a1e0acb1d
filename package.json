{"name": "h5_vue", "version": "0.0.0", "scripts": {"up": "node upload.js", "dev": "vite --host --mode dev", "pro": "vite --host  --mode prod", "build": "run-p type-check build-only", "prod": "vite build&&node upload.js", "preview": "vite preview --port 4173", "build-only": "vite build", "build:dev": "vite build  --mode dev", "build:pro": "vite build --mode prod", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@nutui/nutui": "^3.2.3", "@vitejs/plugin-legacy": "^2.2.0", "ali-oss-publish": "^0.4.0", "async-validator": "^4.2.5", "axios": "^1.1.3", "js-md5": "^0.7.3", "jsencrypt": "^3.2.1", "less": "^4.1.3", "moment": "^2.29.4", "pinia": "^2.0.21", "pinia-plugin-persistedstate": "^3.2.1", "terser": "^5.24.0", "vant": "^3.6.4", "vue": "^3.2.38", "vue-request": "^2.0.0-rc.4", "vue-router": "^4.1.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@types/js-md5": "^0.4.3", "@types/node": "^16.11.56", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^3.0.3", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/tsconfig": "^0.1.3", "babel-eslint": "^10.1.0", "eslint": "^8.22.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.3.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.4", "qrcode": "^1.5.4", "typescript": "~4.7.4", "unplugin-vue-components": "^0.22.8", "vite": "^3.0.9", "vue-tsc": "^0.40.7"}}