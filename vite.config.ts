import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import legacy from "@vitejs/plugin-legacy";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "unplugin-vue-components/resolvers"; // https://vitejs.dev/config/

// https://vitejs.dev/config/
// const assetsUrl = "/static/";
// // const assetsUrl = "https://ajresources.oss-cn-hangzhou.aliyuncs.com/static/";
const assetsUrl =
    "//nhz-static-resource.oss-cn-hangzhou.aliyuncs.com/brandnew/font-end/pisces_h5/static/";
export default defineConfig(({ command }) => ({
    // base: '/h5',
    base: command === "serve" ? `/` : assetsUrl,
    plugins: [
        vue(),
        vueJsx(),
        Components({
            resolvers: [VantResolver()]
        }),
        legacy({
            targets: ["defaults", "not IE 11"]
        })
        // legacy({
        //     targets: ["chrome 52"],
        //     additionalLegacyPolyfills: ["regenerator-runtime/runtime"],
        //     renderLegacyChunks: true,
        //     polyfills: [
        //         "es.symbol",
        //         "es.array.filter",
        //         "es.promise",
        //         "es.promise.finally",
        //         "es/map",
        //         "es/set",
        //         "es.array.for-each",
        //         "es.object.define-properties",
        //         "es.object.define-property",
        //         "es.object.get-own-property-descriptor",
        //         "es.object.get-own-property-descriptors",
        //         "es.object.keys",
        //         "es.object.to-string",
        //         "web.dom-collections.for-each",
        //         "esnext.global-this",
        //         "esnext.string.match-all",
        //     ],
        // }),
    ],
    resolve: {
        alias: {
            "@": fileURLToPath(new URL("./src", import.meta.url))
        }
    },
    server: {
        port: 3000,
        proxy: {
            "/hx-server/api-server": {
                // target: "http://127.0.0.1:6100",
                // target: "https://www.hzkxgba.com",
                target: "https://dev.nahezihz.com",
                // target: "https://g.hhhtfin.com",
                changeOrigin: true
            },
            "/agreement": {
                // target: "http://127.0.0.1:6100",
                // target: "https://g.hhhtfin.com",
                target: "https://dev.nahezihz.com",
                changeOrigin: true,
                secure: false
                // pathRewrite:{    // 重写路径
                //     '^/test':''
                // }
            },
            "/ag": {
                // target: "http://127.0.0.1:6100",
                // target: "https://g.hhhtfin.com",
                target: "https://dev.nahezihz.com",
                changeOrigin: true,
                secure: false
                // pathRewrite:{    // 重写路径
                //     '^/test':''
                // }
            },
            "/hx-server": {
                // target: "https://www.hzkxgba.com",
                target: "https://dev.nahezihz.com",
                // target: "https://g.hhhtfin.com",
                changeOrigin: true
            },
            "/logo": {
                target: "https://nhz-kxoss.oss-cn-hangzhou.aliyuncs.com",
                changeOrigin: true
            }
        }
    }
}));
