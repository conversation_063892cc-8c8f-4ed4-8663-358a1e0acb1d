// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    BackTip: typeof import('./src/components/BackTip/BackTip.vue')['default']
    Button: typeof import('./src/components/App/Button.vue')['default']
    City: typeof import('./src/components/Auth/City.vue')['default']
    Contact: typeof import('./src/components/Contact/index.vue')['default']
    Input: typeof import('./src/components/App/Input.vue')['default']
    Page: typeof import('./src/components/layout/page.vue')['default']
    Picker: typeof import('./src/components/gdtAuth/picker.vue')['default']
    Radio: typeof import("./src/components/Auth/Radio.vue")["default"]
    RetainDialog: typeof import('./src/components/common/RetainDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Select: typeof import('./src/components/Auth/Select.vue')['default']
    Select2: typeof import('./src/components/Auth/select2.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanArea: typeof import('vant/es')['Area']
    VanButton: typeof import('vant/es')['Button']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCollapse: typeof import('vant/es')['Collapse']
    VanCollapseItem: typeof import('vant/es')['CollapseItem']
    VanDatetimePicker: typeof import("vant/es")["DatetimePicker"]
    VanDialog: typeof import('vant/es')['Dialog']
    VanEmpty: typeof import("vant/es")["Empty"]
    VanField: typeof import('vant/es')['Field']
    VanIcon: typeof import('vant/es')['Icon']
    VanLoading: typeof import('vant/es')['Loading']
    VanOverlay: typeof import("vant/es")["Overlay"]
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VueCuont: typeof import('./src/components/vueCuont/index.vue')['default']
    Zhima: typeof import('./src/components/gdtAuth/zhima.vue')['default']
    ZhiMa: typeof import('./src/components/Auth/ZhiMa.vue')['default']
    Zhima2: typeof import('./src/components/Auth/zhima2.vue')['default']
  }
}
