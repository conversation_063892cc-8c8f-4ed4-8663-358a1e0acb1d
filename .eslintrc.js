module.exports = {
    // 指定 ESLint 配置文件的根目录
    root: true, // 环境配置
    env: {
        browser: true, // 浏览器环境
        node: true, // Node.js环境
        es6: true // 启用ES6全局变量
    }, // 解析器配置
    parser: "vue-eslint-parser", //指定直接传递给解析器上的 parse() 或 parseForESLint() 方法的其他选项的对象。
    parserOptions: {
        parser: "@typescript-eslint/parser", // ECMAScript版本
        ecmaVersion: 2020, // 模块类型（'script' 或 'module'）
        sourceType: "module",
        jsxPragma: "React",
        ecmaFeatures: {
            // 启用JSX支持
            jsx: true, // 启用tSX支持
            tsx: true
        }
    }, // 扩展配置
    extends: [
        "plugin:vue/vue3-recommended",
        "plugin:@typescript-eslint/recommended",
        "prettier",
        "plugin:prettier/recommended"
    ], // 规则配置
    rules: {
        "@typescript-eslint/ban-ts-ignore": "off",
        "@typescript-eslint/explicit-function-return-type": "off", //允许使用 any 类型。
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/no-empty-function": "off",
        "vue/component-name-in-template-casing": ["error", "PascalCase"],
        "vue/component-definition-name-casing": ["error", "kebab-case"],
        "no-use-before-define": "off",
        "@typescript-eslint/no-use-before-define": "off", //允许使用 TypeScript 的注释语法。
        "@typescript-eslint/ban-ts-comment": "off",
        "@typescript-eslint/ban-types": "off", //允许使用非空断言操作符（!）。
        "@typescript-eslint/no-non-null-assertion": "off",
        "@typescript-eslint/explicit-module-boundary-types": "off",
        "@typescript-eslint/no-namespace": "off", //禁止未使用的变量。
        "@typescript-eslint/no-unused-vars": [
            "error",
            {
                argsIgnorePattern: "^h$",
                varsIgnorePattern: "^h$"
            }
        ],
        "no-unused-vars": "off",
        "space-before-function-paren": "off",
        "vue/attributes-order": "off",
        "vue/one-component-per-file": "off",
        "vue/html-closing-bracket-newline": "off",
        "vue/max-attributes-per-line": "off",
        "vue/multiline-html-element-content-newline": "off",
        "vue/multi-word-component-names": "off",
        "vue/singleline-html-element-content-newline": "off",
        "vue/attribute-hyphenation": "off",
        "vue/html-self-closing": "off",
        "vue/require-default-prop": "off",
        "vue/v-on-event-hyphenation": "off",
        "@typescript-eslint/no-unused-vars": "off"
    }, // 文件过滤器
    ignorePatterns: ["node_modules/", "dist/"]
    // 插件配置
    // plugins: ['plugin-name'], // 配置要使用的插件
    // 全局变量配置
    // globals: {
    //     // 可以添加项目中使用的全局变量，避免ESLint报错
    //     globalVar: 'readonly',
    // },
    // 在使用了 eslint-disable 注释后报告未使用的禁用指示
    // reportUnusedDisableDirectives: true,
};
