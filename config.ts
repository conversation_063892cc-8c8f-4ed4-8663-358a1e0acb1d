// const host = window.location.hostname;
// console.log("host", host);

// let isWebHistory = false;
// let url = "https://g.hhhtfin.com/";
// const item = [
//     {
//         hosts: ["hzkxgba"],
//         url: "https://www.hzkxgba.com",
//         WebHistory: true,
//     },
//     {
//         hosts: ["hhhtfin", "sq.csdk", "aijiangkeji"],
//         url: window.location.origin,
//         WebHistory: true,
//     },

//     {
//         hosts: ["192.168", "127.0.0.1"],
//         url: "/",
//         WebHistory: true,
//     },
// ]
//     .filter((item) => {
//         return item.hosts.some((key) => host.indexOf(key) >= 0);
//     })
//     .pop();
// if (item) {
//     isWebHistory = item.WebHistory;
//     console.log(item);
    
//     url = item.url;
// }
let url =import.meta.env.VITE_BASE_URL;
let isWebHistory =true;
export const appHost = url;
export const webHistoryType = isWebHistory;
