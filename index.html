<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, shrink-to-fit=no, maximum-scale=1, user-scalable=no, viewport-fit=cover"
        />
        <meta name="theme-color" content="#000000" />
        <meta content="yes" name="apple-mobile-web-app-capable" />
        <meta content="yes" name="fullscreen" />
        <meta content="true" name="x5-fullscreen" />
        <link rel="icon" href="">
        <!-- <link rel="shortcut icon" type="image/x-icon" href="/public/favicon.ico" /> -->
        <title></title>
        <script type="module">
            import { postBack } from "@/common/channel";
            import { getUrlData } from "@/utils/index";
            let urlData=getUrlData();
            if (postBack.includes(urlData._c)) {
                !(function (g, d, t, e, v, n, s) {
                if (g.gdt) return;
                v = g.gdt = function () {
                    v.tk ? v.tk.apply(v, arguments) : v.queue.push(arguments);
                };
                v.sv = "1.0";
                v.bt = 0;
                v.queue = [];
                n = d.createElement(t);
                n.async = !0;
                n.src = e;
                s = d.getElementsByTagName(t)[0];
                s.parentNode.insertBefore(n, s);
            })(
                window,
                document,
                "script",
                "//qzonestyle.gtimg.cn/qzone/biz/gdt/dmp/user-action/gdtevent.min.js"
            );

            gdt("init", "1111915880");
            gdt("init", "26950130");
            gdt("track", "PAGE_VIEW");
            }
        </script>
    </head>
    <style>
        .apploading {
            position: fixed;
            top: 35%;
            left: 50%;
            margin-left: -15px;
            color: #333;
        }
        @keyframes myLoading {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(356deg);
            }
        }
        .apploading-img {
            width: 30px;
            height: 30px;
            animation: myLoading 1200ms linear infinite;
        }
        /* button {
            animation: throttle 2s step-end forwards;
        } */
        /* .throttle {
            animation: throttle 500ms step-end forwards;
        }
        .throttle:active {
            animation: none;
        } */
        /* button:active {
            animation: none;
        } */
        @keyframes throttle {
            from {
                pointer-events: none;
            }
            to {
                pointer-events: auto;
            }
        }
    </style>
    <body>
        <div id="app"></div>
        <div class="apploading" id="apploading">
            <div class="apploading-logoBox">
                <img class="apploading-img" src="./src/assets/images/Loading.png" alt="" />
                <!-- <div style="margin-top: 30px">Loading...</div> -->
            </div>
        </div>
        <script type="module" src="/src/main.ts"></script>
    </body>
</html>
